package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitSource;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.repository.DirectDebitGenerationPlanRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import org.joda.time.DateTime;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class PaymentExpiryService {
    private static final Logger logger = Logger.getLogger(PaymentExpiryService.class.getName());

    @Autowired
    private MessagingService messagingService;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    @Autowired
    private DirectDebitGenerationPlanService directDebitGenerationPlanService;

    public void SendFirstMessage(ContractPaymentTerm cpt) throws JsonProcessingException {
        Contract contract = cpt.getContract();
        logger.info("Payment expiry start send sms clientId: " + contract.getClient().getId());

        Map<String, String> parameters = new HashMap<>();
        Map<String, AppAction> cta = new HashMap<>();
        flowProcessorMessagingService.fillExpiryPaymentParameter(cpt, parameters);
        flowProcessorMessagingService.fillExpiryPaymentContext(cpt, parameters, cta);

        String contentTemplate = contract.isMaidCc()
            ? CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString()
            : MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString();

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                contract.getEntityType(),
                TemplateUtil.getTemplate(contentTemplate));
        }

    public void SendSecondSMS(
            Contract contract,
            Double monthly_fee_of_nationality,
            String ExpiryDate) throws JsonProcessingException {

        logger.info("Payment expiry start send second message for client: " + contract.getClient().getId());

        DDMessagingToDoRepository ddMessagingToDoRepository = Setup.getRepository(DDMessagingToDoRepository.class);
        Client client = contract.getClient();

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contract.getActiveContractPaymentTerm());
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "PaymentExpiryFlow");
        }});
        String signLink = Setup.getApplicationContext().getBean(Utils.class)
                .getSingDDLink(signDDMap);

        Map<String, String> parameters = new HashMap<>();
        parameters.put("ExpiryDate", ExpiryDate);
        parameters.put("link_send_dd_details", signLink);
        parameters.put("monthly_fee_of_nationality", String.valueOf(monthly_fee_of_nationality.intValue()));
        parameters.put("greetings", contract.isMaidCc() ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
        parameters.put("link_send_dd_details_click_here", "@link6@");

        Map<String, AppAction> cta = new HashMap<>();
        AppAction a = new AppAction();
        a.setText("click here");
        a.setType(AppActionType.LINK);
        a.setFunctionType(FunctionType.WEB_SERVICE);
        a.setNavigationType(NavigationType.WEB);
        a.setHyperlink(signLink);
        a.setAppRouteName("");
        a.setAppRouteArguments(new HashMap<String, String>(){{
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});
        cta.put("link6", a);

        Template notificationTemplate = TemplateUtil.getTemplate(contract.isMaidCc() ?
                CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                contract.getEntityType(),
            notificationTemplate);

        for (int index = 1; index <= 3; index++) {
            java.sql.Date SendDate = java.sql.Date.valueOf(new LocalDate().plusDays(3 * index).toString("yyyy-MM-dd"));
            DDMessagingToDo dDMessagingToDo = new DDMessagingToDo();
            dDMessagingToDo.setAccountName(client.getAccountName());
            dDMessagingToDo.setSendToClient(true);
            dDMessagingToDo.setClientId(client.getId());
            dDMessagingToDo.setType(DDMessagingService.DdMessagingMethod.FORCE_SMS.toString());
            dDMessagingToDo.setEvent(DDMessagingType.ExpiryPayment);
            dDMessagingToDo.setClientName(client.getName());
            dDMessagingToDo.setClientPhoneNumber(client.getNormalizedMobileNumber());
            dDMessagingToDo.setContractUuid(contract.getUuid());
            dDMessagingToDo.setSendTime(Time.valueOf(LocalTime.now()));
            dDMessagingToDo.setSendDate(SendDate);
            dDMessagingToDo.setClientTemplateName(notificationTemplate.getName());
            ddMessagingToDoRepository.save(dDMessagingToDo);
        }
    }

    @Transactional
    public void excludeClientFromExpiryFlow(Contract c, boolean exclude) {
        Contract contract = contractRepository.findOne(c.getId());
        if (contract.isExcludeFromExpiryFlow() == exclude) return;
        contract.setExcludeFromExpiryFlow(exclude);
        contractRepository.silentSave(contract);
    }

    public void createPaymentExpiryFlow(
            Contract contract, PicklistItem monthlyPayment,
            FlowEventConfig flowEventConfig, FlowSubEventConfig flowSubEventConfig) throws Exception {
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        Map<String, Object> lastSignatureType = directDebitSignatureService
                .getLastSignatureType(cpt, false, false);

        boolean useOldSignatures = ((Boolean) lastSignatureType.get("useApprovedSignature") ||
                (Boolean) lastSignatureType.get("useNonRejectedSignature"));

        Map<String, Object> additionalInfo = new HashMap<>();
        Map<String, Object> params = new HashMap<String, Object>() {{
            //ACC-9058
            put("ddSource", DirectDebitSource.PAYMENT_EXPIRY_AUTO_EXTENSION);
            put("skipDdc", ContractService.hasBaseAdditionalInfoByKey(
                    cpt.getContract().getId(), Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "true"));
        }};

        LocalDate ddaStartDate = new LocalDate(cpt.getContract().getPaidEndDate())
                .plusMonths(1).dayOfMonth().withMinimumValue();

        int ddNum = 1;
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        // ACC-9612
        List<DirectDebitGenerationPlan> updatedPlans = new ArrayList<>();
        List<DirectDebitGenerationPlan> plans = directDebitGenerationPlanRepository.findPlansForPaymentExpiryFlow(
                cpt.getContract(), new java.sql.Date(ddaStartDate.dayOfMonth().withMinimumValue().toDate().getTime()));

        // Convert plans list to map grouped by year-month
        Map<String, List<DirectDebitGenerationPlan>> plansByYearMonth = new HashMap<>();
        for (DirectDebitGenerationPlan p : plans) {
            plansByYearMonth.computeIfAbsent(new LocalDate(p.getDDSendDate()).toString("yyyy-MM"), k -> new ArrayList<>()).add(p);
        }

        // handle first dda with plan
        DirectDebit firstDda = generateDDA(ddaStartDate, plansByYearMonth, updatedPlans,
                ontTimeDDMonthDuration, monthlyPayment, useOldSignatures, cpt, params);
        additionalInfo.put("dda_" + ddNum, firstDda.getId());
        ddNum++;


        // handle second dda with plan
        ddaStartDate = ddaStartDate.plusMonths(1);
        DirectDebit secondDda = generateDDA(ddaStartDate, plansByYearMonth, updatedPlans,
                ontTimeDDMonthDuration, monthlyPayment, useOldSignatures, cpt, params);
        additionalInfo.put("dda_" + ddNum, secondDda.getId());
        ddNum++;


        // Handle ddb with plan
        LocalDate ddbStartDate = ddaStartDate.plusMonths(1);
        while(plansByYearMonth.containsKey(ddbStartDate.toString("yyyy-MM"))) {

            DirectDebit dda = generateDDA(ddbStartDate, plansByYearMonth, updatedPlans,
                    ontTimeDDMonthDuration, monthlyPayment, useOldSignatures, cpt, params);

            logger.info("shifted dda id  : " + dda.getId());
            additionalInfo.put("dda_" + ddNum, dda.getId());
            ddNum++;

            ddbStartDate = ddbStartDate.plusMonths(1);
        }

        LocalDate ddbEndDate = ddbStartDate.plusMonths(Setup.getApplicationContext()
                .getBean(SwitchingNationalityService.class).getDefaultPaymentsDuration(cpt.getContract()) - 2);

        DirectDebit ddb = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddbStartDate.toDate(), ddbEndDate.toDate(),
                null, null, null, null, null,
                DirectDebitType.MONTHLY, null, useOldSignatures, null,
                false, true, true, null, true, params);
        logger.info("ddb id : " + ddb.getId());

        additionalInfo.put("ddbId", ddb.getId());

        if (ContractService.hasBaseAdditionalInfoByKey(cpt.getContract().getId(), Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "true")) {
            cpt.getContract().addBaseAdditionalInfo(Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "false");
            contractRepository.silentSave(cpt.getContract());
        }

        updatedPlans.forEach(p -> {
            p.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CREATED);
            directDebitGenerationPlanRepository.save(p);
        });

        startPaymentExpiryFlow(cpt,
                new HashMap<String, Object>() {{
                    put("additionalInfo", additionalInfo);
                }},
                useOldSignatures && directDebitService.isRequiredBankInfoExist(cpt),
                flowEventConfig, flowSubEventConfig);
    }

    private DirectDebit generateDDA(
            LocalDate ddaStartDate, Map<String, List<DirectDebitGenerationPlan>> plansByYearMonth,
            List<DirectDebitGenerationPlan> updatedPlans, int ontTimeDDMonthDuration, PicklistItem oneTimePaymentType,
            boolean useOldSignatures, ContractPaymentTerm cpt,
            Map<String, Object> map) {

        List<DirectDebitGenerationPlan> monthPlans = plansByYearMonth.getOrDefault(ddaStartDate.toString("yyyy-MM"), new ArrayList<>());
        logger.info("monthPlans size: " + monthPlans.size());

        if (!monthPlans.isEmpty()) {
            List<ContractPayment> contractPayments = new ArrayList<>();
            for (DirectDebitGenerationPlan p : monthPlans) {
                contractPayments.add(directDebitGenerationPlanService.createPayment(p, cpt, p.getDDSendDate()));
                updatedPlans.add(p);
            }

            map.put("plansPayments", contractPayments);
        }

        DirectDebit dda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.toDate(), ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(),
                null, null, null, calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.toDate()), null,
                DirectDebitType.ONE_TIME, oneTimePaymentType, useOldSignatures, null,
                true, true, true, cpt, true, map);
        map.remove("plansPayments");
        return dda;
    }

    private void startPaymentExpiryFlow(
            ContractPaymentTerm cpt, Map<String, Object> map,
            boolean bankInfoAndSignaturesAvailable, FlowEventConfig flowEventConfig, FlowSubEventConfig flowSubEventConfig) {

        logger.info( "start payment expiry flow for cpt id: " + cpt.getId());

        map.put("trials", 0);
        map.put("reminders", 1);
        FlowProcessorEntity f = flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        if (bankInfoAndSignaturesAvailable) {
            f.setCompleted(true);
            flowProcessorEntityRepository.save(f);
        }
    }

    public static boolean isDuringTheExpiryFlowPeriod(Contract c) {
        LocalDate paidEndDate = new LocalDate(c.getPaidEndDate());
        int xDays = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD));

        // If we are between (PED - xDays expiry flow parameter) and PED or end of the month
        boolean includeNextMonthDda = (new LocalDate().isAfter(paidEndDate.minusDays(xDays).minusDays(1)) &&
                new LocalDate().isBefore(paidEndDate.plusDays(1))) ||
                (new LocalDate().isAfter(new LocalDate().minusDays(xDays).minusDays(1)) &&
                        new LocalDate().isBefore(new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue()));

        logger.info("includeNextMonthDda: " + includeNextMonthDda);
        return includeNextMonthDda;
    }

    public Map<String, Object> generateDdsForPaymentExpiryFlow(
            ContractPaymentTerm cpt, PicklistItem monthlyPayment, boolean useOldSignatures) {

        Map<String, Object> additionalInfo = new HashMap<>();
        LocalDate ddaStartDate = new LocalDate(cpt.getContract().getPaidEndDate())
                .plusMonths(1).dayOfMonth().withMinimumValue();
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        DirectDebit firstDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.toDate(),
                ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true);
        additionalInfo.put("firstDdaId", firstDda.getId());

        DirectDebit secondDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.plusMonths(1).toDate(),
                ddaStartDate.plusMonths(1).plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.plusMonths(1).toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true);
        additionalInfo.put("secondDdaId", secondDda.getId());

        LocalDate ddbStartDate = ddaStartDate.plusMonths(2);
        LocalDate ddbEndDate = ddbStartDate.plusMonths(Setup.getApplicationContext()
                .getBean(SwitchingNationalityService.class).getDefaultPaymentsDuration(cpt.getContract()) - 2);

        DirectDebit ddb = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddbStartDate.toDate(), ddbEndDate.toDate(),
                null, null, null, null, null,
                DirectDebitType.MONTHLY, null, useOldSignatures, null,
                false, true, true, null, true);
        additionalInfo.put("ddbId", ddb.getId());

        return additionalInfo;
    }

    public void startPaymentExpiryFlow(ContractPaymentTerm cpt, Map<String, Object> map, boolean bankInfoAndSignaturesAvailable) {
        logger.info( "start payment expiry flow for cpt id: " + cpt.getId());

        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW);
        FlowSubEventConfig flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(
                        FlowSubEventConfig.FlowSubEventName.GENERATE_DDS_AFTER_PAID_END_DATE,
                        flowEventConfig);

        map.put("trials", 0);
        map.put("reminders", 1);
        FlowProcessorEntity f = flowProcessorService.createFlowProcessor(
                flowEventConfig, flowSubEventConfig, cpt, map);

        if (bankInfoAndSignaturesAvailable) {
            f.setCompleted(true);
            flowProcessorEntityRepository.save(f);
        }
    }
}