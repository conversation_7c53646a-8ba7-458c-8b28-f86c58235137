package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.UaePhoneNormlizer;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.*;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class DdMessagingTodoService {

    protected static final Logger logger = Logger.getLogger(DdMessagingTodoService.class.getName());
    @Autowired
    private DDMessagingToDoRepository ddMessagingToDoRepository;
    @Autowired
    private DDMessagingRepository ddMessagingRepository;

    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;

    public void createMessagingTodo(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            DDMessagingService.DdMessagingMethod type,
            Map<String, Object> parameters) {

        if (cpt.getContract() == null) return;

        Client client = cpt.getContract().getClient();
        Housemaid maid = cpt.getContract().getHousemaid();

        DDMessagingToDo ddMessagingToDo = new DDMessagingToDo();
        ddMessagingToDo.setType(type.toString());
        ddMessagingToDo.setSendToClient(ddMessagingContract.getSendToClient());
        ddMessagingToDo.setRejectCategory(ddMessagingContract.getDdMessaging().getRejectCategory());
        ddMessagingToDo.setSendToMaid(ddMessagingContract.getSendToMaid());
        ddMessagingToDo.setSendToMaidWhenRetractCancellation(ddMessagingContract.getSendToMaidWhenRetractCancellation());
        ddMessagingToDo.setClientId(client.getId());
        ddMessagingToDo.setMaidId(maid != null ? maid.getId() : null);
        ddMessagingToDo.setAdjustedEndDate(cpt.getContract().getAdjustedEndDate());
        ddMessagingToDo.setContractUuid(cpt.getContract().getUuid());
        ddMessagingToDo.setAccountName(cpt.getAccountName());
        ddMessagingToDo.setEvent(ddMessagingContract.getDdMessaging().getEvent());
        ddMessagingToDo.setDdMessagingConfigId(ddMessagingContract.getDdMessaging().getId());
        ddMessagingToDo.setFlowProcessorEntityId(entity != null ? entity.getId() : null);
        ddMessagingToDo.setAmount(parameters.get("bounced_payment_amount") == null ? null :
                Double.valueOf(parameters.get("bounced_payment_amount").toString()));
        ddMessagingToDo.setPaymentId(parameters.get("bouncedPaymentId") == null ? null :
                Long.valueOf(parameters.get("bouncedPaymentId").toString()));
        ddMessagingToDo.setDirectDebitId(parameters.get("directDebitId") == null ? null :
                Long.valueOf(parameters.get("directDebitId").toString()));
        ddMessagingToDo.setRelatedEntityId(parameters.containsKey("relatedEntityId2") ?
                Long.valueOf((String) parameters.get("relatedEntityId2")) : null);
        ddMessagingToDo.setRelatedEntityType((String) parameters.getOrDefault("relatedEntityType2", null));

        // ACC-8851
        if (parameters.containsKey("isTerminationMessage")) {
            ddMessagingToDo.setTerminationMessage((boolean) parameters.get("isTerminationMessage"));
        }

        if (parameters.containsKey("cancellationReason")) {
            ddMessagingToDo.setCancellationReason(PicklistHelper
                    .getItem(AccountingModule.PICKLIST_TERMINATION_REASON_LIST, (String) parameters.get("cancellationReason")));
        }

        if (parameters.containsKey("sendTime")) {
            ddMessagingToDo.setSendTime((Time) parameters.get("sendTime"));
        }

        if (parameters.containsKey("sendDate")) {
            ddMessagingToDo.setSendDate((java.sql.Date) parameters.get("sendDate"));
        }

        if (ddMessagingToDo.getSendDate() == null && ddMessagingContract.getDdMessaging().getSendDate() != null) {
            ddMessagingToDo.setSendDate(ddMessagingContract.getDdMessaging().getSendDate());
        }

        if (parameters.get("scheduled_termination_date") != null) {
            ddMessagingToDo.setContractScheduleDateOfTermination(new LocalDateTime(parameters.get("scheduled_termination_date")).toDate());
        } else if (cpt.getContract().getScheduledDateOfTermination() != null) {
            ddMessagingToDo.setContractScheduleDateOfTermination(cpt.getContract().getScheduledDateOfTermination());
        }

        switch(type) {
            case MESSAGE:
                if (ddMessagingContract.getDdMessaging().getSendTimeScheduled() != null) {
                    ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getSendTimeScheduled());
                }
                break;
            /*case HUMAN_SMS:
                ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getHumanSmsTime());
                ddMessagingToDo.setHumanSmsDesc(ddMessagingContract.getDdMessaging().getHumanSmsDescription());
                ddMessagingToDo.setHumanSmsTitle(ddMessagingContract.getDdMessaging().getHumanSmsTitle());
                break;
            case EXPERT_TODO:
                ddMessagingToDo.setSendTime(ddMessagingContract.getDdMessaging().getHumanSmsTime());
                break;*/
        }

        if (ddMessagingContract.getSendToClient()) {
            ddMessagingToDo.setClientName(client.getName());
            ddMessagingToDo.setSpouseName(client.getSpouseName());
            ddMessagingToDo.setClientPhoneNumber(client.getNormalizedMobileNumber());
            ddMessagingToDo.setSpousePhoneNumber(client.getNormalizedSpouseMobileNumber());
            ddMessagingToDo.setClientTemplateName(ddMessagingContract.getClientTemplate().getName());
        }

        if (ddMessagingContract.getSendToMaid()) {
            ddMessagingToDo.setMaidName(maid != null ? maid.getName() : "");
            ddMessagingToDo.setMaidFirstName(maid != null ? maid.getFirstName(): "");
            ddMessagingToDo.setMaidPhoneNumber(maid != null ? UaePhoneNormlizer.NormalizePhoneNumber(maid.getPhoneNumber()) : "");
            ddMessagingToDo.setMaidTemplateName(ddMessagingContract.getMaidTemplate().getName());
        }

        // ACC-2445
        if (ddMessagingContract.getSendToMaidWhenRetractCancellation()) {
            ddMessagingToDo.setMaidName(maid != null ? maid.getName() : "");
            ddMessagingToDo.setMaidFirstName(maid != null ? maid.getFirstName(): "");
            ddMessagingToDo.setMaidPhoneNumber(maid != null ? UaePhoneNormlizer.NormalizePhoneNumber(maid.getPhoneNumber()) : "");
            ddMessagingToDo.setMaidWhenRetractCancellationTemplateName(ddMessagingContract.getMaidWhenRetractCancellationTemplate().getName());
        }

        ddMessagingToDoRepository.save(ddMessagingToDo);
    }

    @Transactional
    public void processTodo(Long ddMessagingToDoId) {
        DDMessagingToDo ddMessagingToDo = ddMessagingToDoRepository.findOne(ddMessagingToDoId);
        logger.log(Level.SEVERE, "DirectDebitMessagesSendingJob ddMessagingList on: {0}", ddMessagingToDo.getId());

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Contract contract = contractRepository.findByUuid(ddMessagingToDo.getContractUuid());
        ContractPaymentTerm contractPaymentTerm = contract.getActiveContractPaymentTerm();

        // ACC-7294
        if (validateStopSendDDMessaging(ddMessagingToDo, contractPaymentTerm)) {
            ddMessagingToDo.setActive(false);
            ddMessagingToDoRepository.save(ddMessagingToDo);
            return;
        }

        Map<String, String> parameters = new HashMap<>();
        if (ddMessagingToDo.getRelatedEntityId() != null) {
            parameters.put("relatedEntityId2", ddMessagingToDo.getRelatedEntityId().toString());
            parameters.put("relatedEntityType2", ddMessagingToDo.getRelatedEntityType());
        }
        if (ddMessagingToDo.getPaymentId() != null)
            parameters.put("bouncedPaymentId", ddMessagingToDo.getPaymentId().toString());
        if (ddMessagingToDo.getAmount() != null) {
            parameters.put("bounced_payment_amount", String.valueOf(ddMessagingToDo.getAmount().intValue()));
            parameters.put("latest_bounced_amount", parameters.get("bounced_payment_amount"));
        }

        if (ddMessagingToDo.getContractScheduleDateOfTermination() != null) {
            parameters.put("scheduled_termination_date",
                    DateUtil.formatClientFullDate(ddMessagingToDo.getContractScheduleDateOfTermination()));
            parameters.put("scheduled_termination_date - 1 day",
                    DateUtil.formatClientFullDate(new LocalDate(ddMessagingToDo.getContractScheduleDateOfTermination()).minusDays(1).toDate()));
        }

        DDMessagingContract ddMessagingContract = null;

        if (ddMessagingToDo.getDdMessagingConfigId() != null) {
            if (ddMessagingToDo.getDirectDebitId() != null) {
                parameters.put("directDebitId", ddMessagingToDo.getDirectDebitId().toString());
            }
            DDMessaging ddMessaging = ddMessagingRepository.findOne(ddMessagingToDo.getDdMessagingConfigId());
            ddMessagingContract = Setup.getApplicationContext()
                            .getBean(DDMessagingService.class)
                                    .getDdMessagingContract(ddMessaging, contractPaymentTerm.getBank());

            flowProcessorMessagingService.fillParameters(ddMessagingContract, ddMessagingToDo.getFlowProcessorEntity(), contractPaymentTerm, parameters, ddMessagingContract.getSendToClient());
        }

        // values not of DdMessagingMethod enum is for backward compatibility
        switch(ddMessagingToDo.getType()) {
            case "force_sms":
            case "FORCE_SMS":
                flowProcessorMessagingService.sendForceSms(ddMessagingToDo,
                        ddMessagingContract != null ? ddMessagingContract.getClientTemplate() :
                                TemplateUtil.getTemplate(ddMessagingToDo.getClientTemplateName()),
                        parameters,
                        contractPaymentTerm);
                break;
            case "sms":
            case "MESSAGE":
                logger.info("sending notification of: " + ddMessagingToDo.getId());

                if (ddMessagingToDo.getClientId() == null) logger.info("Client is NULL -> return");

                switch (ddMessagingToDo.getEvent()) {
                    case DirectDebitRejected:
                        if (ddMessagingToDo.getDirectDebitId() != null) {
                            parameters.put("ownerType", "DirectDebit");
                            parameters.put("ownerId", ddMessagingToDo.getDirectDebitId().toString());
                            break;
                        }
                }

                parameters.putIfAbsent("ownerType", "Contract");
                parameters.putIfAbsent("ownerId", contract.getId().toString());

                flowProcessorMessagingService.sendDdMessage(
                        ddMessagingContract, contractPaymentTerm, parameters, new HashMap<>(), ddMessagingToDo);
                break;
            /*case "human_sms":
            case "HUMAN_SMS":
                flowProcessorMessagingService.createDdMessagingHumanSms(
                        ddMessagingContract.getDdMessaging(), contractPaymentTerm, parameters);
                break;
            case "expert_todo":
            case "EXPERT_TODO":
                flowProcessorMessagingService.createDdMessagingExpertTodo(
                        ddMessagingContract.getDdMessaging(), contractPaymentTerm);
                break;*/
        }

        // ACC-8851
        if (ddMessagingToDo.isTerminationMessage()) {
            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .setContractForTermination(contract, ddMessagingToDo.getCancellationReason().getCode());
        }

        ddMessagingToDo.setSent(true);
        ddMessagingToDoRepository.save(ddMessagingToDo);
    }

    // ACC-9835
    // ACC-8851
    public boolean validateStopSendDDMessaging(DDMessagingToDo ddMessagingToDo, ContractPaymentTerm cpt) {

        if (ddMessagingToDo.getEvent().equals(DDMessagingType.Termination)) {
            return cpt.getContract().getScheduledDateOfTermination() == null;
        }

        // Check Flow Processor Entity Flows
        if (ddMessagingToDo.getFlowProcessorEntity() != null &&
                (ddMessagingToDo.getFlowProcessorEntity().isStopped() || ddMessagingToDo.getFlowProcessorEntity().isCompleted())) {
            logger.info("The Flow Processor Entity was stopped or completed, Flow id: " + ddMessagingToDo.getFlowProcessorEntity().getId() +
                    "; stop todo: " + ddMessagingToDo.getId());
            return true;
        }

        // Check DD Flows
        if (ddMessagingToDo.getDirectDebit() != null) {
            DirectDebit dd = ddMessagingToDo.getDirectDebit();

            // Check Rejection and Rejection due bouncing Flow
            if (dd.getDirectDebitRejectionToDo() != null || dd.getDirectDebitBouncingRejectionToDo() != null) {

                // ACC-8851
                if (dd.getDirectDebitRejectionToDo() != null &&
                        !dd.getDirectDebitRejectionToDo().isStopped() &&
                        !dd.getDirectDebitRejectionToDo().isCompleted() &&
                        Setup.getApplicationContext()
                                .getBean(DirectDebitRejectionFlowService.class)
                                .validateStopSendDDMessagingAndStopRejectionFlowIfExpired(ddMessagingToDo)) {
                    return true;
                }

                if (dd.getDirectDebitRejectionToDo() != null && dd.getDirectDebitBouncingRejectionToDo() != null) {
                    if ((dd.getDirectDebitRejectionToDo().isStopped() ||
                            dd.getDirectDebitRejectionToDo().isCompleted()) &&
                            (dd.getDirectDebitBouncingRejectionToDo().isStopped() ||
                                    dd.getDirectDebitBouncingRejectionToDo().isCompleted())) {

                        logger.info("The Rejection and Rejection due bouncing flows were stopped or completed, Rejection Flow id: " +
                                dd.getDirectDebitRejectionToDo().getId() +
                                "; Rejection due bouncing Flow id:: " + dd.getDirectDebitBouncingRejectionToDo().getId() +
                                "; stop todo: " + ddMessagingToDo.getId());
                        return true;
                    }
                } else if (dd.getDirectDebitRejectionToDo() != null) {
                    // Check Rejection Flow
                    if ((dd.getDirectDebitRejectionToDo().isStopped() ||
                            dd.getDirectDebitRejectionToDo().isCompleted())) {
                        logger.info("The Rejection flow was stopped or completed, Flow id: " +
                                dd.getDirectDebitRejectionToDo().getId() +
                                "; stop todo: " + ddMessagingToDo.getId());
                        return true;
                    }
                } else if (dd.getDirectDebitBouncingRejectionToDo() != null) {
                    // Check Rejection due bouncing Flow
                    if (dd.getDirectDebitBouncingRejectionToDo().isStopped() ||
                            dd.getDirectDebitBouncingRejectionToDo().isCompleted()) {
                        logger.info("The Rejection due bouncing flow was stopped or completed, Flow id: " +
                                dd.getDirectDebitBouncingRejectionToDo().getId() +
                                "; stop todo: " + ddMessagingToDo.getId());
                        return true;
                    }
                }
            } else if ((dd.getCategory().equals(DirectDebitCategory.B) && !dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                    (dd.getCategory().equals(DirectDebitCategory.A) && !dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE))) {
                logger.info("The Incomplete flow was stopped or completed, Flow id: " + dd.getId() +
                        "; stop todo: " + ddMessagingToDo.getId());
                return true;
            }
        }

        // Check Bounced Payment Flow
        if (ddMessagingToDo.getPayment() != null &&
                (ddMessagingToDo.getPayment().isReplaced() ||
                        ddMessagingToDo.getPayment().getStatus().equals(PaymentStatus.DELETED))) {
            logger.info("The Bounced Payment flow was replaced or deleted, Flow id: " + ddMessagingToDo.getPayment().getId() +
                    "; stop todo: " + ddMessagingToDo.getId());
            return true;
        }

        return false;
    }
}