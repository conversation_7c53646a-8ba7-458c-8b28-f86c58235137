package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.MvHousemaidNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.AccountingModuleMainJob;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * <AUTHOR> Mariam
 * Acc-8796
 */
@Service
public class MaidVisaFailedMedicalCheckService {

    private static final Logger logger = Logger.getLogger(MaidVisaFailedMedicalCheckService.class.getName());

    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private MessagingService messagingService;
    @Autowired
    private ClientMessagingAndRefundService clientMessagingAndRefundService;
    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private PaymentExpiryService paymentExpiryService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    public boolean checkIfContractIsPreCollectedAndMaidHasNotMedicalCertificate(Contract c) {
        return c.isMaidVisa() &&
                ContractService.isPreCollectedSalary(c) &&
                !checkIfHousemaidPassedMedicalStep(c);
    }

    public void processActivePreCollectedMaidVisaContracts() {
        Page<Contract> p;
        Long lastId = -1L;

        do {
            p = contractRepository.findActiveMVPreCollectedContractsWithPreCollectedPayments(
                    lastId,
                    new LocalDate().minusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    new LocalDate().toString("yyyy-MM"),
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    PageRequest.of(0, 200));

            logger.info("Candidate Contracts List Size: " + p.getContent().size());

            p.getContent()
                    .forEach(c -> {
                        try {
                            logger.info("pre collected contract id: " + c.getId() + "; start: " + c.getStartOfContract());

                            startFailedMedicalCheck(c);

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());

        lastId = -1L;
        int threshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD));
        do {
            p = contractRepository.findActiveMVPreCollectedContractsWithoutPreCollectedPayments(
                    lastId,
                    new LocalDate().minusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    threshold,
                    new LocalDate().toString("yyyy-MM"),
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    PageRequest.of(0, 200));

            logger.info("Candidate Payment List Size: " + p.getContent().size());

            p.getContent()
                    .forEach(c -> {
                        try {
                            logger.info("monthly contract id: " + c.getId() + "; start: " + c.getStartOfContract());

                            LocalDate discountStartDate = new LocalDate(calculateDiscountsWithVatService
                                    .getDiscountStartDateInMillis(c.getActiveContractPaymentTerm()));
                            if (!QueryService.existsEntity(
                                    Payment.class, "e.contract.id = :p0 and e.typeOfPayment.code = 'monthly_payment' and " +
                                            "e.status = 'RECEIVED' and e.amountOfPayment > 0 and e.includeWorkerSalary = true and " +
                                            "e.dateOfPayment between :p1 and :p2",
                                    new Object[]{ c.getId(), discountStartDate.toDate(), discountStartDate.dayOfMonth().withMaximumValue().toDate() })) {
                                return;
                            }

                            startFailedMedicalCheck(c);

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void processFailedMedicalCheckForRecurringContracts() {
        Page<Payment> p;
        Long lastId = -1L;

        do {
            p = paymentRepository.findRecurringContractsWithPreCollectdPaymentsForMedicalCheck(
                    lastId,
                    new LocalDate().minusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    new LocalDate().toString("yyyy-MM"),
                    PageRequest.of(0, 200));

            logger.info("Candidate Contracts List Size: " + p.getContent().size());

            p.getContent()
                    .forEach(payment -> {
                        try {
                            logger.info("pre collected payment id: " + payment.getId() +
                                    "; pre collected contract id: " + payment.getContract().getId() +
                                    "; start: " + payment.getContract().getStartOfContract());

                            startFailedMedicalCheckForRecurring(payment);

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());


        lastId = -1L;
        int threshold = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD));
        do {
            p = paymentRepository.findAllRecurringContractsForMedicalCheck(
                    lastId,
                    new LocalDate().minusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    threshold,
                    new LocalDate().toString("yyyy-MM"),
                    PageRequest.of(0, 200));

            logger.info("Contracts List Size: " + p.getContent().size());

            p.getContent()
                    .forEach(payment -> {
                        try {
                            logger.info("monthly payment id: " + payment.getId() +
                                    "; monthly contract id: " + payment.getContract().getId() +
                                    "; start: " + payment.getContract().getStartOfContract());

                            LocalDate discountStartDate = new LocalDate(calculateDiscountsWithVatService
                                    .getDiscountStartDateInMillis(payment.getContract().getActiveContractPaymentTerm()));
                            if (!QueryService.existsEntity(
                                    Payment.class, "e.contract.id = :p0 and e.typeOfPayment.code = 'monthly_payment' and " +
                                            "e.status = 'RECEIVED' and e.amountOfPayment > 0 and e.includeWorkerSalary = true and " +
                                            "e.dateOfPayment between :p1 and :p2",
                                    new Object[]{payment.getContract().getId(), discountStartDate.toDate(), discountStartDate.dayOfMonth().withMaximumValue().toDate() })) {
                                return;
                            }

                            startFailedMedicalCheckForRecurring(payment);

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public boolean isDuringFirstSalaryMonth(ContractPaymentTerm cpt) {

        logger.info("is contract during First Salary Month -> contract ID: " + cpt.getContract().getId());

        if (PaymentHelper.isPaymentDateEqualsDiscountStartDate(cpt, new Date())) {
            if (ContractService.isDiscountEffectiveAfterLessThanThreshold(cpt)) {
                logger.info("contract during first salary month -> true");
                return true;
            }
        }
        logger.info("contract during first salary month -> false");

        return paymentRepository
                .existsReceivedPreCollectedPaymentByContractAndDateOfPaymentBetween(
                        cpt.getContract(),
                        new LocalDate().dayOfMonth().withMinimumValue().toDate(),
                        new LocalDate().dayOfMonth().withMaximumValue().toDate());
    }

    @Transactional
    public void startFailedMedicalCheck(Contract c) {

        logger.info("start failed medical for DD -> contract ID: " + c.getId());

        if (checkIfHousemaidPassedMedicalStep(c)) {
            c.addBaseAdditionalInfo("passedMedicalStep", "true");
            c.setExcludeFromExpiryFlow(false);
            contractRepository.silentSave(c);
            return;
        }

         c.getHousemaid().addBaseAdditionalInfo("hasMedicalCheckThisMonth", new LocalDate().toString("yyyy-MM"));
         housemaidRepository.silentSave(c.getHousemaid());

        logger.info("process first medical check -> contract ID: " + c.getId());
        checkFirstMedical(c, false,
                MvNotificationTemplateCode.MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT);
    }

    @Transactional
    public void startFailedMedicalCheckForRecurring(Payment payment) {

        logger.info("start failed medical for recurring -> contract ID: " + payment.getContract().getId());

        if (checkIfHousemaidPassedMedicalStep(payment.getContract())) {
            payment.getContract().addBaseAdditionalInfo("passedMedicalStep", "true");
            payment.getContract().setExcludeFromExpiryFlow(false);
            contractRepository.silentSave(payment.getContract());
            return;
        }

        payment.getContract().getHousemaid().addBaseAdditionalInfo("hasMedicalCheckThisMonth",
                new LocalDate().toString("yyyy-MM"));
        housemaidRepository.silentSave(payment.getContract().getHousemaid());

        logger.info("Deleting recurring scheduled payment with id: " + payment.getId());
        payment.setStatus(PaymentStatus.DELETED);
        paymentService.updatePaymentSilent(payment);

        ContractPaymentTerm cpt = payment.getContract().getActiveContractPaymentTerm();

        boolean duringFirstSalaryMonth = isDuringFirstSalaryMonth(cpt);

        addWaivedPaymentAndShiftMaid(payment.getContract());

        if (duringFirstSalaryMonth) {
            logger.info("first failed medical for recurring -> contract ID: " + cpt.getContract().getId());
            // Send first failed medical check message
            sendClientFailedMedicalCheckMessages(payment.getContract(),
                    MvNotificationTemplateCode.MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT);
        } else {
            logger.info("second failed medical for recurring -> contract ID: " + cpt.getContract().getId());
            sendClientFailedMedicalCheckMessages(payment.getContract(),
                    MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_RECURRING_CONTRACT);
        }
    }

    @Transactional
    public boolean checkFailedMedicalsForPayingViaCc(FlowProcessorEntity entity) {

        ContractPaymentTerm cpt = entity.getActiveCpt();

        if (HousemaidService.hasBaseAdditionalInfoByKey(cpt.getContract().getHousemaid().getId(),
                "hasMedicalCheckThisMonth",
                new LocalDate().toString("yyyy-MM"))) return false;

        if (excludeFromMedicalCheck(cpt) &&
                entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER)) {
            logger.info("Contract is excluded from medical check -> skip");
            return false;
        }

        if (!checkIfHousemaidIsFailedMedical(cpt)) {
            logger.info("Maid failed medical step -> skip");
            return false;
        }

        if (Arrays.asList(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                FlowSubEventConfig.FlowSubEventName.NO_SIGNATURE).contains(entity.getCurrentSubEvent().getName())) {

            String conditions = "e.contract.id = :p0 and e.status = 'RECEIVED' and e.amountOfPayment > 0 and " +
                    "(e.typeOfPayment.code in ('pre_collected_payment', 'pre_collected_payment_no_vat') @monthly@ )";
            boolean isDiscountLessThanThreshold = ContractService.isDiscountEffectiveAfterLessThanThreshold(cpt);

            List<Object> l = new ArrayList<>();
            l.add(cpt.getContract().getId());

            if (isDiscountLessThanThreshold) {
                DateTime d = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));
                conditions = conditions.replace("@monthly@", " or (e.typeOfPayment.code = 'monthly_payment' and " +
                        "e.includeWorkerSalary = true and e.dateOfPayment between :p1 and :p2)");
                l.add(new LocalDate(d).dayOfMonth().withMinimumValue().toDate());
                l.add(new LocalDate(d).dayOfMonth().withMaximumValue().toDate());
            } else {
                conditions = conditions.replace("@monthly@", "");
            }

            if (!QueryService.existsEntity(Payment.class, conditions, l.toArray())) {
                logger.info("Contract has no received pre-collected payment -> skip");
                return false;
            }

            MvNotificationTemplateCode clientTemplateCode =
                    isDuringFirstSalaryMonth(cpt) ?
                            MvNotificationTemplateCode.MV_FIRST_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT :
                            MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_CREDIT_CARD_CONTRACT;

            checkFirstMedical(cpt.getContract(), true, clientTemplateCode);
        }

        cpt.getContract().getHousemaid().addBaseAdditionalInfo("hasMedicalCheckThisMonth", new LocalDate().toString("yyyy-MM"));
        housemaidRepository.silentSave(cpt.getContract().getHousemaid());

        return true;
    }

    public boolean sendInitialFlowMessageForFailedMedicalCheck(FlowProcessorEntity entity) {

        logger.info("handle initial flow contains non-monthly payment next month -> flow ID: " + entity.getId());

        ContractPaymentConfirmationToDo currentTodo = entity.getContractPaymentConfirmationToDo();

        List<ContractPaymentWrapper> monthlyPaymentsAfterPreCollectedMonth = currentTodo.getContractPaymentList().stream()
                .filter(wrapper -> PaymentHelper.isMonthlyPayment(wrapper.getPaymentType()) &&
                        new LocalDate(wrapper.getPaymentDate())
                                .isAfter(new LocalDate(entity.getContract().getStartOfContract()).plusMonths(1).dayOfMonth().withMaximumValue()))
                .collect(Collectors.toList());

        if (monthlyPaymentsAfterPreCollectedMonth.isEmpty()) {
            return true;
        }

        currentTodo.setDisabled(true);
        contractPaymentConfirmationToDoRepository.silentSave(currentTodo);

        if (monthlyPaymentsAfterPreCollectedMonth.size() == currentTodo.getContractPaymentList().size()) {
            entity.setStopped(true);
            flowProcessorEntityRepository.silentSave(entity);
            return false;
        }

        logger.info("Found non-monthly payments for next month -> flow ID: " + entity.getId() +
                   ", non-monthly payments: " + monthlyPaymentsAfterPreCollectedMonth.size());

        List<ContractPaymentWrapper> wrappers = currentTodo.getContractPaymentList().stream()
                .filter(wrapper -> !monthlyPaymentsAfterPreCollectedMonth.contains(wrapper))
                .collect(Collectors.toList());

        List<ContractPayment> contractPayments = wrappers.stream()
                .map(ContractPaymentWrapper::initContractPaymentProps)
                .collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("required", false);

        entity.setContractPaymentConfirmationToDo(
                contractPaymentConfirmationToDoService.createConfirmationTodoFromContractPayments(entity.getActiveCpt(),
                        contractPayments, ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card, map));

        flowProcessorEntityRepository.save(entity);

        return true;
    }

    public boolean excludeFromMedicalCheck(ContractPaymentTerm cpt) {
         return cpt.getContract().getAllowRecurring() &&
                    cpt.getSourceId() != null &&
                        cpt.getSourceAmount() != null &&
                            cpt.getSourceInfo() != null &&
                    paymentRepository.existsRecurringPaymentByContractAndStatusAndDateBetween(
                            cpt.getContract(),
                            PaymentStatus.PDC,
                            new LocalDate(cpt.getContract().getPaidEndDate()).plusDays(1).toDate(),
                            new LocalDate(cpt.getContract().getPaidEndDate()).plusMonths(1).toDate());
    }

    public boolean checkIfHousemaidIsFailedMedical(ContractPaymentTerm cpt) {
        return ContractService.isPreCollectedSalary(cpt.getContract()) &&
                !checkIfHousemaidPassedMedicalStep(cpt.getContract());
    }

    private boolean checkIfHousemaidPassedMedicalStep(Contract c) {
        return c.getHousemaid().getMedicalCertificateUploadDate() != null;
    }

    @Transactional
    public void handleMedicalCheckWithNextMonthPaymentReceived(Contract c, Payment payment) {

        logger.info("handle medical check with next month payment received -> contract ID: " + c.getId());

        c.getHousemaid().addBaseAdditionalInfo("hasMedicalCheckThisMonth", new LocalDate().toString("yyyy-MM"));
        housemaidRepository.silentSave(c.getHousemaid());

        // 1. Add the contract to the expiry flow if not added
        paymentExpiryService.excludeClientFromExpiryFlow(c, false);

        // 2. Shift the start date of the maid one month so payroll release will exclude her without triggering any refunds
        shiftStartDateOfHousemaidFromPayroll(c, new LocalDate().plusMonths(1).withDayOfMonth(1).toDate());

        // 3. Send a client message of a row #9 Pre-collected Messages and the maid message (row #8)
        sendMedicalCheckMessagesForNextPaymentReceived(c);

        // 4. Trigger Non-conditional refund "Maid's salary due to missing medical certificate"
        clientMessagingAndRefundService.addClientRefund(
                c,
                c.getClient(),
                payment.getAmountOfPayment(),
                ClientRefundRequestType.ERP,
                PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP,
                payment, // No related payment for this refund
                "Payment Received Event flow");

    }

    private void sendMedicalCheckMessagesForNextPaymentReceived(Contract contract) {
        logger.info("send medical check messages for received payment -> contract ID: " + contract.getId());

        // Send client message of row #9 Pre-collected Messages (MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_RECURRING_CONTRACT)
        sendClientFailedMedicalCheckMessages(contract,
                MvNotificationTemplateCode.MV_FAILED_MEDICAL_NEXT_PAYMENT_RECEIVED);

        // Send maid message (row #8 - MV_HOUSEMAID_FIRST_FAILED_MEDICAL_CHECK_SALARY)
        sendMaidFailedMedicalCheckMessages(contract,
                MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_FAILED_MEDICAL_NEXT_PAYMENT_RECEIVED);
    }

    @Transactional
    public boolean checkFirstMedical(Contract c, boolean payingViaCc, MvNotificationTemplateCode clientTemplateCode) {

        logger.info(" enter first medical check -> contract ID: " + c.getId());

        if (!payingViaCc) {
            // check (next month DD is DDA - it's before the last one DDA)
            if (directDebitRepository.isDdbCoverNextMonth(
                    c,
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                    DirectDebitService.notAllowedStatuses)) {
                // if not then it's old structure proceed with second medical check.
                return checkSecondMedical(c);
            }

            // cancel upcoming DDA only for DD Payers
            cancelUpcomingDDA(c);
        }

        addWaivedPaymentAndShiftMaid(c);

        sendClientFailedMedicalCheckMessages(c, clientTemplateCode);

        return true;
    }

    private void addWaivedPaymentAndShiftMaid(Contract c) {

        logger.info("process add waived payment and shift maid -> contract ID: " + c.getId());

        // Push the ped and avoid c termination
        addWaivedPreCollectedPaymentAndPushPED(c, new LocalDate(c.getPaidEndDate()).plusMonths(1).dayOfMonth().withMinimumValue().toDate());

        // Exclude the maid from the payroll and Stop Client Refund
        shiftStartDateOfHousemaidFromPayroll(c, new LocalDate(c.getPaidEndDate()).plusMonths(1).withDayOfMonth(1).toDate());

        // Exclude client from Expiry Flow
        paymentExpiryService.excludeClientFromExpiryFlow(c, true);

        sendMaidFailedMedicalCheckMessages(c, MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_FIRST_FAILED_MEDICAL_CHECK_SALARY);
    }

    private List<ContractPayment> cancelUpcomingDDA(Contract contract) {

        logger.info("cancel upcoming DDA -> contract ID: " + contract.getId());

        List<DirectDebit> l = directDebitRepository
                .getActiveDdaByMonth(
                        contract,
                        new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                        new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate(),
                        DirectDebitService.notAllowedStatuses);

        // Handle online credit card payments for next month and get cancelled payment info
        Set<String> createdPayments = cancelOnlineCreditCardPaymentsForNextMonth(contract);

        l.forEach(d -> {
            logger.info("dd id: " + d.getId());

            // Check if the DDA includes other non-monthly payments
            List<ContractPayment> nonMonthlyPayments = d.getContractPayments().stream()
                    .filter(cp -> !PaymentHelper.isMonthlyPayment(cp.getPaymentType()))
                    .collect(Collectors.toList());

            if (!nonMonthlyPayments.isEmpty()) {
                logger.info("DDA includes non-monthly payments -> DD ID: " + d.getId() +
                        ", non-monthly payments count: " + nonMonthlyPayments.size());

                // Filter out payments that have same date and type as cancelled online payments
                List<ContractPayment> filteredNonMonthlyPayments = nonMonthlyPayments.stream()
                        .filter(cp -> !createdPayments.contains(
                                new LocalDate(cp.getDate()).toString("yyyy-MM") + "_" + cp.getPaymentType().getCode()))
                        .collect(Collectors.toList());

                if (filteredNonMonthlyPayments.isEmpty()) return;

                // Create required online credit card payment for non-monthly payments instead of new DDA
                // Create required online credit card payment using UnpaidOnlineCreditCardPaymentService
                Setup.getApplicationContext()
                        .getBean(UnpaidOnlineCreditCardPaymentService.class)
                        .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                                filteredNonMonthlyPayments,
                                contract.getActiveContractPaymentTerm(),
                                new Date(),
                                new HashMap<String, Object>() {{
                                    put("required", true); // Mark as required payment
                                }});
            }

            Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                    .cancelWholeDD(d, DirectDebitCancellationToDoReason.HOUSEMAID_FAILED_MEDICAL_CHECK);

        });

        return l.stream().map(DirectDebit::getContractPayments)
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    private Set<String> cancelOnlineCreditCardPaymentsForNextMonth(Contract contract) {

        logger.info("cancel online credit card payments for next month -> contract ID: " + contract.getId());

        // Find online reminder flows that cover next month's payment
        List<FlowProcessorEntity> onlineFlows = flowProcessorEntityRepository
                .findOnlineReminderFlowsCoverNextMonthlyPayment(contract,
                        new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                        new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());

        if (onlineFlows == null || onlineFlows.isEmpty()) {
            logger.info("No online credit card payment flows found for next month -> contract ID: " + contract.getId());
            return new HashSet<>();
        }

        Set<String> proceedPayments = new HashSet<>();
        proceedPayments.addAll(handleOnlineCreditCardFlow(onlineFlows.get(0), contract));

        return proceedPayments;
    }

    private Set<String> handleOnlineCreditCardFlow(FlowProcessorEntity entity, Contract contract) {

        logger.info("handle online credit card flow -> flow ID: " + entity.getId() +
                   ", contract ID: " + contract.getId());

        List<ContractPaymentWrapper> monthlyPaymentsAfterThisMonth = entity.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
        .filter(wrapper -> PaymentHelper.isMonthlyPayment(wrapper.getPaymentType()) &&
                new LocalDate(wrapper.getPaymentDate()).toDate().getTime() >=
                        ContractService.getStartOfMonthFollowingPreCollectedMonth(contract))
        .collect(Collectors.toList());

        if (monthlyPaymentsAfterThisMonth.isEmpty()) return new HashSet<>();

        return proceedOnlineCreditCardFlow(entity, monthlyPaymentsAfterThisMonth);
    }

    private Set<String> proceedOnlineCreditCardFlow(FlowProcessorEntity entity, List<ContractPaymentWrapper> toFilter) {

        ContractPaymentConfirmationToDo currentTodo = entity.getContractPaymentConfirmationToDo();

        Set<String> proceedPayments = new HashSet<>();
        List<ContractPayment> contractPayments = currentTodo.getContractPaymentList().stream()
                .filter(wrapper -> !toFilter.contains(wrapper))
                .map(ContractPaymentWrapper::initContractPaymentProps)
                .peek(cp -> proceedPayments.add(
                        new LocalDate(cp.getDate()).toString("yyyy-MM")+ "_" + cp.getPaymentType().getCode()))
                .collect(Collectors.toList());

        currentTodo.setContractPaymentList(
                currentTodo.getContractPaymentList().stream()
                        .peek(wrapper -> {
                            if (!toFilter.contains(wrapper)) {
                                wrapper.setGeneratedPaymentId(null);
                            } else {
                                Payment p = paymentRepository.findOne(wrapper.getGeneratedPaymentId());
                                if (p != null && p.getStatus().equals(PaymentStatus.PRE_PDP)) {
                                    logger.info("payment id: " + p.getId());
                                    p.setStatus(PaymentStatus.DELETED);
                                    paymentService.forceUpdatePayment(p);
                                }
                            }
                        }).collect(Collectors.toList()));

        logger.info("Todo payments size: " + currentTodo.getContractPaymentList().size() +
                "; wrappers Ids: " + currentTodo.getContractPaymentList().stream().map(BaseEntity::getId).collect(Collectors.toList()));

        if (toFilter.size() == currentTodo.getContractPaymentList().size()) return new HashSet<>();

        unpaidOnlineCreditCardPaymentService
                .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(contractPayments, entity.getActiveCpt());

        return proceedPayments;
    }

    public void addWaivedPreCollectedPaymentAndPushPED(Contract c, Date d) {

        logger.info("add waived payment and push PED -> contract ID: " + c.getId());

        ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();
        ContractPaymentType monthlyPaymentType = cpt.getContractPaymentTypes().stream()
                .filter(type -> PaymentHelper.isMonthlyPayment(type.getType()))
                .findFirst().orElse(null);

        paymentService.addWaivedPayment(
                cpt,
                d,
                paymentService.getWaivedPayment(c, monthlyPaymentType.getType()),
                monthlyPaymentType.getType(),
                monthlyPaymentType.getDescription(),
                new HashMap<String, Object>() {{
                    put("methodOfPayment", PaymentMethod.ADJUSTMENT);
                }});
    }

    private void shiftStartDateOfHousemaidFromPayroll(Contract c, Date d) {
        moduleConnector.get(
                "/payroll/housemaid/shiftMvMaidStartDate/" + c.getHousemaid().getId() +
                "?date=" + new LocalDate(d).toString("yyyy-MM-dd"),
                Object.class);
    }

    @Transactional
    public boolean checkSecondMedical(Contract c) {

        logger.info("enter second medical check -> contract ID: " + c.getId());

        shiftStartDateOfHousemaidFromPayroll(c, new LocalDate().plusMonths(1).withDayOfMonth(1).toDate());

        // add contract to expiry flow
        paymentExpiryService.excludeClientFromExpiryFlow(c, false);

        addConditionalRefundForUpComingDdb(c);

        prepareAccountingEntityPropertyForSendClientSecondFailedMessages(c);

        return true;
    }

    private void addConditionalRefundForUpComingDdb(Contract c) {

        logger.info("add conditional refund for upcoming DDB -> contract ID: " + c.getId());

        List<Payment> l = paymentRepository.findFirstDDBPaymentByContractAndStatusNotInAndDateBetween(
                c,
                DirectDebitService.notAllowedStatuses,
                new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());

        if (l.isEmpty()) return;

        Payment p = l.get(0);
        clientMessagingAndRefundService.addConditionalClientRefund(
                c,
                c.getClient(),
                p.getAmountOfPayment(),
                ClientRefundRequestType.ERP,
                PARAMETER_PCS_FAILED_MEDICAL_PAYMENT_REQUEST_PURPOSE,
                p, "Payment Received Event flow");

    }

    @Transactional
    public void handlePreCollectedContract(Payment entity, Contract c) {
        logger.info("payment Id: " + entity.getId());

        ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();

        if (entity.getAmountOfPayment() > 0 &&
                AbstractPaymentTypeConfig.preCollectedPaymentTypes.contains(entity.getTypeOfPayment().getCode()) &&
                !ContractService.isPreCollectedSalary(c)) {
            c.addBaseAdditionalInfo("preCollectedSalary", "true");
            contractRepository.silentSave(c);
        } else if (PaymentHelper.isMonthlyPayment(entity.getTypeOfPayment()) &&
                entity.getAmountOfPayment() == 0 &&
                new DateTime(entity.getDateOfPayment()).toString("yyyy-MM")
                        .equals(new DateTime(calculateDiscountsWithVatService
                                .getDiscountStartDateInMillis(cpt)).toString("yyyy-MM")) &&
                        ContractService.isPreCollectedSalary(c) &&
                ContractService.isDiscountEffectiveAfterLessThanThreshold(cpt)) {
            c.removeBaseAdditionalInfo("preCollectedSalary");
            contractRepository.silentSave(c);
        }
    }

    public void sendMaidFailedMedicalCheckMessages(Contract c, MvHousemaidNotificationTemplateCode templateCode) {
        if (c.getHousemaid() == null) return;
        Template template = TemplateUtil.getTemplate(templateCode.toString());

        messagingService.sendMessageToMaid(
                c,
                c.getHousemaid(),
                template,
                getFailedMedicalParameters(c, template),
                c.getHousemaid().getId(),
                c.getHousemaid().getEntityType());
    }

    public void sendClientFailedMedicalCheckMessages(Contract c, MvNotificationTemplateCode templateCode) {
        Template template = TemplateUtil.getTemplate(templateCode.toString());

        Map<String, String> parameters = getFailedMedicalParameters(c, template);
        messagingService.sendMessageToClient(
                c,
                parameters,
                getFailedMedicalContext(parameters),
                c.getId(),
                c.getEntityType(),
                template);
    }

    private Map<String, String> getFailedMedicalParameters(Contract c, Template t) {
        Map<String, String> parameters = new HashMap<>();

        DateTime d = new DateTime(calculateDiscountsWithVatService
                        .getDiscountStartDateInMillis(c.getActiveContractPaymentTerm()));

        parameters.put("first_pre_collected_month", new LocalDate(paymentRepository.
                findFirstPreCollectedOrMonthlyWithSalaryDate(
                        c, d.dayOfMonth().withMinimumValue().toDate(), d.dayOfMonth().withMaximumValue().toDate()))
                .toString("MMMM"));

        parameters.put("month_plus_one", new LocalDate().plusMonths(1).toString("MMMM"));
        parameters.put("month", new LocalDate().toString("MMMM"));
        parameters.put("month_plus_two", new LocalDate().plusMonths(2).toString("MMMM"));
        parameters.put("month_minus_one", new LocalDate().minusMonths(1).toString("MMMM"));
        parameters.put("month_minus_two", new LocalDate().minusMonths(2).toString("MMMM"));

        if (t.getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@whatsapp_link@")) {
            parameters.put("whatsapp_link", Setup.getApplicationContext()
                    .getBean(Utils.class)
                    .shorteningUrl("https://wa.me/" +
                            StringHelper.NormalizePhoneNumber(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_MAIDS_CC_CLIENT_CALL))));
        }
        return parameters;
    }

    private Map<String, AppAction> getFailedMedicalContext(Map<String, String> parameters) {
        if (!parameters.containsKey("whatsapp_link")) return new HashMap<>();

        AppAction chatWithUs = new AppAction();
        chatWithUs.setType(AppActionType.BUTTON);
        chatWithUs.setText("Whatsapp us");
        chatWithUs.setFunctionType(FunctionType.WEB_SERVICE);
        chatWithUs.setNavigationType(NavigationType.WEB);
        chatWithUs.setHyperlink(parameters.get("whatsapp_link"));
        chatWithUs.setAppRouteName("");
        chatWithUs.setAppRouteArguments(new HashMap<>());

        return new HashMap<String, AppAction>() {{
            put("whatsApp_us", chatWithUs);
        }};
    }

    private void prepareAccountingEntityPropertyForSendClientSecondFailedMessages(Contract contract) {
        try {
            AccountingEntityProperty a = accountingEntityPropertyRepository
                    .findByKeyAndOriginAndDeletedFalseAndPurposeEquals(AccountingModuleMainJob.SEND_MESSAGE, contract,
                            "SEND_CLIENT_SECOND_FAILED_MESSAGES");

            if (a != null) {
                return;
            }

            AccountingEntityProperty property = new AccountingEntityProperty();
            property.setOrigin(contract);
            property.setKey(AccountingModuleMainJob.SEND_MESSAGE);
            property.setMessageSendDate(new DateTime().plusMonths(1).withDayOfMonth(1).withTimeAtStartOfDay().withHourOfDay(16).toDate());
            property.setPurpose("SEND_CLIENT_SECOND_FAILED_MESSAGES");
            property.setValue(objectMapper.writeValueAsString(new HashMap<String, String>() {{
                put("templateBaseName", "MV_SECOND_FAILED_MEDICAL_CHECK_SALARY");
            }}));
            accountingEntityPropertyRepository.save(property);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    public Map<String, Object> addAdjustmentPaymentForPreCollectedContractsFromSheet(MultipartFile file)
                throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        Row headerRow = sheet.getRow(0);
        int contractIdIndex = -1;
        int paymentDateIndex = -1;

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String headerValue = cell.getStringCellValue().trim().toUpperCase();
                switch (headerValue) {
                    case "CONTRACT_ID":
                        contractIdIndex = i;
                        break;
                    case "PAYMENT_DATE":
                        paymentDateIndex = i;
                        break;
                }
            }
        }

        // Validate that all required columns are found
        if (contractIdIndex == -1 || paymentDateIndex == -1) {
            throw new IllegalArgumentException("Excel file must contain columns: CONTRACT_ID, PAYMENT_DATE");
        }

        List<String> errors = new ArrayList<>();

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;

            try {

                long contractId = (long) row.getCell(contractIdIndex).getNumericCellValue();
                Date paymentDate = row.getCell(paymentDateIndex).getDateCellValue();

                addWaivedPreCollectedPaymentAndPushPED(contractRepository.findOne(contractId),
                        paymentDate);

            } catch (Exception e) {
                errors.add("Row " + row.getRowNum() + ": " + e.getMessage());
                e.printStackTrace();
            }
        }

        return new HashMap<String, Object>() {{
            put("errors", errors);
        }};

    }

    // ACC-9441 Waive Pre-collected Salary
    public void processUnFlagContractAsPreCollectedAcc9441(ContractPaymentTerm cpt) {
        logger.info("Starting Unflag Contract As Pre-Colelcted for contract ID: " + cpt.getContract().getId());

        Contract c = cpt.getContract();

        // Remove the preCollectedSalary flag
        c.removeBaseAdditionalInfo("preCollectedSalary");
        contractRepository.silentSave(c);

        DateTime discountStartDate = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));
        logger.info("Discount start date: " + discountStartDate.toString("yyyy-MM-dd"));

        DateTime startDate = ContractService.isCurrentMonthSigningMonth(c) ?
                discountStartDate :
                new DateTime().dayOfMonth().withMinimumValue();

        Set<String> proceedReminderPayments = cancelOnlineCreditCardPayments(c);

        // Handle recurring
        if (cpt.getContract().getAllowRecurring()) {
            logger.info("Contract is recurring, processing recurring payer case");
            processUnFlagContractAsPreCollectedRecurringPayerCase(cpt, new DateTime(startDate));
        }

        if (ContractService.isCurrentMonthSigningMonth(c)) {
            processUnFlagContractAsPreCollectedSigningMonth(cpt, new DateTime(startDate), proceedReminderPayments);
            return;
        }

        processUnFlagContractAsPreCollectedCurrentMonth(cpt, startDate.toLocalDate(),
                discountStartDate.toLocalDate(), proceedReminderPayments);

    }

    private Set<String> cancelOnlineCreditCardPayments(Contract c) {

        logger.info("cancel online credit card payments for next month -> contract ID: " + c.getId());

        // Find online reminder flows that cover next month's payment
        List<FlowProcessorEntity> onlineFlows = flowProcessorEntityRepository
                .findOnlineReminderFlowsCoverNextMonthlyPayment(c,
                        ContractService.isCurrentMonthSigningMonth(c) ?
                                new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate() :
                                new LocalDate().dayOfMonth().withMinimumValue().toDate(),
                        ContractService.isCurrentMonthSigningMonth(c) ?
                                new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate() :
                                new LocalDate().dayOfMonth().withMaximumValue().toDate());

        if (onlineFlows == null || onlineFlows.isEmpty()) {
            logger.info("No online credit card payment flows found for next month -> contract ID: " + c.getId());
            return new HashSet<>();
        }

        // pass first an online reminder.

        return new HashSet<>(handleOnlineCreditCardFlowForWaivePreCollected(onlineFlows.get(0), c));
    }

    private Set<String> handleOnlineCreditCardFlowForWaivePreCollected(FlowProcessorEntity entity, Contract contract) {

        logger.info("handle online credit card flow -> flow ID: " + entity.getId() +
                ", contract ID: " + contract.getId());

        List<ContractPaymentWrapper> monthlyPaymentWithWorkerSalary = entity.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                .filter(wrapper -> PaymentHelper.isMonthlyPayment(wrapper.getPaymentType()) && wrapper.isIncludeWorkerSalary())
                .collect(Collectors.toList());

        if (monthlyPaymentWithWorkerSalary.isEmpty()) return new HashSet<>();

        return proceedOnlineCreditCardFlow(entity, monthlyPaymentWithWorkerSalary);
    }

    public void processUnFlagContractAsPreCollectedSigningMonth(ContractPaymentTerm cpt, DateTime startDate, Set<String> proceedPayments) {
        logger.info("Starting processUnFlagContractAsPreCollectedSigningMonth for CPT ID: " + cpt.getId());

        // If current month payment that has worker salary is already paid
        List<Payment> l = paymentRepository.findPreCollectedOrMonthlyByContractAndStatusInAndDateOfPaymentBetween(
                cpt.getContract().getId(),
                Collections.singletonList(PaymentStatus.RECEIVED),
                startDate.toDate(),
                startDate.dayOfMonth().withMaximumValue().toDate());

        if (!l.isEmpty()) {

            addPreCollectedRefund(cpt.getContract(), l.get(0));

        } else {

            // Update Contract Payment Type Discount Effective After.
            ContractPaymentType type = cpt.getContractPaymentTypes()
                    .stream()
                    .filter(t -> PaymentHelper.isMonthlyPayment(t.getType()))
                    .collect(Collectors.toList()).get(0);
            logger.info("Monthly payment type ID: " + type.getId());

            if (type.getDiscountEffectiveAfter() == 1) {
                cpt.setDiscountEffectiveAfter(2);
                cpt = Setup.getRepository(ContractPaymentTermRepository.class).silentSave(cpt);
                logger.info("Updated CPT discount effective after to 2");
            }

            if (flowProcessorService.isPayingViaCreditCard(cpt.getContract())) {
                handlePayingViaCcFLows(cpt, startDate.toLocalDate(), true);

            } else {
                // Check if DD not exists before create new DD
                List<ContractPayment> creditCardPayments = cancelUpcomingDDA(cpt.getContract());

                ContractPaymentWrapper wrapper = contractPaymentConfirmationToDoService
                        .createWrapperForMonthlyPayment(cpt, new LocalDate(cpt.getContract()
                                .getStartOfContract()).plusMonths(1).withDayOfMonth(1).toDate(), new HashMap<String, Object>() {{
                                    put("ignoreWorkerSalary", true);
                        }});

                creditCardPayments.add(wrapper.initContractPaymentPropsWithTerm(cpt));


                creditCardPayments = creditCardPayments.stream().filter(cp -> !proceedPayments.contains(
                        new LocalDate(cp.getDate()).toString("yyyy-MM") + "_" + cp.getPaymentType().getCode()))
                        .collect(Collectors.toList());

                unpaidOnlineCreditCardPaymentService
                        .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                                creditCardPayments,
                                cpt);
            }

            addWaivedPreCollected(cpt, startDate.toLocalDate());
        }
    }

    public void processUnFlagContractAsPreCollectedCurrentMonth(
            ContractPaymentTerm cpt, LocalDate startDate, LocalDate discountStartDate, Set<String> proceedPayments) {

        logger.info("Starting processUnFlagContractAsPreCollectedCurrentMonth for CPT ID: " + cpt.getId());

        if (discountStartDate.equals(startDate) ||
                new LocalDate().isAfter(discountStartDate.dayOfMonth().withMaximumValue()) ||
                    QueryService.existsEntity(Payment.class, "e.contract.id = :p0 and e.typeOfPayment.code = 'monthly_payment' " +
                                "and e.status = 'RECEIVED' and e.methodOfPayment = :p1 and e.amountOfPayment = 0 and e.dateOfPayment between :p2 and :p3",
                        new Object[]{cpt.getContract().getId(), PaymentMethod.ADJUSTMENT,
                                startDate.toDate(), startDate.dayOfMonth().withMaximumValue().toDate()})) {

            List<Payment> l = paymentRepository.findPreCollectedOrMonthlyByContractAndStatusInAndDateOfPaymentBetween(
                    cpt.getContract().getId(),
                    Collections.singletonList(PaymentStatus.RECEIVED),
                    discountStartDate.toDate(),
                    discountStartDate.dayOfMonth().withMaximumValue().toDate());

            if (!l.isEmpty()) {
                addPreCollectedRefund(cpt.getContract(), l.get(0));
                return;
            }
        }

        if (new LocalDate().isAfter(discountStartDate.dayOfMonth().withMaximumValue())) {
            if (paymentService.isPaymentReceivedByCountPaymentsAndRefunds(cpt.getContract(),
                    null,
                    PicklistHelper.getItem("TypeOfPayment", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE),
                    startDate)) {

                // current month received -> check if there's refund for this payment
                List<Payment> l = paymentRepository.findPreCollectedOrMonthlyByContractAndStatusInAndDateOfPaymentBetween(
                        cpt.getContract().getId(),
                        Collections.singletonList(PaymentStatus.RECEIVED),
                        startDate.toDate(),
                        startDate.dayOfMonth().withMaximumValue().toDate());

                if (!l.isEmpty()) {
                    Payment currentMonthPayment = l.get(0);

                    // Check if there's a refund for this payment
                    if (clientRefundTodoRepository.existsByRelatedPaymentId(currentMonthPayment.getId())) {
                        // Has refund -> get pre-collected payment and refund it
                        List<Payment> preCollectedPayments = paymentRepository.findPreCollectedOrMonthlyByContractAndStatusInAndDateOfPaymentBetween(
                                cpt.getContract().getId(),
                                Collections.singletonList(PaymentStatus.RECEIVED),
                                discountStartDate.toDate(),
                                discountStartDate.dayOfMonth().withMaximumValue().toDate());

                        addPreCollectedRefund(cpt.getContract(), preCollectedPayments.get(0));

                    } else {
                        // No refund -> refund the current month payment
                        addPreCollectedRefund(cpt.getContract(), currentMonthPayment);
                    }
                    return;
                }
            }
        }

        if (flowProcessorService.isPayingViaCreditCard(cpt.getContract())) {
            handlePayingViaCcFLows(cpt, startDate, false);
            logger.info("Adding waived pre-collected payment");
            addWaivedPreCollected(cpt, startDate);
            return;
        }

        List<Payment> l = paymentRepository.findPreCollectedOrMonthlyByContractAndStatusInAndDateOfPaymentBetween(
                cpt.getContract().getId(),
                Arrays.asList(PaymentStatus.BOUNCED, PaymentStatus.PDC),
                startDate.toDate(),
                startDate.dayOfMonth().withMaximumValue().toDate());

        logger.info("Found " + l.size() + " bounced/PDC payments");
        List<ContractPayment> payments = new ArrayList<>();

        for (Payment payment : l) {
            logger.info("Handling bounced/PDC payment ID: " + payment.getId());
            payments.addAll(handleBouncedAndPdcPaymentCase(cpt, payment));
        }

        if (proceedPayments != null && !proceedPayments.isEmpty()) {
            payments = payments.stream()
                    .filter(cp -> !proceedPayments.contains(
                            new LocalDate(cp.getDate()).toString("yyyy-MM") + "_" +
                                    cp.getPaymentType().getCode()))
                    .collect(Collectors.toList());
        }

        unpaidOnlineCreditCardPaymentService
                .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                        contractPaymentService.getUniqueAndSortedPayments(cpt.getContract(), payments, null),
                        cpt);
    }

    public void addPreCollectedRefund(Contract contract, Payment p) {

        logger.info("add PreCollectedRefund Payment ID: " + p.getId());

        clientMessagingAndRefundService.addClientRefund(
                contract,
                contract.getClient(),
                p.getWorkerSalary(),
                ClientRefundRequestType.ERP,
                contract.isWorkerSalaryVatted() ?
                        PARAMETER_PRE_COLLECTED_SALARY_PAYMENT_REQUEST_PURPOSE :
                        PARAMETER_PRE_COLLECTED_SALARY_PAYMENT_WITHOUT_VAT_REQUEST_PURPOSE,
                p,
                "Payment Received Event Flow");
    }

    public void addWaivedPreCollected(ContractPaymentTerm cpt, LocalDate date) {
        // get pre-collected type from picklist item (type of payment).
        ContractPaymentType preCollectedPaymentType = cpt.getContractPaymentTypes().stream()
                .filter(type -> PaymentHelper.isPreCollectedPayment(type.getType()))
                .findFirst().orElse(null);

        paymentService.addWaivedPayment(
                cpt,
                date.toDate(),
                paymentService.getWaivedPayment(cpt.getContract(), preCollectedPaymentType.getType()),
                preCollectedPaymentType.getType(),
                preCollectedPaymentType.getDescription(),
                new HashMap<String, Object>() {{
                    put("methodOfPayment", PaymentMethod.ADJUSTMENT);
                }});
    }

    public List<ContractPayment> handleBouncedAndPdcPaymentCase(ContractPaymentTerm cpt, Payment payment) {

        if (checkWaitingBankForPreCollectedForPayment(payment)) return new ArrayList<>();

        List<ContractPayment> creditCardPayments = new ArrayList<>();
        if (payment.getDirectDebit() != null) {
            DirectDebit dd = directDebitRepository.findOne(payment.getDirectDebit().getId());
            if (dd != null && payment.getDirectDebit().getCategory().equals(DirectDebitCategory.A)) {

                if (dd.getContractPayments() != null &&
                        !dd.getContractPayments().isEmpty() && dd.getContractPayments().size() > 1) {

                    creditCardPayments = dd.getContractPayments()
                            .stream().filter(cp -> !PaymentHelper.isMonthlyPayment(cp.getPaymentType()))
                            .peek(cp -> cp.setGeneratedPaymentId(null))
                            .collect(Collectors.toList());
                }

                Setup.getApplicationContext()
                        .getBean(DirectDebitCancellationService.class)
                        .cancelWholeDD(dd, DirectDebitCancellationToDoReason.MANUALLY_FROM_ERP);
            }
        }

        payment.setStatus(PaymentStatus.DELETED);
        paymentService.updatePaymentSilent(payment);

        logger.info("Stopped bouncing flow for payment ID: " + payment.getId());

        ContractPaymentWrapper wrapper = contractPaymentConfirmationToDoService
                .createWrapperForMonthlyPayment(cpt, payment.getDateOfPayment());

        creditCardPayments.add(wrapper.initContractPaymentPropsWithTerm(cpt));

        // creditCardPayments
        return creditCardPayments;
    }

    private boolean checkWaitingBankForPreCollectedForPayment(Payment payment) {

        if (payment.hasBaseAdditionalInfo("waitingBankResponseForPreCollectedSalary")) {
            payment.removeBaseAdditionalInfo("waitingBankResponseForPreCollectedSalary");
            return false;
        } else if (payment.getDirectDebitFile() != null) {
            DirectDebitFile ddf = Setup.getRepository(DirectDebitFileRepository.class).findOne(payment.getDirectDebitFile().getId());
            if (ddf != null && ((payment.getStatus().equals(PaymentStatus.PDC) &&
                    ddf.getDdMethod() != null &&
                    ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) &&
                    ddf.getStatus().equals(DirectDebitFileStatus.APPROVED)) ||
                    (ddf.getDdMethod() != null &&
                            ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                            payment.getSentToBankByMDD()))) {
                logger.info("Waiting for bank response for pre-collected salary Payment ID: " + payment.getId());

                payment.addBaseAdditionalInfo("waitingBankResponseForPreCollectedSalary", "true");
                paymentService.updatePaymentSilent(payment);
                return true;
            }
        }

        return false;
    }

    public void processUnFlagContractAsPreCollectedRecurringPayerCase(ContractPaymentTerm cpt, DateTime startDate) {

        List<Payment> payments = paymentRepository.
                findByContractAndStatusAndDateOfPaymentBetweenAndRecurring(
                        cpt.getContract().getId(),
                        PaymentStatus.PDC,
                        new LocalDate(startDate).toDate(),
                        new LocalDate(startDate).dayOfMonth().withMaximumValue().toDate());

        if (payments.isEmpty()) return;
        Payment payment = payments.get(0);

        logger.info("Found PDC payments: " + payments.get(0).getId());

        payment.setStatus(PaymentStatus.DELETED);
        paymentService.updatePaymentSilent(payment);
    }

    public void handlePayingViaCcFLows(ContractPaymentTerm cpt, LocalDate startDate, boolean isSigningMonth) {

        FlowProcessorEntity flow = flowProcessorService.getRunningFlow(
                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                Arrays.asList(FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDA,
                        FlowSubEventConfig.FlowSubEventName.INITIAL_FLOW_FOR_DDB),
                cpt.getContract(), null);

        processInitialFlow(flow, startDate, isSigningMonth);

        if (flow == null) {
            logger.info("Not an initial flow -> check for extension flow");
            flow = flowProcessorService.getFirstRunningFlow(cpt.getContract(),
                    FlowEventConfig.FlowEventName.EXTENSION_FLOW);

            processExtensionFlow(flow, isSigningMonth);
        }

        if (flow == null) {
            logger.info("Not an extension flow -> check for IPAM flow");
            flow = flowProcessorService.getFirstRunningFlow(cpt.getContract(),
                    FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED);

            if (flow == null) {
                logger.info("Not an IPAM flow -> check for monthly reminder flow");
                flow = flowProcessorService.getRunningFlow(
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        Collections.singletonList(FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER),
                        cpt.getContract(), null);
            }
        }

        if (flow != null && flow.getContractPaymentConfirmationToDo() != null) {

            List<Payment> payments = paymentRepository
                    .findByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                            cpt.getContract(), PaymentStatus.RECEIVED, "monthly_payment",
                            new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                            new LocalDate().plusMonths(1).dayOfMonth().withMaximumValue().toDate());

            if (!payments.isEmpty()) {
                addPreCollectedRefund(cpt.getContract(), payments.get(0));
                return;
            }

            LocalDate paymentDate = isSigningMonth ?
                    // discount month
                    new LocalDate(calculateDiscountsWithVatService
                            .getDiscountStartDateInMillis(cpt)) :
                    new LocalDate().plusMonths(1).withDayOfMonth(1);

            logger.info("paymentDate: " + paymentDate.toString("yyyy-MM-dd"));

            ContractPaymentWrapper w =  flow.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                    .filter(wr -> PaymentHelper.isMonthlyPayment(wr.getPaymentType()) &&
                            wr.isIncludeWorkerSalary() &&
                            new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                    .equals(paymentDate.toString("yyyy-MM")))
                    .findFirst().orElse(null);

            if (w != null) {

                flow.getContractPaymentConfirmationToDo().setDisabled(true);
                contractPaymentConfirmationToDoRepository.save(flow.getContractPaymentConfirmationToDo());

                ContractPaymentWrapper newWrapper = contractPaymentConfirmationToDoService
                        .createWrapperForMonthlyPayment(cpt, paymentDate.toDate(), new HashMap<String, Object>() {{
                            put("ignoreWorkerSalary", true);
                        }});

                logger.info("New Wrapper created: Amount: " + newWrapper.getAmount() +
                        "; Date: " + new LocalDate(newWrapper.getPaymentDate()).toString("yyyy-MM-dd") +
                        "; Type: " + newWrapper.getPaymentType().getName());

                List<ContractPaymentWrapper> oldWrappers = flow.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                        .filter(wr -> !PaymentHelper.isMonthlyPayment(wr.getPaymentType()) ||
                                !wr.isIncludeWorkerSalary() ||
                                !new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                        .equals(paymentDate.toString("yyyy-MM")))
                        .collect(Collectors.toList());

                oldWrappers.forEach(wr -> wr.setId(null));
                oldWrappers.add(newWrapper);

                ContractPaymentConfirmationToDo todo = createMonthlyCreditCard(
                        cpt,
                        flow.getContractPaymentConfirmationToDo().getSource(),
                        oldWrappers);

                flow.setContractPaymentConfirmationToDo(todo);
                flowProcessorEntityRepository.save(flow);
            }
        }
    }

    public void processInitialFlow(FlowProcessorEntity flow, LocalDate startDate, boolean isSigningMonth) {

        if (flow == null) return;

        if (flow.getContractPaymentConfirmationToDo() == null) return;

        // Get the contract payment term from the flow
        ContractPaymentTerm cpt = flow.getContractPaymentTerm();

        // Disable the existing payment confirmation
        flow.getContractPaymentConfirmationToDo().setDisabled(true);
        contractPaymentConfirmationToDoRepository.save(flow.getContractPaymentConfirmationToDo());

        ContractPaymentWrapper w =  flow.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                .filter(wr -> PaymentHelper.isMonthlyPayment(wr.getPaymentType()) &&
                        wr.isIncludeWorkerSalary() &&
                        ((isSigningMonth &&
                                (new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                        .equals(new LocalDate(flow.getContract().getStartOfContract()).toString("yyyy-MM")) ||
                                new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                                .equals(new LocalDate(flow.getContract().getStartOfContract()).plusMonths(1)
                                                        .toString("yyyy-MM")))) ||
                         (!isSigningMonth && new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                .equals(new LocalDate().toString("yyyy-MM")))))
                .findFirst().orElse(null);

        if (w != null) {

            List<ContractPaymentWrapper> wrappers = flow.getContractPaymentConfirmationToDo()
                    .getContractPaymentList();

            boolean coverCurrentAndNextMonth = false;
            List<ContractPaymentWrapper> oldWrappers = new ArrayList<>();

            if (isSigningMonth) {

                boolean hasCurrentMonth = false;
                boolean hasNextMonth = false;

                List<ContractPaymentWrapper> filtered = new ArrayList<>();
                for (ContractPaymentWrapper wr : wrappers) {
                    if (PaymentHelper.isMonthlyPayment(wr.getPaymentType()) &&
                            wr.isIncludeWorkerSalary()) {
                        if (new LocalDate(wr.getPaymentDate()).toString("yyyy-MM").equals(
                                new LocalDate(flow.getContract().getStartOfContract()).toString("yyyy-MM"))) {
                            hasCurrentMonth = true;
                            filtered.add(wr);
                        } else if (new LocalDate(wr.getPaymentDate()).toString("yyyy-MM").equals(startDate.toString("yyyy-MM"))) {
                            hasNextMonth = true;
                            filtered.add(wr);
                        }
                    } else {
                        oldWrappers.add(wr);
                    }
                }

                coverCurrentAndNextMonth = hasCurrentMonth && hasNextMonth;

            } else {

                oldWrappers = wrappers.stream()
                        .filter(wr ->
                                !PaymentHelper.isMonthlyPayment(wr.getPaymentType()) ||
                                        !wr.isIncludeWorkerSalary() ||
                                        !new LocalDate(wr.getPaymentDate()).toString("yyyy-MM").equals(startDate.toString("yyyy-MM"))
                                ).collect(Collectors.toList());
            }

            // has monthly with worker salary.
            if (isSigningMonth) {

                Map<String, Object> ignoreWorkerSalaryMap = new HashMap<>();
                ignoreWorkerSalaryMap.put("ignoreWorkerSalary", true);

                if (coverCurrentAndNextMonth) {
                    // Create wrapper for the current month (next month after signing)
                    oldWrappers.add(contractPaymentConfirmationToDoService
                            .createWrapperForMonthlyPayment(cpt, new LocalDate().toDate(), ignoreWorkerSalaryMap));
                }

                oldWrappers.add(contractPaymentConfirmationToDoService
                        .createWrapperForMonthlyPayment(cpt, new LocalDate().plusMonths(1).toDate(), ignoreWorkerSalaryMap));

            } else {

                oldWrappers.add(contractPaymentConfirmationToDoService
                        .createWrapperForMonthlyPayment(cpt, new LocalDate().toDate(), new HashMap<String, Object>() {{
                            put("ignoreWorkerSalary", true);
                        }}));

            }

            oldWrappers.forEach(wr -> wr.setId(null)); // Reset IDs for new entities

            // Create new credit card payment confirmation
            ContractPaymentConfirmationToDo todo = createMonthlyCreditCard(
                    cpt,
                    flow.getContractPaymentConfirmationToDo().getSource(),
                    oldWrappers);

            // Update the flow with the new payment confirmation
            flow.setContractPaymentConfirmationToDo(todo);
            flowProcessorEntityRepository.save(flow);
        }
    }

    public void processExtensionFlow(FlowProcessorEntity flow, boolean isSigningMonth) {

        if (flow == null) return;

        if (flow.getContractPaymentConfirmationToDo() == null) return;

        if (isSigningMonth) return;

        ContractPaymentWrapper w =  flow.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                .filter(wr -> PaymentHelper.isMonthlyPayment(wr.getPaymentType()) &&
                        new LocalDate(wr.getPaymentDate()).toString("yyyy-MM")
                                .equals(new LocalDate().toString("yyyy-MM")))
                .findFirst().orElse(null);

        if (w != null) {
            flow.getContractPaymentConfirmationToDo().setDisabled(true);
            contractPaymentConfirmationToDoRepository.save(flow.getContractPaymentConfirmationToDo());

            ContractPaymentWrapper newWrapper = contractPaymentConfirmationToDoService
                    .createWrapperForMonthlyPayment(flow.getActiveCpt(), new LocalDate().toDate(), new HashMap<String, Object>() {{
                        put("ignoreWorkerSalary", true);
                    }});

            logger.info("New Wrapper created: Amount: " + newWrapper.getAmount() +
                    "; Date: " + new LocalDate(newWrapper.getPaymentDate()).toString("yyyy-MM-dd") +
                    "; Type: " + newWrapper.getPaymentType().getName());

            List<ContractPaymentWrapper> oldWrappers = flow.getContractPaymentConfirmationToDo().getContractPaymentList().stream()
                    .filter(wr -> !PaymentHelper.isMonthlyPayment(wr.getPaymentType()) ||
                            !new LocalDate(wr.getPaymentDate()).toString("yyyy-MM").equals(new LocalDate().toString("yyyy-MM")))
                    .collect(Collectors.toList());

            oldWrappers.forEach(wr -> wr.setId(null));
            oldWrappers.add(newWrapper);

            ContractPaymentConfirmationToDo todo = createMonthlyCreditCard(
                    flow.getActiveCpt(),
                    flow.getContractPaymentConfirmationToDo().getSource(),
                    oldWrappers);

            flow.setContractPaymentConfirmationToDo(todo);
            flowProcessorEntityRepository.save(flow);
        }

    }

    public ContractPaymentConfirmationToDo createMonthlyCreditCard(
            ContractPaymentTerm cpt,
            ContractPaymentConfirmationToDo.Source source,
            List<ContractPaymentWrapper> wrappers) {

        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
        todo.setContractPaymentTerm(cpt);
        todo.setSource(source);
        todo.setPaymentType(Setup.getItem("TypeOfPayment", "monthly_payment"));
        todo.setPaymentMethod(PaymentMethod.CARD);
        todo.setPayingOnline(true);

        wrappers.forEach(w -> {
            w.setContractPaymentConfirmationToDo(todo);
            todo.getContractPaymentList().add(w);
        });

        contractPaymentConfirmationToDoService.createConfirmationToDo(todo);

        logger.info("todo id: " + todo.getId());

        return contractPaymentConfirmationToDoRepository.findOne(todo.getId());
    }
}
