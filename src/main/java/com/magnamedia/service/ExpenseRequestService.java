package com.magnamedia.service;

import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails;
import com.magnamedia.repository.ExpensePaymentRepository;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.helper.AttachmentHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import com.magnamedia.entity.dto.ExpenseRequestRefundSearchDto;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import org.joda.time.DateTime;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import org.apache.commons.lang3.BooleanUtils;

/**
 * Utility service for handling expense confirmation triggers
 * This service contains common logic that can be reused across different controllers
 */
@Service
public class ExpenseRequestService {

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;

    /**
     * Handles the after confirmation trigger logic for expense requests
     * This method updates the expense request todo and associated payment with transaction details
     * 
     * @param expenseRequestTodo The expense request todo to update
     * @param matchType The type of match (EXISTING_EXPENSE, AUTO_DEDUCTED, REFUND)
     * @param transaction The transaction that was created
     */
    @Transactional
    public void afterConfirmationTrigger(ExpenseRequestTodo expenseRequestTodo, 
                                       CreditCardReconciliationStatementDetails.MatchType matchType, 
                                       Transaction transaction) {
        if (expenseRequestTodo != null) {
            expenseRequestTodo.setAmountInLocalCurrency(transaction.getAmount());
            expenseRequestTodo.setVatAmount(transaction.getVatAmount());
            
            if (matchType.equals(CreditCardReconciliationStatementDetails.MatchType.REFUND)) {
                expenseRequestTodo.setRefundConfirmed(true);
            }

            if (Arrays.asList(CreditCardReconciliationStatementDetails.MatchType.EXISTING_EXPENSE, 
                            CreditCardReconciliationStatementDetails.MatchType.AUTO_DEDUCTED).contains(matchType)) {
                expenseRequestTodo.setConfirmed(true);
            }

            ExpensePayment expensePayment = expenseRequestTodo.getExpensePayment();
            if (expensePayment != null) {
                expensePayment.setCallUpdateAmountsTrigger(Boolean.FALSE);
                expensePayment.setLocalCurrencyAmount(expenseRequestTodo.getAmountInLocalCurrency());
                expensePayment.setVatAmount(transaction.getVatAmount());
                expensePayment.setTransaction(transaction);
                expensePayment.setConfirmed(true);
                expensePaymentRepository.save(expensePayment);
            }

            expenseRequestTodoRepository.save(expenseRequestTodo);
        }
    }

    /**
     * Handles the reconciliator confirmation step logic for expense payments
     * This method centralizes the reconciliation logic that was previously in ExpensePaymentInReconciliatorConfirmationStep
     * 
     * @param expensePayment The expense payment to reconcile
     */
    @Transactional
    public void reconciliatorConfirmationStep(ExpensePayment expensePayment) {
        expensePayment.setCompleted(Boolean.TRUE);
        
        // Check if invoice is missing to determine status
        if (isInvoiceMissingOrTaxInvoiceMissing(expensePayment)) {
            expensePayment.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
        } else {
            expensePayment.setStatus(ExpensePaymentStatus.PAID);
        }
        
        expensePayment.setConfirmed(Boolean.TRUE);
        expensePayment.setMissingVatInvoice(isMissingVatInvoice(expensePayment));

        confirmRequests(expensePayment);
    }

    /**
     * Checks if invoice or tax invoice is missing for the expense payment
     * This method replicates the logic from ExpensePaymentInPendingPaymentCashierStep.isInvoiceMessingOrTaxInvoiceMessing
     * 
     * @param expensePayment The expense payment to check
     * @return true if invoice or tax invoice is missing, false otherwise
     */
    public boolean isInvoiceMissingOrTaxInvoiceMissing(ExpensePayment expensePayment) {
        if (expensePayment.getRequiresInvoice() == null) return false;
        if (expensePayment.getRequiresInvoice().equals(Boolean.FALSE)) return false;

        Attachment invoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        if (invoice == null) return true;

        if (expensePayment.getTaxable() == null) return true;
        if (expensePayment.getTaxable().equals(Boolean.FALSE)) return false;

        if (expensePayment.getVatAmount() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice().equals(Boolean.TRUE)) return false;

        Attachment vatInvoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        if (vatInvoice == null) return true;

        return false;
    }

    /**
     * Checks if VAT invoice is missing for the expense payment
     * 
     * @param expensePayment The expense payment to check
     * @return true if VAT invoice is missing, false otherwise
     */
    public boolean isMissingVatInvoice(ExpensePayment expensePayment) {
        boolean missingVatInvoice = false;
        if (expensePayment.getTaxable() == null) {
            missingVatInvoice = true;
        } else if (expensePayment.getTaxable().equals(Boolean.TRUE)) {
            if (expensePayment.getVatAmount() == null || expensePayment.getVatAmount().equals(0D)) {
                missingVatInvoice = true;
            } else {
                Attachment vatInvoice = expensePayment.getAttachment(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
                if (vatInvoice == null) {
                    missingVatInvoice = true;
                }
            }
        }
        return missingVatInvoice;
    }

    /**
     * Confirms all expense request todos associated with the expense payment
     * 
     * @param expensePayment The expense payment
     */
    public void confirmRequests(ExpensePayment expensePayment) {
        List<ExpenseRequestTodo> todos = expenseRequestTodoRepository.findByExpensePayment(expensePayment);
        if (todos != null && !todos.isEmpty()) {
            for (ExpenseRequestTodo todo : todos) {
                todo.setConfirmed(Boolean.TRUE);
                todo.setAmount(expensePayment.getAmount());
                todo.setLoanAmount(expensePayment.getLoanAmount());
                expenseRequestTodoRepository.save(todo);
            }
        }
    }

    /**
     * Builds a SelectQuery for searching refund requests based on the given filter DTO.
     * This centralises the filtering logic so controllers can reuse it.
     */
    public SelectQuery<ExpenseRequestTodo> buildRefundSearchQuery(ExpenseRequestRefundSearchDto search) {
        SelectQuery<ExpenseRequestTodo> query = new SelectQuery<>(ExpenseRequestTodo.class);
        query.leftJoin("expense");
        query.leftJoin("expensePayment");
        // All refunds only
        query.filterBy("isRefunded", "=", true);

        if (search == null) return query; // nothing to filter

        if (search.getRefundId() != null) {
            query.filterBy("id", "=", search.getRefundId());
        }
        if (search.getExpenseId() != null) {
            query.filterBy("expense.id", "=", search.getExpenseId());
        }
        if (search.getRefundConfirmed() != null) {
            SelectFilter sf = new SelectFilter("refundConfirmed", "=", search.getRefundConfirmed());
            if (!BooleanUtils.toBoolean(search.getRefundConfirmed())) {
                sf = sf.or("refundConfirmed", "IS NULL", null);
            }
            query.filterBy(sf);
        }
        if (search.getExpenseAmount() != null && search.getExpenseAmountOperator() != null) {
            query.filterBy("amount", search.getExpenseAmountOperator(), search.getExpenseAmount());
        }
        if (search.getRefundAmount() != null && search.getRefundAmountOperator() != null) {
            query.filterBy("refundAmount", search.getRefundAmountOperator(), search.getRefundAmount());
        }
        if (search.getSupplierId() != null) {
            query.filterBy("beneficiaryType", "=", ExpenseBeneficiaryType.SUPPLIER);
            query.filterBy("beneficiaryId", "=", search.getSupplierId());
        }
        if (search.getRelatedToType() != null) {
            query.filterBy("relatedToType", "=", search.getRelatedToType());

            if (search.getRelatedToId() == null) {
                switch (search.getRelatedToType()) {
                    case MAID:
                        throw new RuntimeException("Please fill the proper MAID in 'Related to' search field");
                    case TEAM:
                        throw new RuntimeException("Please fill the proper TEAM in 'Related to' search field");
                    case OFFICE_STAFF:
                        throw new RuntimeException("Please fill the proper OFFICE STAFF in 'Related to' search field");
                    case APPLICANT:
                        throw new RuntimeException("Please fill the proper APPLICANT in 'Related to' search field");
                    default:
                        break;
                }
            }
            query.filterBy("relatedToId", "=", search.getRelatedToId());
        }
        // Expense date range / operator
        if (search.getExpenseDate1() != null && search.getExpenseDateOperator() != null && search.getExpenseDate2() == null) {
            if ("=".equals(search.getExpenseDateOperator())) {
                query.filterBy("creationDate", ">=", new DateTime(search.getExpenseDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("creationDate", "<", new DateTime(search.getExpenseDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else {
                query.filterBy("creationDate", search.getExpenseDateOperator(), search.getExpenseDate1());
            }
        } else if (search.getExpenseDate1() != null && search.getExpenseDate2() != null) {
            query.filterBy("creationDate", ">=", search.getExpenseDate1());
            query.filterBy("creationDate", "<=", search.getExpenseDate2());
        }
        // Refund date range
        if (search.getRefundDate1() != null && search.getRefundDateOperator() != null && search.getRefundDate2() == null) {
            if ("=".equals(search.getRefundDateOperator())) {
                query.filterBy("refundDate", ">=", new DateTime(search.getRefundDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("refundDate", "<", new DateTime(search.getRefundDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else {
                query.filterBy("refundDate", search.getRefundDateOperator(), search.getRefundDate1());
            }
        } else if (search.getRefundDate1() != null && search.getRefundDate2() != null) {
            query.filterBy("refundDate", ">=", search.getRefundDate1());
            query.filterBy("refundDate", "<=", search.getRefundDate2());
        }
        // MC-215
        if (search.getExpenseTransaction() != null) {
            query.filterBy("expensePayment.transaction.id", "=", search.getExpenseTransaction());
        }

        if (search.getExpenseName() != null) {
            query.filterBy("expense.name", "like", "%" + search.getExpenseName() + "%");
        }
        return query;
    }
} 