package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingPaymentStructure;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Service
public class UnpaidOnlineCreditCardPaymentService {
    private static final Logger logger = Logger.getLogger(UnpaidOnlineCreditCardPaymentService.class.getName());

    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;

    public FlowProcessorEntity createPaymentReminderFlow(ContractPaymentConfirmationToDo toDo, ContractPaymentTerm cpt) {
        return createPaymentReminderFlow(toDo, cpt, new Date());
    }

    public FlowProcessorEntity createPaymentReminderFlow(ContractPaymentConfirmationToDo toDo, ContractPaymentTerm cpt, Date executionDate) {
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);

        FlowSubEventConfig flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(
                        toDo.isRequired() ?
                                FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS :
                                FlowSubEventConfig.FlowSubEventName.PENDING_PAYMENT,
                        flowEventConfig);

        Map<String, Object> map = new HashMap<>();
        map.put("trials", 1);
        map.put("reminders", 0);
        map.put("lastExecutionDate", new DateTime(executionDate).withHourOfDay(10)
                .withMinuteOfHour(0)
                .withSecondOfMinute(0)
                .toDate());
        map.put("todo", toDo);
        FlowProcessorEntity f = flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        logger.info("flowProcessorEntity id: " + (f == null ? "null" : f.getId()));
        if (f == null) return null;

        f = flowProcessorEntityRepository.findOne(f.getId());
        f.setContractPaymentConfirmationToDo(toDo);
        f = flowProcessorEntityRepository.save(f);

        flowProcessorService.processFlowSubEventConfig(f);

        return f;
    }

    public void stopRunningFlows(ContractPaymentConfirmationToDo todo) {
        logger.log(Level.INFO, "todo ID: {0}", todo.getId());
        flowProcessorEntityRepository.findByContractPaymentConfirmationToDoAndStoppedFalseAndCompletedFalse(todo)
                .forEach(f -> {
                    logger.log(Level.INFO, "flow ID: {0}", f.getId());
                    f.setStopped(true);
                    flowProcessorEntityRepository.save(f);
                });
    }

    @Transactional
    public void stopFlowAfterPaymentStatusChanged(ContractPaymentConfirmationToDo todo) throws Exception {
        logger.log(Level.INFO, "todo ID: {0}", todo.getId());
        stopRunningFlows(todo);

        retractContractTermination(todo);
    }

    public void retractContractTermination(ContractPaymentConfirmationToDo t) {
        logger.info("toDo id: " + t.getId());

        if (!t.isRequired() ||
                !t.getContractPaymentTerm().getContract().getStatus().equals(ContractStatus.ACTIVE) ||
                t.getContractPaymentTerm().getContract().getScheduledDateOfTermination() == null ||
                t.getContractPaymentTerm().getContract().getReasonOfTerminationList() == null ||
                !t.getContractPaymentTerm().getContract().getReasonOfTerminationList().getCode()
                        .equals(Contract.ONLINE_REMINDER_TERMINATION_REASON)) return;

        SelectQuery<FlowProcessorEntity> q = new SelectQuery<>(FlowProcessorEntity.class);
        q.filterBy("contractPaymentConfirmationToDo", "=", t);
        q.filterBy("currentSubEvent.name", "=",
                FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS);
        q.filterBy("causedTermination", "=", true);
        q.setLimit(1);

        List<FlowProcessorEntity> l = q.execute();
        if (l.isEmpty()) return;

        reactivateOnlineReminder(t.getContractPaymentTerm().getContract(), t.getId());

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .retractContractTermination(t.getContractPaymentTerm().getContract());

        l.get(0).setCausedTermination(false);
        flowProcessorEntityRepository.save(l.get(0));
        logger.info("flow id: " + l.get(0).getId() + " one payment or more received -> reset the flow");
    }

    public void reactivateOnlineReminder(Long contractId) {
        logger.info("contract id: " + contractId);
        Contract c = Setup.getRepository(ContractRepository.class)
                .findOne(contractId);

        if (c.getStatus().equals(ContractStatus.ACTIVE) &&
                c.getScheduledDateOfTermination() != null &&
                c.getReasonOfTerminationList() != null &&
                c.getReasonOfTerminationList().getCode().equals(Contract.ONLINE_REMINDER_TERMINATION_REASON)) {

            Setup.getApplicationContext()
                    .getBean(ContractService.class)
                    .retractContractTermination(c);
        }

        reactivateOnlineReminder(c,null);
    }

    public void reactivateOnlineReminder(Contract c, Long todId) {
        logger.info("contract id: " + c.getId());

        List<Map<String, Object>> l = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class)
                .findValidOnlineReminder(c.getId(), todId);

        l.forEach(m -> {
            ContractPaymentConfirmationToDo t = (ContractPaymentConfirmationToDo) m.get("todo");

            logger.info("t id: " + t.getId());
            createPaymentReminderFlow(t, t.getContractPaymentTerm());

            FlowProcessorEntity f = (FlowProcessorEntity) m.get("flow");
            logger.info("flow id: " + f.getId());

            f.setCausedTermination(false);
            f.setStoppedDueContractTerminated(false);
            flowProcessorEntityRepository.save(f);
        });
    }

    public void messagingFilter(SelectFilter selectFilter, FlowProcessorEntity entity) {

        if (entity.getContractPaymentConfirmationToDo() == null)  return;

        selectFilter.and("paymentStructure", "=",
                entity.getContractPaymentConfirmationToDo().getContractPaymentList().size() > 1 ?
                        DDMessagingPaymentStructure.MULTIPLE_PAYMENTS :
                        DDMessagingPaymentStructure.ONE_PAYMENT);
    }

    public void reactivateReminderFlowAfterReActiveContract(ContractPaymentConfirmationToDo t) {
        logger.info("toDo id: " + t.getId());

        // filter payment received
        List<ContractPaymentWrapper> contractPaymentWrappers = contractPaymentConfirmationToDoService.getUnReceivedContractPaymentViaPayments(
                t.getContractPaymentList()
                        .stream()
                        .filter(w -> !PaymentHelper.isMonthlyPayment(w.getPaymentType()))
                        .collect(Collectors.toList()),
                t.getContractPaymentTerm().getContract(),
                ContractPaymentWrapper.class);
        logger.info("contractPaymentWrappers size: " + contractPaymentWrappers.size());

        // Disable ContractPaymentConfirmationToDo if it’s not
        if(!t.isDisabled()){
            t.setDisabled(true);
            Setup.getRepository(ContractPaymentConfirmationToDoRepository.class).saveAndFlush(t);
        }

        if(contractPaymentWrappers.isEmpty()) return;

        // remove conflict wrapper between confirmationTodo and matched confirmationTodo
        contractPaymentConfirmationToDoService.getMatchedToDoViaWrappers(t)
        .forEach(matchedToDo -> matchedToDo.getContractPaymentList()
                .forEach(w -> {
                    logger.info("wrapper id: " + w.getId());
                    contractPaymentWrappers.removeIf(wrapper -> wrapper.getAmount().equals(w.getAmount()) &&
                            wrapper.getPaymentType().getCode().equals(w.getPaymentType().getCode()) &&
                            wrapper.getPaymentDate().getTime() >= new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate().getTime() &&
                            wrapper.getPaymentDate().getTime() <= new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate().getTime());
                }));

        if (contractPaymentWrappers.isEmpty()) return;

        logger.info("contractPaymentWrappers size: " + contractPaymentWrappers.size());

        // create confirmationTodo for payment
        createConfirmationTodoAndStartReminderFlow(contractPaymentWrappers, t.getContractPaymentTerm().isActive() ?
                t.getContractPaymentTerm() : t.getContractPaymentTerm().getContract().getActiveContractPaymentTerm());
    }

    private void createConfirmationTodoAndStartReminderFlow(
            List<ContractPaymentWrapper> contractPaymentWrappers, ContractPaymentTerm cpt) {

        // get ContractPayment form contractPaymentWrappers
        List<ContractPayment> contractPayments = contractPaymentWrappers.stream()
                .map(ContractPaymentWrapper::initContractPaymentProps)
                .collect(Collectors.toList());
        logger.info("ContractPayments size: " + contractPayments.size());

        // non-monthly Payments
        createConfirmationTodoFromContractPaymentsAndStartReminderFlow(contractPayments, cpt);
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(List<ContractPayment> l, ContractPaymentTerm cpt) {

        return createConfirmationTodoFromContractPaymentsAndStartReminderFlow(l, cpt, new Date());
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
            List<ContractPayment> l, ContractPaymentTerm cpt, Date executionDate) {
        return createConfirmationTodoFromContractPaymentsAndStartReminderFlow(l, cpt, executionDate, new HashMap<>());
    }

    @Transactional
    public FlowProcessorEntity createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
            List<ContractPayment> l, ContractPaymentTerm cpt, Date executionDate, Map<String, Object> map) {
        if (l == null || l.isEmpty()) return null ;
        logger.info("cpt id: " + cpt.getId() + "; " + l.size());
        ContractPaymentConfirmationToDo todo = contractPaymentConfirmationToDoService.createConfirmationTodoFromContractPayments(
                cpt, l,
                ContractPaymentConfirmationToDo.Source.PAYMENT_REMINDER,
                map);

        return createPaymentReminderFlow(todo, cpt, executionDate);
    }

    public static boolean isRequired(List<ContractPayment> contractPayments) {

        if (contractPayments == null || contractPayments.isEmpty()) return false;
        if (contractPayments.stream().anyMatch(cp -> PaymentHelper.isMonthlyPayment(cp.getPaymentType()))) {
            return true;
        }

        String parameter = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_PAYMENT_TYPES_TO_BE_REQUIRED_WITHIN_THE_REMINDER_FLOW);

        List<String> codes = Arrays.stream(parameter.trim().split("[,;]"))
                .collect(Collectors.toList());


        return contractPayments.stream().anyMatch(cp -> codes.contains(cp.getPaymentType().getCode()));
    }

    public boolean shouldDelayTerminationMessageBeforePaidEndDate(FlowProcessorEntity flow) throws ParseException {

        logger.info("Flow Trials: " + flow.getTrials() + "; Flow reminders: " + flow.getReminders());

        // 1- Check Stop Flow
        if (flowProcessorService.validateFlowStopping(flow)) return true;

        // 2- Validation and Check
        if (!flow.getCurrentSubEvent().getName()
                .equals(FlowSubEventConfig.FlowSubEventName.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS)) return false;

        if (!flow.getCurrentSubEvent().isTerminateContractOnMaxReminders() ||
                flow.getReminders() < flow.getCurrentSubEvent().getMaxReminders() - 1) return false;

        FlowProgressPeriod flowProgressPeriod = flow.getCurrentSubEvent().getProgressPeriods()
                .stream()
                .filter(progress ->
                        flow.getCurrentSubEvent().getMaxTrials() == progress.getTrials() &&
                                flow.getCurrentSubEvent().getMaxReminders() == progress.getReminders())
                .findFirst()
                .orElse(null);

        return flowProcessorService.processFlowLastExecutionDateAndNextTrialSendDate(flow, flowProgressPeriod);
    }
}