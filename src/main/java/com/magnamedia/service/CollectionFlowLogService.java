package com.magnamedia.service;

import com.magnamedia.controller.CollectionFlowLogController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.ContractScheduleForTerminationUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.CmOngoingCollectionFlowsTemplates.CmOngoingCollectionFlowsTemplateCode;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 3/6/2022
 **/

@Service
public class CollectionFlowLogService {
    private static final Logger logger = Logger.getLogger(CollectionFlowLogService.class.getName());

    @Autowired
    private CollectionFlowLogRepository collectionFlowLogRepository;
    @Autowired
    private CollectionFlowJobHistoryRepository collectionFlowJobHistoryRepository;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private DirectDebitController directDebitController;
    @Autowired
    private GraphicDesignerTodoRepository graphicDesignerTodoRepository;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private CCAppContentService ccAppContentService;
    @Autowired
    private CCAppService ccAppService;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    public void generateCollectionFlowLog(
            String flowType, Contract contract, Long relatedToId, String relatedToEntity,
            Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes) {

        generateCollectionFlowLog(flowType, contract,  relatedToId, relatedToEntity, triggerDate, idleSinceDate,
                status, notes, false, false, null, null);
    }
    public void generateCollectionFlowLog(
            String flowType, Contract contract, Long relatedToId, String relatedToEntity,
            Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes,
            Long currentRunningFlowId, String currentRunningFlowEntity) {

        generateCollectionFlowLog(flowType, contract,  relatedToId, relatedToEntity, triggerDate, idleSinceDate,
                status, notes, false, false, currentRunningFlowId, currentRunningFlowEntity);

    }
    public void generateCollectionFlowLog(
            String flowType, Contract contract, Long relatedToId, String relatedToEntity,
            Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes,
            boolean ended, Boolean rejectionTodoIsRescheduledWhenEnded) {

        generateCollectionFlowLog(flowType, contract,  relatedToId, relatedToEntity, triggerDate, idleSinceDate,
                status, notes, ended, rejectionTodoIsRescheduledWhenEnded, null, null);

    }

    //this is the main function
    public void generateCollectionFlowLog(
            String flowType, Contract contract, Long relatedToId, String relatedToEntity,
            Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes,
            boolean ended, Boolean rejectionTodoIsRescheduledWhenEnded, Long currentRunningFlowId, String currentRunningFlowEntity) {
        CollectionFlowLog collectionFlowLog = new CollectionFlowLog(
                PicklistHelper.getItem(AccountingModule.PICKLIST_COLLECTION_FLOW_TYPE, flowType),
                contract,
                relatedToId,
                relatedToEntity,
                triggerDate,
                idleSinceDate,
                status,
                notes,
                currentRunningFlowId,
                currentRunningFlowEntity);

        User creator = null;
        if (flowType.equals(CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE)) {
            FlowProcessorEntity flowEntity = flowProcessorEntityRepository.findOne(relatedToId);
            if (flowEntity != null) {
                creator = flowEntity.getCreator();
            }
        } else if(flowType.equals(CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE)) {
            AccountingEntityProperty property = accountingEntityPropertyRepository.findByOriginAndKeyAndDeletedFalse(
                    relatedToId, "change_to_paying_via_credit_card_date");
            if (property != null) {
                creator = property.getCreator();
            }
        }

        // If creator is admin, set trigger user to ERP
        if (creator != null  &&
                !"admin".equalsIgnoreCase(creator.getLoginName()) &&
                !"guest".equalsIgnoreCase(creator.getLoginName())) {
            collectionFlowLog.setTriggerUser(creator.getFullName());
        }

        collectionFlowLog.setEnded(ended);
        collectionFlowLog.setRejectionTodoIsRescheduledWhenEnded(rejectionTodoIsRescheduledWhenEnded);
        collectionFlowLogRepository.silentSave(collectionFlowLog);
    }

    public void generateCollectionFlowLogForDdRejection(String flowType, Contract contract, DirectDebitRejectionToDo rejectionToDo) {
        String flowNotes = getCollectionFlowNotesFromRejectionCategory(rejectionToDo.getLastRejectCategory());
        DirectDebitRejectionToDoType rejectionToDoType = rejectionToDo.getTaskName() != null && !rejectionToDo.getTaskName().isEmpty() ? DirectDebitRejectionToDoType.valueOf(rejectionToDo.getTaskName()) : null;
        CollectionFlowStatus flowStatus = null;
        Date idleSince = null;
        DirectDebit directDebit = rejectionToDo.getLastDirectDebit();

        if (directDebit != null) {
            if (directDebit.getCategory().equals(DirectDebitCategory.A)) { // case DDA we only need M Status
                switch (directDebit.getMStatus()) {
                    case IN_COMPLETE:
                        flowStatus = hasRunningGraphicDesignerTodo(contract) ?
                                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                                CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                        break;
                    case PENDING_DATA_ENTRY:
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        break;
                    case PENDING:
                        flowStatus = directDebit.getDirectDebitFiles().stream()
                                .anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)) ?
                                CollectionFlowStatus.PENDING_BANK_RESPONSE :
                                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        break;
                    default:
                        break;
                }

                idleSince = directDebit.getLastModificationDate();
            } else { // case DDB we need to check both M Status and Status
                if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                    flowStatus = hasRunningGraphicDesignerTodo(contract) ?
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                            CollectionFlowStatus.PENDING_CLIENT_RESPONSE;

                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                } else if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                        directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {

                    boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) &&
                                    ddf.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = manualDDsSentToBank ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) &&
                        directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {

                    boolean automaticDDsSentToBank = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) &&
                                    ddf.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = automaticDDsSentToBank ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;

                } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING)) {
                    boolean fileSent = directDebit.getDirectDebitFiles().stream()
                            .anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT));

                    flowStatus = fileSent ? CollectionFlowStatus.PENDING_BANK_RESPONSE :
                            CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                }

                idleSince = directDebit.getLastModificationDate();
            }
        }

        if (flowStatus == null)
            flowStatus = getCollectionFlowStatusFromRejectionToDoType(rejectionToDoType);

        if (rejectionToDoType != null && rejectionToDoType.equals(DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B)) //if the current step is WAITING_RE_SCHEDULE_B consider the flow as ended
            generateCollectionFlowLog(flowType, contract, rejectionToDo.getId(), rejectionToDo.getEntityType(), rejectionToDo.getCreationDate(), idleSince != null ? idleSince : rejectionToDo.getCreationDate(), flowStatus, flowNotes, true, true);
        else
            generateCollectionFlowLog(flowType, contract, rejectionToDo.getId(), rejectionToDo.getEntityType(), rejectionToDo.getCreationDate(), idleSince != null ? idleSince : rejectionToDo.getCreationDate(), flowStatus, flowNotes);
    }

    // ACC-9074
    public void generateLogsBasedOnCollectionLogType() {
        long lastId = -1;
        Page<Object[]> p;
        do {
            p = collectionFlowLogRepository
                    .findFlowProcessorEntityWithoutLogs(lastId, Arrays.asList(
                            FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED.name(),
                            FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS.name(),
                            FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW.name(),
                            FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO.name(),
                            FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO.name(),
                            FlowEventConfig.FlowEventName.EXTENSION_FLOW.name()),
                            PageRequest.of(0, 100));
            //fetch all contracts in one query and put it in map to fetch the contract faster
            logger.info("result size : " + p.getContent().size());
            Map<Long, Contract> m = new HashMap<>();

            contractRepository.findAllById(p.getContent().stream()
                            .map(c -> Utils.parseValue(c[2], Long.class)).collect(Collectors.toList()))
                    .forEach(c -> m.put(c.getId(), c));

            p.getContent().forEach(f -> {
                try {
                    logger.info("flow id: " + f[0]);
                    generateCollectionFlowLog((String) f[3],
                            m.get(Utils.parseValue(f[2], Long.class)),
                            Utils.parseValue(f[0], Long.class),
                            (String) f[1], new Date(), new Date(),
                            CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = (long) p.getContent().get(p.getContent().size() - 1)[0];
            }
        } while (!p.getContent().isEmpty());
    }

    @Transactional //this must be transactional, so if any method failed then we set the collection job history record as failed and rollback all generated logs
    public void generateAllCollectionFlowLogs(CollectionFlowJobHistory collectionFlowJobHistory) {
        try {
            Date calculateFrom = collectionFlowJobHistory.getCalculateFrom();
            Date calculateTo = collectionFlowJobHistory.getCalculateTo();

            //1- Bounced Payments
            measureExecutionTime("generateBouncedPaymentsLogs", () -> generateBouncedPaymentsLogs(calculateFrom, calculateTo));

            //2- DD Rejection
            measureExecutionTime("generateDDREjectionsLogs", () -> generateDDRejectionLogs(calculateFrom, calculateTo));

            //3- Switching Nationality
            measureExecutionTime("generateSwitchingNationalityLogs", this::generateSwitchingNationalityLogs);

            //4- Switching Bank Info
            measureExecutionTime("generateSwitchingBankInfoLogs", this::generateSwitchingBankInfoLogs);

            //5- Refund
            measureExecutionTime("generateRefundLogs", this::generateRefundLogs);

            //6- Client paying via credit card
            measureExecutionTime("generateClientPayingViaCreditCardLogs", this::generateClientPayingViaCreditCardLogs);

            //7- One month agreement
            measureExecutionTime("generateOneMonthAgreementLogs", this::generateOneMonthAgreementLogs);

            //8- Merged Flows (After Cash & Reminder & Payment Expiry & Incomplete Missing Bank Info & Incomplete flow / Data entry rejection)
            measureExecutionTime("generateLogsBasedOnCollectionLogType", this::generateLogsBasedOnCollectionLogType);

            collectionFlowJobHistory.setStatus(CollectionJobStatus.SUCCEED);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Exception while generateAllCollectionFlowLogs for collectionFlowJobHistory id# " + collectionFlowJobHistory.getId() + " ," + e.getMessage());
            collectionFlowJobHistory.setStatus(CollectionJobStatus.FAILED);
            collectionFlowJobHistory.setError(e.getMessage());
        }

        collectionFlowJobHistoryRepository.save(collectionFlowJobHistory);
    }

    private void generateBouncedPaymentsLogs(Date calculateFrom, Date calculateTo) {
        long lastId = -1L;
        List<Object[]> l;
        do {
            l = paymentRepository.findByNewBouncedPaymentForCollectionFlowLogs(lastId, calculateFrom, calculateTo);
            l.forEach(object -> {
                Long paymentId = ((BigInteger) object[0]).longValue();
                logger.info("payment id: " + paymentId);
                generateBouncedPaymentsLogs(paymentId, (Date) object[1]);
            });
            if (!l.isEmpty()) {
                lastId = ((BigInteger) l.get(l.size() - 1)[0]).longValue();
            }
        } while (!l.isEmpty());
    }

    private void generateBouncedPaymentsLogs(Long paymentId, Date lastModificationDate) {
        Payment bouncedPayment = paymentRepository.findOne(paymentId);

        //if the contract is terminated don't start the flow
        PicklistItem reasonOfTermination = bouncedPayment.getContract().getReasonOfTerminationList();
        if (reasonOfTermination != null &&
                ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment(reasonOfTermination.getCode()) &&
                (bouncedPayment.getContract().getIsScheduledForTermination() ||
                        bouncedPayment.getContract().getScheduledDateOfTermination() != null ||
                        bouncedPayment.getContract().getDateOfTermination() != null)) return;

        //the payment became bounced during our time frame
        Date idleSince;
        CollectionFlowStatus flowStatus;
        DirectDebit directDebit = bouncedPayment.getDirectDebit();
        List<Object[]> ddfs = directDebit != null ?
                directDebitFileRepository.selectByDirectDebitAndDdMethod(directDebit, DirectDebitMethod.MANUAL) :
                new ArrayList<>();

        if (ddfs.size() > 0) {
            //check the status of related DD
            Map<String,Object> result = checkStatusOfRelatedDD(ddfs, bouncedPayment, directDebit);
            idleSince = (Date) result.get("idleSince");
            flowStatus = (CollectionFlowStatus) result.get("flowStatus");
        } else { // there is no manual DD, then we still need to create a new DD
            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
            idleSince = lastModificationDate;
        }

        generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_BOUNCED_PAYMENT_FLOW_CODE,
                bouncedPayment.getContract(), bouncedPayment.getId(),
                bouncedPayment.getEntityType(), lastModificationDate, idleSince, flowStatus, "");
    }

    public void generateDDRejectionLogs(Date calculateFrom, Date calculateTo) {
        long lastId = -1;
        Page<DirectDebitRejectionToDo> p;
        do {
            p = directDebitRejectionToDoRepository.findInCompletedTodos(lastId, calculateFrom, calculateTo, PageRequest.of(0, 100));

            for (DirectDebitRejectionToDo rejectionToDo : p.getContent()) {
                Contract contract = rejectionToDo.getLastDirectDebit() != null ?
                        rejectionToDo.getLastDirectDebit().getContractPaymentTerm().getContract() :
                        null;
                if (contract == null) {
                    DirectDebit dd = directDebitRepository.findFirstByDirectDebitRejectionToDo(rejectionToDo);
                    contract = dd.getContractPaymentTerm().getContract();
                }

                logger.info("todo id: " + rejectionToDo.getId());

                generateCollectionFlowLogForDdRejection(
                        CollectionFlowLogController.PICKLISTITEM_DD_REJECTION_FLOW_CODE, contract, rejectionToDo);
            }

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void generateSwitchingNationalityLogs() {
        long lastId = -1;
        Page<AccountingEntityProperty> p;
        do {
            //checking properties
            p = accountingEntityPropertyRepository.findByKeyAndCreationDateAndDeletedFalseAndContractNotTerminated(lastId,
                    Arrays.asList(Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID,
                            Contract.DOWNGRADING_NATIONALITY_DATE, Contract.UPGRADING_NATIONALITY_DATE),
                    PageRequest.of(0, 100));

            for (AccountingEntityProperty property : p.getContent()) {
                Contract contract = (Contract) property.getOrigin();
                logger.info("contract id: " + contract.getId() + "; property id: " + property.getId());

                generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_SWITCHING_NATIONALITY_FLOW_CODE,
                        contract, property.getId(), property.getEntityType(),
                        property.getCreationDate(), property.getCreationDate(),
                        CollectionFlowStatus.PENDING_CLIENT_RESPONSE, "");
            }
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void generateSwitchingBankInfoLogs() {
        //checking new CPTs second
        long lastId = -1;
        Page<ContractPaymentTerm> p;
        do {
            p = contractPaymentTermRepository.findByReasonAndCreationDate(lastId, ContractPaymentTermReason.SWITCHING_BANK_ACCOUNT,
                    PageRequest.of(0, 100));

            for (ContractPaymentTerm newCPT : p.getContent()) {

                Map<String, Object> details = getSwitchBankInfoDetails(newCPT);

                Boolean endedFlag = (Boolean) details.get("endedFlag");
                CollectionFlowStatus flowStatus = (CollectionFlowStatus) details.get("flowStatus");
                Date idleSince = (Date) details.get("idleSince");
                if (endedFlag) continue;
                logger.info("cpt id: " + newCPT.getId());

                generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW_CODE,
                        newCPT.getContract(), newCPT.getId(),
                        newCPT.getEntityType(), newCPT.getCreationDate(), idleSince, flowStatus, "");
            }
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void generateRefundLogs() {
        long lastId = -1;
        Page<ClientRefundToDo> p;
        do {
            p = clientRefundTodoRepository.findByStatusAndCreationDate(lastId,
                    ClientRefundStatus.PENDING, PageRequest.of(0, 100));
            p.getContent().forEach(refundToDo -> {
                logger.info("refundToDo id: " + refundToDo.getId());

                generateCollectionFlowLog(
                        CollectionFlowLogController.PICKLISTITEM_REFUND_FLOW_CODE,
                        refundToDo.getContract(), refundToDo.getId(),
                        refundToDo.getEntityType(), refundToDo.getCreationDate(),
                        refundToDo.getTaskModifiedDate(), CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION, "");
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    // ACC-4715
    public void generateClientPayingViaCreditCardLogs() {
        long lastId = -1;
        Page<Contract> p;
        do {
            p = collectionFlowLogRepository.findContractFlaggedPayingViaCreditCardWithoutLogs(lastId, PageRequest.of(0, 100));
            p.getContent().forEach(c -> {
                try {
                    logger.log(Level.INFO, "contract id : {0}", c.getId());
                    generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE,
                            c, c.getId(),
                            c.getEntityType(), new Date(), new Date(),
                            getPayingViaCreditCardLogStatus(c), "");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    // ACC-5751
    private void generateOneMonthAgreementLogs() {
        long lastId = -1;
        Page<Contract> p;
        do {
            p = collectionFlowLogRepository.findContractFlaggedOneMonthAgreementWithoutLogs(lastId, PageRequest.of(0, 100));
            p.getContent().forEach(c -> {
                try {
                    logger.log(Level.INFO, "contract id : {0}", c.getId());
                    generateCollectionFlowLog(CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE,
                            c, c.getId(),
                            c.getEntityType(), new Date(), new Date(),
                            getOneMonthAgreementLogStatus(c), "");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public CollectionFlowStatus getOneMonthAgreementLogStatus(Contract c) {

        if (directDebitRepository.existsPendingDdWithDdfSent(c))
            return CollectionFlowStatus.PENDING_BANK_RESPONSE;
        if(directDebitRepository.existsPendingUserAction(c))
            return CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
        return CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    public void handleCollectionFlowLogs(Date calculateTo) {

        //1- Bounced Payments
        measureExecutionTimeInTryCatch("handleBouncedPaymentLog", () -> handleBouncedPaymentLog(calculateTo));

        //2- DD Rejection
        measureExecutionTimeInTryCatch("handleDDRejectionLog", () -> handleDDRejectionLog(calculateTo));

        //3- Switching Nationality
        measureExecutionTimeInTryCatch("handleSwitchingNationalityLog", this::handleSwitchingNationalityLog);

        //4- Switching Bank Info
        measureExecutionTimeInTryCatch("handleSwitchingBankInfoLog", this::handleSwitchingBankInfoLog);

        //5- Refund
        measureExecutionTimeInTryCatch("handleRefundLog", this::handleRefundLog);

        //6- Merged Flows (After Cash & Online payments reminder flow & Payment Expiry & stopping part of Incomplete Flow (Missing Bank Info or Missing DD Info) )
        measureExecutionTimeInTryCatch("handleStoppedFlowEntitiesWithLogs", this::handleStoppedFlowEntitiesWithLogs);

        //7- Client paying via credit card
        measureExecutionTimeInTryCatch("handleClientPayingViaCreditCardLog", this::handleClientPayingViaCreditCardLog);

        //8- One month agreement
        measureExecutionTimeInTryCatch("handleOneMonthAgreementLog", () -> handleOneMonthAgreementLog(calculateTo));

        //19- Incomplete Flow Missing Bank Info
        measureExecutionTimeInTryCatch("handleIncompleteFlowMissingBankInfo", this::handleIncompleteFlowMissingBankInfo);

        //10- Missing/Wrong Documents
        measureExecutionTimeInTryCatch("handleIncompleteFlowDataEntryRejectionLog", () -> handleIncompleteFlowDataEntryRejectionLog(calculateTo));
    }

    private void handleBouncedPaymentLog(Date calculateTo) {
        long lastId = -1;
        Page<Map> p;
        do {
            p = collectionFlowLogRepository.findBouncedPaymentLogsNeedToUpdate(lastId, calculateTo, PageRequest.of(0, 100));
            p.getContent().forEach(m -> {
                try {
                    handleBouncedPaymentLog((CollectionFlowLog) m.get("log"), (Payment) m.get("payment"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = ((CollectionFlowLog) p.getContent().get(p.getContent().size() - 1).get("log")).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void handleBouncedPaymentLog(CollectionFlowLog flowLog, Payment bouncedPayment) {
        logger.info("payment id: " + bouncedPayment.getId());
        PicklistItem reasonOfTermination = bouncedPayment.getContract().getReasonOfTerminationList();
        if ((reasonOfTermination != null &&
                ContractScheduleForTerminationUtils.isTerminationReasonDueBouncedPayment(reasonOfTermination.getCode()) &&
                (bouncedPayment.getContract().getIsScheduledForTermination() ||
                        bouncedPayment.getContract().getScheduledDateOfTermination() != null ||
                        bouncedPayment.getContract().getDateOfTermination() != null)) ||
                !bouncedPayment.getStatus().equals(PaymentStatus.BOUNCED) || bouncedPayment.isReplaced()){

            flowLog.setEnded(true);
            collectionFlowLogRepository.silentSave(flowLog);
            return;
        }

        Date idleSince = null;
        CollectionFlowStatus flowStatus = null;
        DirectDebit directDebit = bouncedPayment.getDirectDebit();

        List<Object[]> ddfs = directDebit != null ?
                directDebitFileRepository.selectByDirectDebitAndDdMethod(directDebit, DirectDebitMethod.MANUAL) :
                new ArrayList<>();

        //check the status of related DD
        if (ddfs.size() > 0) {
            Map<String,Object> result = checkStatusOfRelatedDD(ddfs, bouncedPayment, directDebit);
            idleSince = (Date) result.get("idleSince");
            flowStatus = (CollectionFlowStatus) result.get("flowStatus");
        }
        logger.info("flowStatus: " + flowStatus + "; idleSince: " + idleSince);

        if (flowStatus == null ||
                (isValueNotChanged(flowLog, "status", flowStatus) &&
                        isValueNotChanged(flowLog, "idleSince", idleSince))) return;

        flowLog.setStatus(flowStatus);
        flowLog.setIdleSinceDate(idleSince);
        collectionFlowLogRepository.silentSave(flowLog);
    }

    private void handleDDRejectionLog(Date calculateTo) {
        long lastId = -1;
        Page<Map> p;
        do {
            p = collectionFlowLogRepository.findDDRejectionLogsNeedToUpdate(lastId, calculateTo, PageRequest.of(0, 100));
            p.getContent().forEach(m -> {
                try {
                    handleDDRejectionLog((CollectionFlowLog) m.get("log"), (DirectDebitRejectionToDo) m.get("ddrTodo"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = ((CollectionFlowLog) p.getContent().get(p.getContent().size() - 1).get("log")).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void handleDDRejectionLog(CollectionFlowLog flowLog, DirectDebitRejectionToDo directDebitRejectionToDo) {
        logger.info("todo id: " + directDebitRejectionToDo.getId());
        DirectDebitRejectionToDoType rejectionToDoType =
                directDebitRejectionToDo.getTaskName() != null && !directDebitRejectionToDo.getTaskName().isEmpty() ?
                        DirectDebitRejectionToDoType.valueOf(directDebitRejectionToDo.getTaskName()) : null;
        Contract contract = flowLog.getContract();

        String notes = getCollectionFlowNotesFromRejectionCategory(directDebitRejectionToDo.getLastRejectCategory());

        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()){
            flowLog.setEnded(true);
            flowLog.setNotes(notes);
            collectionFlowLogRepository.silentSave(flowLog);
        } else if (rejectionToDoType != null && rejectionToDoType.equals(DirectDebitRejectionToDoType.WAITING_RE_SCHEDULE_B)) {
            flowLog.setStatus(CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION);
            flowLog.setRejectionTodoIsRescheduledWhenEnded(true);
            flowLog.setEnded(true);
            flowLog.setNotes(notes);
            collectionFlowLogRepository.silentSave(flowLog);
        } else {
            CollectionFlowStatus flowStatus = flowLog.getStatus();
            Date idleSince = flowLog.getIdleSinceDate();
            DirectDebit directDebit = directDebitRejectionToDo.getLastDirectDebit();
            if (directDebit != null) {
                if (directDebit.getCategory().equals(DirectDebitCategory.A)) { // case DDA we only need M Status
                    switch (directDebit.getMStatus()) {
                        case IN_COMPLETE:
                            if (hasRunningGraphicDesignerTodo(contract))
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                            break;
                        case PENDING_DATA_ENTRY:
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            break;
                        case PENDING:
                            if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                                flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                            else
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                            break;
                        default:
                            break;
                    }

                    idleSince = directDebit.getLastModificationDate();
                } else { // case DDB we need to check both M Status and Status
                    if (directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE) && directDebit.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                        if (hasRunningGraphicDesignerTodo(contract))
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    }else if (directDebit.getStatus().equals(DirectDebitStatus.CONFIRMED) &&
                            directDebit.getMStatus().equals(DirectDebitStatus.PENDING)) {
                        boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.MANUAL) && ddf.getStatus().equals(DirectDebitFileStatus.SENT));
                        if (manualDDsSentToBank)
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING) &&
                            directDebit.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                        boolean manualDDsSentToBank = directDebit.getDirectDebitFiles().stream().anyMatch(ddf -> ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) && ddf.getStatus().equals(DirectDebitFileStatus.SENT));
                        if (manualDDsSentToBank)
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    } else if (directDebit.getStatus().equals(DirectDebitStatus.PENDING)) {
                        if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                            flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        else
                            flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    }
                    idleSince = directDebit.getLastModificationDate();
                }
            }
            logger.info("flowStatus: " + flowStatus +
                    "; idleSince: " + idleSince +
                    "; notes: " + notes +
                    "; rejectionTodoIsRescheduledWhenEnded: " + flowLog.getRejectionTodoIsRescheduledWhenEnded());

            if (isValueNotChanged(flowLog, "status", flowStatus) &&
                    isValueNotChanged(flowLog, "idleSince", idleSince) &&
                    isValueNotChanged(flowLog, "notes", notes) &&
                    !flowLog.getRejectionTodoIsRescheduledWhenEnded()) return;

            flowLog.setStatus(flowStatus);
            flowLog.setIdleSinceDate(idleSince);
            flowLog.setNotes(notes);

            // if the RejectionTodo was already Rescheduled so mark the flags is false
            if (flowLog.getRejectionTodoIsRescheduledWhenEnded()) {
                flowLog.setRejectionTodoIsRescheduledWhenEnded(false);
                flowLog.setEnded(false);
            }

            collectionFlowLogRepository.silentSave(flowLog);
        }
    }

    private void handleIncompleteFlowDataEntryRejectionLog(Date calculateTo) {
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findIncompleteFlowDataEntryRejectionLog(lastId, calculateTo, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                try {
                    logger.info("log id: " + flowLog.getId());
                    handleMissingOrWrongDocumentLog(flowLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void handleMissingOrWrongDocumentLog(CollectionFlowLog flowLog) {
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());

        Date idleSince = flowLog.getIdleSinceDate();
        CollectionFlowStatus newStatus;

        if (!cpt.getIsIBANRejected() && !cpt.getIsAccountHolderRejected() && !cpt.getIsEidRejected()) {
            if (directDebitRepository.existsActiveDdByCptAndStatus(
                    cpt, Collections.singletonList(DirectDebitStatus.PENDING_DATA_ENTRY))) {
                newStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                idleSince = cpt.getLastModificationDate();
            } else {
                flowLog.setEnded(true);
                collectionFlowLogRepository.silentSave(flowLog);
                return;
            }
        } else {
            //in case still rejected or rejected again --> reset the flow status
            GraphicDesignerToDo graphicDesignerToDo = graphicDesignerTodoRepository
                    .findTopByContractIdAndToDoTypeAndCompletedFalseOrderByIdDesc(
                            cpt.getContract().getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE);
            if (graphicDesignerToDo != null) {
                idleSince = idleSince.compareTo(graphicDesignerToDo.getLastModificationDate()) < 0 ? graphicDesignerToDo.getLastModificationDate() : idleSince;
                newStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
            } else {
                idleSince = idleSince.compareTo(cpt.getLastModificationDate()) < 0 ? cpt.getLastModificationDate() : idleSince;

                if(directDebitRepository.existsActiveDdByCptAndStatus(
                        cpt, Collections.singletonList(DirectDebitStatus.IN_COMPLETE))) {
                    newStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                } else {
                    flowLog.setEnded(true);
                    collectionFlowLogRepository.silentSave(flowLog);
                    return;
                }
            }
        }
        logger.info("newStatus: " + newStatus + "; idleSince: " + idleSince );
        if (isValueNotChanged(flowLog, "status", newStatus) &&
                isValueNotChanged(flowLog, "idleSince", idleSince)) return;

        flowLog.setStatus(newStatus);
        flowLog.setIdleSinceDate(idleSince);
        collectionFlowLogRepository.silentSave(flowLog);
    }

    public void handleSwitchingNationalityLog() {
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findSwitchingNationalityLogsNeedToUpdate(lastId, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                logger.info("log id: " + flowLog.getId());
                flowLog.setEnded(true);
                collectionFlowLogRepository.silentSave(flowLog);
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void handleSwitchingBankInfoLog() {
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findSwitchingBankLogsNeedToUpdate(lastId, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                try {
                    //handle newCPT
                    ContractPaymentTerm newCPT = contractPaymentTermRepository.findOne(flowLog.getRelatedToId());
                    Map<String, Object> details = getSwitchBankInfoDetails(newCPT);

                    if ((Boolean) details.get("endedFlag")) {
                        flowLog.setEnded(true);
                        collectionFlowLogRepository.silentSave(flowLog);
                        return;
                    }
                    CollectionFlowStatus flowStatus = (CollectionFlowStatus) details.get("flowStatus");
                    Date idleSince = ((Date) details.get("idleSince")).compareTo(flowLog.getIdleSinceDate()) >= 0 ?
                            (Date) details.get("idleSince") :
                            flowLog.getIdleSinceDate();
                    logger.info("newStatus: " + flowStatus + "; idleSince: " + idleSince);

                    if (isValueNotChanged(flowLog, "status", flowStatus) &&
                            isValueNotChanged(flowLog, "idleSince", idleSince)) return;

                    flowLog.setIdleSinceDate(idleSince);
                    flowLog.setStatus(flowStatus);
                    collectionFlowLogRepository.silentSave(flowLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    public void handleRefundLog() {
        // Stopping step
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            //todo should I pass calculateTo or remove it?
            p = collectionFlowLogRepository.findRefundLogsNeedToStopped(lastId, Arrays.asList(
                    ClientRefundStatus.REJECTED, ClientRefundStatus.PAID), PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                try {
                    logger.info("log id: " + flowLog.getId());
                    flowLog.setEnded(true);
                    collectionFlowLogRepository.silentSave(flowLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());

        // Updating Step
        lastId = -1;
        Page<CollectionFlowLog> o;
        do {
            o = collectionFlowLogRepository.findRefundLogsNeedToUpdate(lastId, PageRequest.of(0, 100));
            o.getContent().forEach(flowLog -> {
                try {
                    logger.info("log id: " + flowLog.getId());
                    ClientRefundToDo refundToDo = clientRefundTodoRepository.findOne(flowLog.getRelatedToId());

                    logger.info("idleSince: " + refundToDo.getLastModificationDate() );

                    if (isValueNotChanged(flowLog, "idleSince", refundToDo.getLastModificationDate())) return;

                    flowLog.setIdleSinceDate(refundToDo.getLastModificationDate());
                    collectionFlowLogRepository.silentSave(flowLog);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            if (!o.getContent().isEmpty()) {
                lastId = o.getContent().get(o.getContent().size() - 1).getId();
            }
        } while (!o.getContent().isEmpty());
    }

    public void handleStoppedFlowEntitiesWithLogs() {
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findFlowProcessorEntityStoopedWithLogs(lastId, Arrays.asList(
                    CollectionFlowLogController.PICKLISTITEM_IPAM_FLOW_CODE,
                    CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW_CODE,
                    CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO,
                    CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION,
                    CollectionFlowLogController.PICKLISTITEM_PAYMENT_EXPIRY_FLOW_CODE,
                    CollectionFlowLogController.PICKLISTITEM_EXTENSION_FLOW),
                    PageRequest.of(0, 100));
            p.getContent().forEach(f -> {
                logger.info("log id: " + f.getId());
                f.setEnded(true);
                collectionFlowLogRepository.silentSave(f);
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    // ACC-4715
    public void handleClientPayingViaCreditCardLog() {
        //1- Stopping Step
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findLogsNeedToStopByFlowTypeCode(lastId,
                    CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                logger.info("log id: " + flowLog.getId() + " stopped");
                flowLog.setEnded(true);
                collectionFlowLogRepository.silentSave(flowLog);
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());

        //2- Updating Step
        Page<Object[]> o;
        lastId = -1;
        do {
            o = collectionFlowLogRepository.findPayingViaCcLogsNeedToUpdate(lastId,
                    new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate().plusMonths(2).dayOfMonth().withMinimumValue().toDate(), PageRequest.of(0, 100));

            List<CollectionFlowLog> logs = collectionFlowLogRepository.findAll(o.getContent().stream()
                    .map(x -> (Long) x[0]).collect(Collectors.toList()));
            Map<Long, String> m = new HashMap<>();

            o.getContent().forEach(row -> m.put((Long) row[0], (String) row[1]));

            logs.forEach(flowLog -> {
                flowLog.setIdleSinceDate(new Date());
                flowLog.setStatus(CollectionFlowStatus.valueOf(m.get(flowLog.getId())));
                collectionFlowLogRepository.silentSave(flowLog);
            });
            if (!o.getContent().isEmpty()) {
                lastId = ((CollectionFlowLog) o.getContent().get(o.getContent().size() - 1)[0]).getId();
            }
        } while (!o.getContent().isEmpty());
    }

    public CollectionFlowStatus getPayingViaCreditCardLogStatus(Contract c) {

        boolean nextMonthPaymentReceived = paymentRepository.existsMonthlyPaymentReceivedByDate(
                c, new LocalDate().plusMonths(1).getYear(),
                new LocalDate().plusMonths(1).getMonthOfYear());
        boolean hasPendingDd = Setup.getRepository(DirectDebitFileRepository.class)
                .existsByContractPaymentTermAndDdCategoryBAndDdStatusInAndStatusSent(
                        c.getActiveContractPaymentTerm(), DirectDebitStatus.PENDING);
        logger.log(Level.INFO, "c id: {0}; nextMonthPaymentReceived: {1}; hasPendingDd: {2}",
                new Object[]{c.getId(), nextMonthPaymentReceived, hasPendingDd});

        return nextMonthPaymentReceived && hasPendingDd ?
                CollectionFlowStatus.PENDING_BANK_RESPONSE : CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    // ACC-5751
    public void handleOneMonthAgreementLog(Date calculateTo) {
        // Stopping Step
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findLogsNeedToStopByFlowTypeCodeForOMA(lastId,
                    CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                logger.log(Level.INFO, "log id: {0} stopped", flowLog.getId());
                flowLog.setEnded(true);
                collectionFlowLogRepository.silentSave(flowLog);
            });
            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());

        // Updating Step
        lastId = -1;
        Page<Object[]> o;
        do {
            o = collectionFlowLogRepository.findOMALogsNeedToUpdate(lastId, calculateTo, PageRequest.of(0, 100));
            List<CollectionFlowLog> logs = collectionFlowLogRepository.findAll(o.getContent().stream()
                    .map(x -> (Long) x[0]).collect(Collectors.toList()));
            Map<Long, String> m = new HashMap<>();

            o.getContent().forEach(row -> m.put((Long) row[0], (String) row[1]));

            logs.forEach(flowLog -> {
                flowLog.setIdleSinceDate(new Date());
                flowLog.setStatus(CollectionFlowStatus.valueOf(m.get(flowLog.getId())));
                collectionFlowLogRepository.silentSave(flowLog);
            });
            if (!o.getContent().isEmpty()) {
                lastId = ((CollectionFlowLog) o.getContent().get(o.getContent().size() - 1)[0]).getId();
            }
        } while (!o.getContent().isEmpty());
    }

    // ACC-6804
    public void handleIncompleteFlowMissingBankInfo() {
        // get the logs that linked with running flow and should be updated
        long lastId = -1;
        Page<CollectionFlowLog> p;
        do {
            p = collectionFlowLogRepository.findInCompleteFlowMissingBankInfoToUpdate(lastId, PageRequest.of(0, 100));
            p.getContent().forEach(flowLog -> {
                CollectionFlowStatus flowStatus = getIncompleteFlowStatus(flowLog.getContract());
                logger.info("flow status changed to: " + flowStatus +  "; contract id: " + flowLog.getContract().getId());
                flowLog.setStatus(flowStatus);
                flowLog.setIdleSinceDate(new Date());
                collectionFlowLogRepository.silentSave(flowLog);
            });

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private CollectionFlowStatus getIncompleteFlowStatus(Contract c) {
        return c.isSigningPaperMode() &&
                hasRunningGraphicDesignerTodo(c) ?
                CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION :
                CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
    }

    public void handleExtensionFlowLog (CollectionFlowLog flowLog) {

        if (!QueryService.existsEntity(FlowProcessorEntity.class,
                "e.id = :p0 and e.entityType = :p1 and e.stopped = false and e.completed = false",
                new Object[]{flowLog.getRelatedToId(), flowLog.getRelatedToEntity()})) {
            flowLog.setEnded(true);
            collectionFlowLogRepository.save(flowLog);
        }
    }

    //----------------------------------------------------------------------------//
    //---------------------------- HELPER FUNCTIONS ------------------------------//
    //----------------------------------------------------------------------------//

    public static CollectionFlowStatus getCollectionFlowStatusFromRejectionToDoType (DirectDebitRejectionToDoType rejectionToDoType) {
        if (rejectionToDoType == null)
            return null;
        switch (rejectionToDoType) {
            case WAITING_BANK_RESPONSE:
            case WAITING_BANK_RESPONSE_B:
            case WAITING_BANK_RESPONSE_B_CASE_D:
            case WAITING_BANK_RESPONSE_B_BOUNCED:
                return CollectionFlowStatus.PENDING_BANK_RESPONSE;
            case WAITING_RE_SCHEDULE_B:
            case WAITING_ACCOUNTANT_ACTION:
            case WAITING_FLOW_PAUSE_B_BOUNCED:
            case WAITING_ACCOUNTANT_ACTION_B_CASE_D:
            case WAITING_ACCOUNTANT_ACTION_B_BOUNCED:
                return CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
            case WAITING_CLIENT_SIGNATURE:
            case WAITING_CLIENT_SIGNATURE_B_CASE_D:
            case WAITING_CLIENT_SIGNATURE_B_BOUNCED:
            case WAITING_CLIENT_INFO_DUE_AUTHORIZATION:
            case WAITING_CLIENT_INFO_DUE_AUTHORIZATION_B_CASE_D:
                return CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
        }
        return null;
    }

    public static String getCollectionFlowNotesFromRejectionCategory (DirectDebitRejectCategory rejectCategory) {
        if (rejectCategory != null)
            switch (rejectCategory) {
                case Signature:
                    return CollectionFlowLogController.SIGNATURE_REJECTION_REASON_NOTES;
                case Authorization:
                    return CollectionFlowLogController.AUTHORIZATION_REJECTION_REASON_NOTES;
                case Invalid_Account:
                    return CollectionFlowLogController.INVALID_ACCOUNT_REJECTION_REASON_NOTES;
                case Account:
                    return CollectionFlowLogController.ACCOUNT_REJECTION_REASON_NOTES;
                case EID:
                    return CollectionFlowLogController.EID_REJECTION_REASON_NOTES;
            }
        return "";
    }

    public CollectionFlowJobHistory getNewCollectionFlowHistory () {
        Date calculateFrom;
        Date calculateTo;
        Date now = new Date();
        CollectionFlowJobHistory lastFlowJobHistory = getLastCollectionFlowHistoryByStatus(CollectionJobStatus.SUCCEED);
        if (lastFlowJobHistory != null) {
            calculateFrom = lastFlowJobHistory.getCalculateTo();
            calculateTo = now;
        } else {
            calculateFrom = DateUtil.getFirstDateEver();
            calculateTo = now;
        }

        CollectionFlowJobHistory collectionFlowJobHistory = new CollectionFlowJobHistory(calculateFrom, calculateTo, CollectionJobStatus.RUNNING);
        return collectionFlowJobHistoryRepository.save(collectionFlowJobHistory);
    }

    public CollectionFlowJobHistory getLastCollectionFlowHistoryByStatus (CollectionJobStatus status) {
        return collectionFlowJobHistoryRepository.findTopByStatusOrderByIdDesc(status);
    }

    public Map<String, Object> getSwitchBankInfoDetails(ContractPaymentTerm newCPT) {
        Map<String, Object> returnResult = new HashMap<>();
        boolean endedFlag = false;
        CollectionFlowStatus flowStatus = null;
        Date idleSince = null;
        List<DirectDebitStatus> validStatuses = Arrays.asList(
                DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.CONFIRMED, DirectDebitStatus.IN_COMPLETE);

        if (!newCPT.isActive())
            endedFlag = true;
        else {
            ContractPaymentTerm oldCPT = contractPaymentTermRepository.findFirstByContractAndIdLessThanOrderByIdDesc(newCPT.getContract(), newCPT.getId());
            if (oldCPT != null) {
                //search for old DDs if found then we didn't cancel the old dds yet and the flow is still running
                if (directDebitRepository.existsByContractPaymentTermAndStatusIn(oldCPT, validStatuses)) {
                    // now we need to check the new DDs created on the new CPT to define their status right now.
                    List<Object[]> newDDs = directDebitRepository.findStatusAndLastModificationDateByContractPaymentTerm(newCPT);

                    OUTER:
                    for (Object[] dd : newDDs) {
                        switch ((DirectDebitStatus)dd[1]) {
                            case IN_COMPLETE:
                                if (hasRunningGraphicDesignerTodo(newCPT.getContract()))
                                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                else
                                    flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                                idleSince = (Date) dd[2];
                                break OUTER;
                            case PENDING_DATA_ENTRY:
                                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                idleSince = (Date) dd[2];
                                break OUTER;
                            case PENDING:
                                flowStatus = QueryService.existsEntity(DirectDebitFile.class,
                                        "e.directDebit.id = :p0 and e.status = :p1",
                                        new Object[]{dd[0], DirectDebitFileStatus.SENT}) ?
                                        CollectionFlowStatus.PENDING_BANK_RESPONSE :
                                        CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                                idleSince = (Date) dd[2];
                                break OUTER;
                            default:
                                break;
                        }
                    }

                    if (flowStatus == null) {
                        flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                        idleSince = newCPT.getCreationDate();
                    }
                } else endedFlag = true;
            } else {
                endedFlag = true;
            }
        }

        returnResult.put("endedFlag", endedFlag);
        returnResult.put("flowStatus", flowStatus);
        returnResult.put("idleSince", idleSince);

        return returnResult;
    }


    public List<Map<String, Object>> getRunningFlowsMessagesByContractACC8311(Contract contract) {
        List<Map<String, Object>> l = new ArrayList<>();
        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        addRunningFlowsMessagesByContractACC8311_BouncedFlow(contract, l);

        addRunningFlowsMessagesByContractACC8311_IncompleteDDs(cpt, l);

        addRunningFlowsMessagesByContractACC8311_RejectedDDs(cpt, l);

        addRunningFlowsMessagesByContractACC8311_Ipam(contract, l);

        addRunningFlowsMessagesByContractACC8311_PayingViaCc(cpt, l);

        return l;
    }

    private void addRunningFlowsMessagesByContractACC8311_BouncedFlow(Contract c, List<Map<String, Object>> l) {
        Payment p = paymentRepository.findFirstByContractAndStatusAndReplacedFalseOrderByDateOfPaymentAsc(
                c, PaymentStatus.BOUNCED);
        if (p == null) return;
        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .createToDoIfNotExists(
                        p, p.getContract().getActiveContractPaymentTerm(), ContractPaymentConfirmationToDo.Source.BOUNCED_PAYMENT_FLOW);

        if (todo == null) return;

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_BOUNCED_PAYMENT.toString());
        if (t == null) return;
        String pay_link = Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(todo);

        String text = TemplateUtil.compileTemplate(t,
                c, new HashMap<String, String>() {{
                    put("link_to_pay_via_cc", pay_link);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Bouncing Flow");
            put("message", text);
            put("ccLink", pay_link == null  || pay_link.isEmpty() ? "missing" : pay_link);
            put("signLink", "missing");

        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_IncompleteDDs(
            ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        if (!Setup.getRepository(DirectDebitRepository.class)
                .existsIncompleteDDsByCpt(cpt)) return;

        List<String> missingBankInfo = Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .getMissingBankInfo(cpt);

        if (missingBankInfo.isEmpty()) return;

        boolean missingBankInfoRunning = flowProcessorService.existsRunningFlow(
                cpt.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);
        boolean rejectDataEntryRunning = cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected();

        if (!missingBankInfoRunning && !rejectDataEntryRunning) return;

        StringBuilder sb = new StringBuilder();


        boolean signatureIsMissing = false;
        if (missingBankInfo.contains("Signatures")) {
            signatureIsMissing = true;
            sb.append("signatures");
            sb.append(missingBankInfo.size() > 1 ? " and " : "");
            missingBankInfo.remove("Signatures");
        }

        if (!missingBankInfo.isEmpty()) {
            sb.append(missingBankInfo.size() == 1 ? "document of " : "documents of ");
            sb.append(String.join(" - ", missingBankInfo));
        }

        String signLink = Setup.getApplicationContext()
                .getBean(CCAppContentService.class)
                .getSignDdWebLinkForPaymentSection(cpt, null,
                        true,
                        missingBankInfoRunning || signatureIsMissing);

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_INCOMPLETE_DDS.toString());
        if (t == null) return;

        String text = TemplateUtil.compileTemplate(t,
                cpt.getContract(), new HashMap<String, String>() {{
                    put("missing_info", sb.toString());
                    put("link_to_upload_documents", signLink);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Incomplete DDs");
            put("message", text);
            put("ccLink", "missing");
            put("signLink", "missing");
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_RejectedDDs(ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_REJECTED_DDS.toString());

        if (t == null) return;

        List<DirectDebit> rejectedDds = directDebitRepository.findDirectDebitByRejectionFlowByCpt(cpt);
        Map<String,Object> m = ccAppService.getRejectionFLowSectionInfo(rejectedDds, cpt.getContract());
        if (m == null || !m.containsKey("rejectedDd")) return;

        DirectDebit d = (DirectDebit) m.get("rejectedDd");

        DirectDebitRejectCategory category = d.getDirectDebitRejectionToDo() != null ?
                d.getDirectDebitRejectionToDo().getLastRejectCategory() :
                d.getDirectDebitBouncingRejectionToDo().getLastRejectCategory();

        String linkToUploadDocument = Setup.getApplicationContext()
                .getBean(CCAppContentService.class)
                .getSignDdWebLinkForPaymentSection(cpt, category, false, false);

        String text = TemplateUtil.compileTemplate(t,
                cpt.getContract(), new HashMap<String, String>() {{
                    put("reason_of_rejection", category != null ? category.getLabel() : "");
                    put("link_to_upload_documents", linkToUploadDocument);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Rejected DDs");
            put("message", text);
            put("ccLink", "missing");
            put("signLink", "missing");
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_Ipam(Contract contract, List<Map<String, Object>> l) {
        if (!Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .existsRunningFlow(contract, FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED))
            return;

        Template t = TemplateUtil.getTemplate(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_IPAM.toString());
        if (t == null) return;

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contract.getActiveContractPaymentTerm());
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "GPT_addRunningFlowsMessagesByContractACC8311_Ipam");
        }});

        ContractPaymentConfirmationToDo todo = Setup.getApplicationContext()
                .getBean(AfterCashFlowService.class).getPaymentTodo(contract.getActiveContractPaymentTerm());

        if (todo == null) {
            todo = Setup.getApplicationContext()
                    .getBean(AfterCashFlowService.class)
                    .addConfirmationTodoForPayment(contract);
        }

        String ccLink = todo == null ? "" :
                Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(todo);

        String signingLink =  Setup.getApplicationContext()
                .getBean(Utils.class)
                .getSingDDLink(signDDMap);

        String text = TemplateUtil.compileTemplate(t,
                contract, new HashMap<String, String>() {{
                    put("signing_link", signingLink);
                }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "IPAM");
            put("message", text);
            put("ccLink", ccLink == null  || ccLink.isEmpty() ? "missing" : ccLink);
            put("signLink", signingLink == null  || signingLink.isEmpty() ? "missing" : signingLink);
        }});
    }

    private void addRunningFlowsMessagesByContractACC8311_PayingViaCc(ContractPaymentTerm cpt, List<Map<String, Object>> l) {

        if (!cpt.getContract().isPayingViaCreditCard()) return;

        boolean isEligibleForTokenizationViaContract = ContractService.isEligibleForTokenizationViaContract(cpt.getContract());
        ContractPaymentConfirmationToDo toDo = ccAppContentService.getToDoForPayingViaCCOrOMA(cpt, isEligibleForTokenizationViaContract);

        if (toDo == null) {

            DateTime d = Setup.getApplicationContext().getBean(PaymentService.class)
                    .getLastReceivedMonthlyPaymentDate(cpt.getContract());

            toDo = Setup.getApplicationContext()
                    .getBean(ClientPayingViaCreditCardService.class)
                    .createTodoIfNotExists(cpt, d == null ?
                            new LocalDate(cpt.getContract().getStartOfContract()) :
                            new LocalDate(d).plusMonths(1));
        }

        CmOngoingCollectionFlowsTemplateCode templateName =
                toDo == null && isEligibleForTokenizationViaContract ?
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_HAS_TOKENIZED_CARD_PAYING_VIA_CC :
                        CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC;
        if (toDo == null && templateName.equals(CmOngoingCollectionFlowsTemplateCode.CM_ONGOING_COLLECTION_FLOWS_CLIENT_PAYING_VIA_CC)) return;

        Template t = TemplateUtil.getTemplate(templateName.toString());
        if (t == null) return;

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", cpt);
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "GPT_addRunningFlowsMessagesByContractACC8311_PayingViaCc");
        }});

        String pay_link = toDo == null ? "" :
                Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .getPayingViaCreditCardLink(toDo);

        String signingLink = Setup.getApplicationContext()
                .getBean(Utils.class)
                .getSingDDLink(signDDMap);
        String text = TemplateUtil.compileTemplate(
                t,
                cpt.getContract(),
                new HashMap<String, String>() {{
                        if (!pay_link.isEmpty()) {
                            put("paying_via_cc_link", pay_link);
                        }
                        put("signing_link", signingLink);
                    }});

        l.add(new HashMap<String, Object>() {{
            put("flowName", "Paying via CC");
            put("message", text);
            put("ccLink", pay_link == null  || pay_link.isEmpty() ? "missing" : pay_link);
            put("signLink","missing");
        }});
    }

    private boolean isValueNotChanged(CollectionFlowLog flowLog, String columnName, Object newValue) {

        switch (columnName) {
            case "status":
                return flowLog.getStatus().equals(newValue);
            case "idleSince":
                return flowLog.getIdleSinceDate().equals(newValue);
            case "notes":
                return flowLog.getNotes().equals(newValue);
            default:
                return true;
        }
    }

    private boolean hasRunningGraphicDesignerTodo(Contract c) {

        return graphicDesignerTodoRepository.existsByContractIdAndToDoTypeAndCompletedFalse(
                c.getId(), GraphicToDoType.ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE);
    }

    // ACC-8457
    private Map<String,Object> checkStatusOfRelatedDD(List<Object[]> ddfs, Payment bouncedPayment, DirectDebit directDebit) {
        Date idleSince = null;
        CollectionFlowStatus flowStatus = null;
        Map<String,Object> result = new HashMap<>();
        List<DirectDebitStatus> ddStatuses = Arrays.asList(DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING);
        OUTER:
        for (Object[] ddf : ddfs.stream().filter(ddf -> ddStatuses.contains((DirectDebitStatus) ddf[0])).collect(Collectors.toList())) {
            idleSince = (Date) ddf[1];
            switch ((DirectDebitStatus) ddf[0]) {
                case IN_COMPLETE:
                    if (hasRunningGraphicDesignerTodo(bouncedPayment.getContract()))
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    else
                        flowStatus = CollectionFlowStatus.PENDING_CLIENT_RESPONSE;
                    break OUTER;
                case PENDING_DATA_ENTRY:
                    flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    break OUTER;
                case PENDING:
                    if (directDebit.getDirectDebitFiles().stream().anyMatch(directDebitFile -> directDebitFile.getStatus().equals(DirectDebitFileStatus.SENT)))
                        flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;
                    else
                        flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                    break OUTER;
            }
        }

        // if still null then the DD is confirmed, and we need to check if the manual dd was sent to bank or not.
        if(flowStatus == null) {
            //if not sent to bank then it's still waiting for Business user
            if (!bouncedPayment.getSentToBankByMDD()){
                flowStatus = CollectionFlowStatus.PENDING_BUSINESS_USER_ACTION;
                idleSince = idleSince != null && idleSince.compareTo(bouncedPayment.getLastModificationDate()) >= 0 ? idleSince : bouncedPayment.getLastModificationDate();
            } else {
                flowStatus = CollectionFlowStatus.PENDING_BANK_RESPONSE;

                Date bouncedLastModificationDate = paymentRepository.findLastModificationDateFromHistory(bouncedPayment.getId());
                idleSince = bouncedLastModificationDate != null ?
                        bouncedLastModificationDate : bouncedPayment.getLastModificationDate();
            }
        }

        result.put("idleSince", idleSince);
        result.put("flowStatus", flowStatus);
        return result;
    }

    private void measureExecutionTimeInTryCatch(String methodName, Runnable method) {
        try {
            measureExecutionTime(methodName, method);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void measureExecutionTime(String methodName, Runnable method) {
        long startTime = System.nanoTime();
        logger.info("start method: " + methodName);
        method.run();
        logger.info("end method: " + methodName);
        long endTime = System.nanoTime();

        long executionTimeInNano = endTime - startTime;
        double executionTimeInSeconds = executionTimeInNano / 1e9; // Convert to seconds

        logger.log(executionTimeInSeconds > 45 ? Level.WARNING : Level.INFO,
                (executionTimeInSeconds > 45 ? Level.WARNING.getName() : Level.INFO.getName()) +
                        ".CollectionFlowLogService." + methodName + " executed in " + executionTimeInSeconds + " seconds");
    }
}