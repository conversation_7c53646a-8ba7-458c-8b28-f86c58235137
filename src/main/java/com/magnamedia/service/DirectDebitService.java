package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.chatai.ChatAIRequestBuilder;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.services.chatai.ChatAIService;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Sheikh Albalad
 * Created on Feb 16, 2022
 */

@Slf4j
@Service
public class DirectDebitService {
    protected static final Logger logger = Logger.getLogger(DirectDebitService.class.getName());

    public static final List<String> BANK_INFO_TAGS = Arrays.asList(
            ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
            ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
            ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private DirectDebitSignatureRepository directDebitSignatureRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractPaymentTermHelper contractPaymentTermHelper;
    @Autowired
    private AttachementRepository attachmentRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;
    @Autowired
    private WordTemplateService wordTemplateService;
    @Autowired
    private Utils utils;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private DirectDebitCancellationService directDebitCancellationService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    ContractRepository contractRepository;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    ChatAIService chatAIService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ObjectMapper objectMapper;

    public static List<DirectDebitStatus> notAllowedStatuses = Arrays.asList(
            DirectDebitStatus.EXPIRED,
            DirectDebitStatus.CANCELED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION);

    public static List<DirectDebitStatus> activeWithoutConfirmedStatuses = Arrays.asList(
            DirectDebitStatus.PENDING,
            DirectDebitStatus.REJECTED,
            DirectDebitStatus.IN_COMPLETE,
            DirectDebitStatus.PENDING_DATA_ENTRY);

    public static List<DirectDebitStatus> notAllowedStatusesWithRejected = Arrays.asList(
            DirectDebitStatus.EXPIRED,
            DirectDebitStatus.CANCELED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION,
            DirectDebitStatus.REJECTED);

    public static List<DirectDebitStatus> notAllowedStatusesWithNotApplicable = Arrays.asList(
            DirectDebitStatus.EXPIRED,
            DirectDebitStatus.CANCELED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION,
            DirectDebitStatus.NOT_APPLICABLE);

    public static List<DirectDebitStatus> notAllowedStatusesWithConfirmed = Arrays.asList(
            DirectDebitStatus.EXPIRED,
            DirectDebitStatus.CANCELED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION,
            DirectDebitStatus.CONFIRMED);

    public List<DirectDebit> getContractDdsToCancel(Contract c) {
        logger.info("contract id: " + c.getId());
        List<DirectDebit> dds = getActiveDdsForCancellation(c);
        List<DirectDebit> ddsToCancel = new ArrayList<>();

        for (DirectDebit dd : dds) {
            logger.info("dd id: " + dd.getId());
            if (dd.getPayments().isEmpty() || dd.getCategory().equals(DirectDebitCategory.B)) {
                ddsToCancel.add(dd);
                continue;
            }

            // ACC-3844 todo check with Shaban
            boolean isMonthlyOnly = dd.getPayments().stream()
                    .allMatch(cp -> PaymentHelper.isMonthlyPayment(cp.getPaymentType()));
            if (isMonthlyOnly) {
                ddsToCancel.add(dd);
                continue;
            }

            // ACC-8596
            if (c.isMaidVisa() &&
                    dd.getContractPayments().stream()
                            .anyMatch(ContractPayment::isRequiredForUnfitToWork)) {
                    continue;
            }
            ddsToCancel.add(dd);
        }

        return ddsToCancel;
    }
    
    public DirectDebitRejectionToDo getDirectDebitToDo(DirectDebit entity) {
        DirectDebitRejectionToDo directDebitRejectionToDo = entity.getDirectDebitRejectionToDo();

        if (directDebitRejectionToDo == null) {
            DirectDebitRejectionFlowService service = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
            service.startFlow(entity);
            return null;
        }

        directDebitRejectionToDo.setLastDirectDebit(entity);
        directDebitRejectionToDo = directDebitRejectionToDoRepository.getOne(directDebitRejectionToDo.getId());

        return directDebitRejectionToDo;
    }

    public Map<String, String> getDDBankDetails(Contract contract) {
        DirectDebit directDebit = null;

        List<DirectDebitStatus> invalidStatuses = Arrays.asList(
                DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION);
        
        ContractPaymentTerm activeCPT = contract.getActiveContractPaymentTerm();
        List<DirectDebit> approvedDDs = directDebitRepository.findByContractPaymentTermAndStatusIn(
                activeCPT, Arrays.asList(DirectDebitStatus.CONFIRMED), invalidStatuses);
        if (approvedDDs != null && !approvedDDs.isEmpty()) {
            logger.info("Approved DD");
            directDebit = approvedDDs.get(0);
        } else {
            List<DirectDebit> pendingDDs = directDebitRepository.findByContractPaymentTermAndStatusIn(
                    activeCPT, Arrays.asList(DirectDebitStatus.PENDING), invalidStatuses);
            if (pendingDDs != null && !pendingDDs.isEmpty()) {
                logger.info("Pending DD");
                directDebit = pendingDDs.get(0);
            } else {
                List<DirectDebit> rejectedDDs = directDebitRepository.findByContractPaymentTermAndStatusIn(
                        activeCPT, Arrays.asList(DirectDebitStatus.REJECTED), invalidStatuses);
                if (rejectedDDs != null && !rejectedDDs.isEmpty()) {
                    logger.info("Rejected DD");
                    directDebit = rejectedDDs.stream()
                            .sorted(Comparator.comparing(DirectDebit::getLastModificationDate).reversed())
                            .collect(Collectors.toList()).get(0);
                }
            }
        }

        logger.info("No DD Found");
        return DDUtils.getDDBankDetails(directDebit);
    }

    public boolean isRequiredBankInfoExist(ContractPaymentTerm cpt) {
        List<Attachment> cptAttachments = cpt.getAttachments();

        //ACC-4657
        if (cptAttachments != null && cptAttachments.stream().anyMatch(attachment ->
            Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR,
                    ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR)
                .contains(attachment.getTag()))) return true;

        if ((cpt.getIbanNumber() == null || cpt.getIbanNumber().isEmpty()) &&
                (cptAttachments == null || cptAttachments.stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN)))) {

            logger.info("missing iban");
            return false;
        }

        if ((cpt.getAccountName() == null || cpt.getAccountName().isEmpty()) &&
                (cptAttachments == null || cptAttachments.stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME))) &&
                 !Contract.ContractSource.CHAT_GPT.equals(cpt.getContract().getSource()))  { // ACC-6534

            boolean existingClient = contractRepository.contractForExistingClientFlow(cpt.getContract().getId()) > 0 ||
                    contractRepository.contractForExistingClientFlowBySourceClientMobileApp(cpt.getContract().getId()) > 0;

            // ACC-6667
            if(hasRejectionForAccountName(existingClient ? cpt.getContract().getClient() : cpt.getContract())){
                logger.info("missing account name");
                return false;
            }
        }

        if ((cpt.getEid() == null || cpt.getEid().isEmpty()) &&
                (cptAttachments == null || cptAttachments.stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID)))) {

            logger.info("missing EID");
            return false;
        }

        return true;
    }

    public boolean hasRejectionForAccountName(BaseEntity c) {
        try {
            if (c instanceof  Contract) {
                return directDebitFileRepository.existsByDirectDebit_ContractPaymentTerm_ContractAndRejectCategory(
                        (Contract) c, DirectDebitRejectCategory.Account);
            }

            return directDebitFileRepository.existsByDirectDebit_ContractPaymentTerm_Contract_ClientAndRejectCategory(
                    (Client) c, DirectDebitRejectCategory.Account);
        } catch(Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public void removeDdBankPhoto(
            DirectDebitFile directDebitFile,
            List<String> tags,
            PicklistItem ibanRejectionReason,
            PicklistItem eidRejectionReason,
            PicklistItem accountNameRejectionReason,
            boolean rejectedBySalesScreen) {

        List<String> loweredTags = tags.stream().map(tag -> tag.toLowerCase()).collect(Collectors.toList());

        DirectDebit ddfDD = directDebitFile.getDirectDebit();
        ContractPaymentTerm cpt = ddfDD.getContractPaymentTerm();

        List<DirectDebit> wrongDds = ddfDD.getDdBankInfoGroup() == null ? Arrays.asList(ddfDD) :
                directDebitRepository.getByContractPaymentTermAnd_StatusOrMStatus_AndDdBankInfoGroup(
                        cpt, DirectDebitStatus.PENDING_DATA_ENTRY,
                        directDebitFile.getDirectDebit().getDdBankInfoGroup());

        if (loweredTags.contains(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN)) {
            if (ibanRejectionReason == null)
                throw new RuntimeException("IBAN Rejection Reason must not be null");

            wrongDds.forEach(dd -> {
                dd.setIbanNumber(null);
                dd.setBankName(null);
                dd.setBank(null);

                dd.getDirectDebitFiles().stream()
                        .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                        .forEach(ddf -> {
                            ddf.setIbanNumber(null);
                            ddf.setBankName(null);
                        });
            });

            cpt.setIbanNumber(null);
            cpt.setBankName(null);
            cpt.setBank(null);
            cpt.setIsIBANRejected(true);
            cpt.setIbanRejectionReason(ibanRejectionReason);
        }

        if (loweredTags.contains(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID)) {
            if (eidRejectionReason == null)
                throw new RuntimeException("EID Rejection Reason must not be null");

            wrongDds.forEach(dd -> {
                dd.setEid(null);

                dd.getDirectDebitFiles().stream()
                        .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                        .forEach(ddf -> {
                            ddf.setEid(null);

                            if(ddf.getDirectDebitSignature() != null) {
                                ddf.getDirectDebitSignature().setEid(null);
                                directDebitSignatureRepository.save(ddf.getDirectDebitSignature());
                            }
                        });
            });

            cpt.setEid(null);
            cpt.setIsEidRejected(true);
            cpt.setEidRejectionReason(eidRejectionReason);

            directDebitSignatureRepository.findByContractPaymentTermAndSignatureStatusAndEidNotNull(
                    cpt, DirectDebitSignatureStatus.UNUSED).forEach(s -> {
                s.setEid(null);
                directDebitSignatureRepository.save(s);
            });

        }

        if (loweredTags.contains(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME)) {
            if (accountNameRejectionReason == null)
                throw new RuntimeException("Account Name Rejection Reason must not be null");

            wrongDds.forEach(dd -> {
                dd.setAccountName(null);

                dd.getDirectDebitFiles().stream()
                        .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                        .forEach(ddf -> ddf.setAccountName(null));
            });

            cpt.setAccountName(null);
            cpt.setIsAccountHolderRejected(true);
            cpt.setAccountNameRejectionReason(accountNameRejectionReason);
        }

        cpt.getAttachments().forEach(attachment -> {
            if (loweredTags.contains(attachment.getTag().toLowerCase())) {
                attachmentRepository.delete(attachment);
            }
        });

        wrongDds.forEach(dd -> {
            dd.getAttachments().forEach(attachment -> {
                if (loweredTags.contains(attachment.getTag().toLowerCase())) {
                    attachmentRepository.delete(attachment);
                }
            });

            dd.getDirectDebitFiles().stream()
                    .filter(ddf -> ddf.getDdStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY))
                    .forEach(ddf -> {
                        ddf.setDdDataEntryNotificationSent(false);
                        // ACC-1588
                        ddf.getAttachments().forEach(attachment -> {
                            if (loweredTags.contains(attachment.getTag().toLowerCase())) {
                                attachmentRepository.delete(attachment);
                            }
                        });

                        ddf.setStatus(DirectDebitFileStatus.NOT_COMPLETED);
                        ddf.setDdStatus(DirectDebitStatus.IN_COMPLETE);
                        directDebitFileRepository.save(ddf);
                    });

            dd.setNonCompletedInfo(true);
            if (dd.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                dd.setStatus(DirectDebitStatus.IN_COMPLETE);
            }
            if (dd.getMStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                dd.setMStatus(DirectDebitStatus.IN_COMPLETE);
            }

            logger.info("rejectedBySalesScreen: " + rejectedBySalesScreen);
            dd.setRejectedBySalesScreen(rejectedBySalesScreen);
            directDebitRepository.save(dd);
        });

        contractPaymentTermRepository.save(cpt);

        ddfDD = directDebitRepository.findOne(ddfDD.getId());
        // ACC-9677
        Setup.getApplicationContext()
                .getBean(IncompleteFlowMissingDdInfoService.class)
                .validateAndStartIncompleteFlowMissingDdInfo(ddfDD);

        updateRejectionFLowUponRejectByDataEntry(wrongDds);
    }

    // SD-51412 -> fix rejection flow task name upon DD rejected by data entry
    private void updateRejectionFLowUponRejectByDataEntry(List<DirectDebit> l) {
        if (l.isEmpty()) return;
        Set<Long> s = new HashSet<>();
        List<DirectDebitRejectionToDo> toDos = new ArrayList<>();

        for (DirectDebit d : l) {
            if (d.getDirectDebitRejectionToDo() != null &&
                    !d.getDirectDebitRejectionToDo().isStopped() &&
                    !d.getDirectDebitRejectionToDo().isCompleted() &&
                    !s.contains(d.getDirectDebitRejectionToDo().getId())) {
                s.add(d.getDirectDebitRejectionToDo().getId());
                toDos.add(d.getDirectDebitRejectionToDo());
            }
            if (d.getDirectDebitBouncingRejectionToDo() != null &&
                    !d.getDirectDebitBouncingRejectionToDo().isStopped() &&
                    !d.getDirectDebitBouncingRejectionToDo().isCompleted() &&
                    !s.contains(d.getDirectDebitBouncingRejectionToDo().getId())) {
                s.add(d.getDirectDebitBouncingRejectionToDo().getId());
                toDos.add(d.getDirectDebitBouncingRejectionToDo());
            }
        }

        if (toDos.isEmpty()) return;

        for (DirectDebitRejectionToDo t : toDos) {
            List<String> currentTasks = t.getCurrentTasks();
            if (currentTasks.isEmpty()) continue;

            if (currentTasks.get(currentTasks.size() - 1) != null && !currentTasks.get(currentTasks.size() - 1).isEmpty()) {
                DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));
                logger.info("todo id: " + t.getId() + "; step: " + step);
                switch (step) {
                    case WAITING_BANK_RESPONSE:
                        t.setTaskName(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
                        directDebitRejectionToDoRepository.silentSave(t);
                        break;
                    case WAITING_BANK_RESPONSE_B_CASE_D:
                        t.setTaskName(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());
                        directDebitRejectionToDoRepository.silentSave(t);
                        break;
                    case WAITING_BANK_RESPONSE_B_BOUNCED:
                        t.setTaskName(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
                        directDebitRejectionToDoRepository.silentSave(t);
                        break;
                }
            }
        }
    }

    public List<DirectDebit> getDirectDebitsOfPayments(
            List<ContractPayment> payments,
            ContractPaymentTerm cpt,
            boolean saveChanges) {

        return getDirectDebitsOfPayments(payments, cpt, saveChanges, new HashMap<>());
    }

    public List<DirectDebit> getDirectDebitsOfPayments(
            List<ContractPayment> payments,
            ContractPaymentTerm cpt,
            boolean saveChanges, Map<String, Object> m) {

        // ACC-8407
        payments = payments.stream().filter(cp -> cp.getAmount() > 0).collect(Collectors.toList());

        Map<String, TreeSet<ContractPayment>> contractPaymentMap = new LinkedHashMap<>();
        List<DirectDebit> result = new ArrayList<>();
        StringBuilder log = new StringBuilder();
        Double lastDdAmount = null;
        Date lastDdDate = null;
        for (ContractPayment payment : payments) {
            log.append("payment id: ").append(payment.getId()).append("; amount: ").append(payment.getAmount())
                    .append("; date: ").append(new LocalDate(payment.getDate()).toString("yyyy-MM-dd"))
                    .append("; one time: ").append(payment.isOneTime())
                    .append("; method: ").append(payment.getPaymentMethod())
                    .append("; prorated: ").append(payment.getIsProRated()).append("; type: ").append(payment.getPaymentType()).append("\n");


            if (payment.getPaymentMethod().equals(PaymentMethod.DIRECT_DEBIT)) {
                String key = "";
                if (payment.getDirectDebit() != null && payment.getDirectDebit().getId() != null) {
                    log.append("payment dd IS not NULL: ").append(payment.getDirectDebit().getId());

                    if (!result.stream().anyMatch(p -> p.getId().equals(payment.getDirectDebit().getId()))) {
                        log.append("adding DD with id ").append(payment.getDirectDebit().getId()).append("\n");
                        result.add(payment.getDirectDebit());
                    }
                    continue;
                }
                //ACC-4779
                lastDdDate = lastDdAmount != null ?
                        lastDdAmount.equals(payment.getAmount()) ? lastDdDate
                                : payment.getDate() : payment.getDate();
                lastDdAmount = payment.getAmount();

                key = payment.isOneTime() ?
                        DateUtil.formatDateDashed(payment.getDate()) :
                        DateUtil.formatDateDashed(lastDdDate) + payment.getAmount();

                key = cpt.getContractPaymentTypes()
                        .stream()
                        .anyMatch(type -> type.getType().equals(payment.getPaymentType())
                                && type.isForceDedicatedDd()) ?
                        key + "_" + payment.getPaymentType().getId() : key;
                log.append("map KEY ").append(key).append("\n");

                if (contractPaymentMap.get(key) == null) {
                    contractPaymentMap.put(key, new TreeSet<>(new Comparator<ContractPayment>() {
                        @Override
                        public int compare(ContractPayment payment1, ContractPayment payment2) {
                            return payment1.getDate().compareTo(payment2.getDate()) == 0 ? 1
                                    : payment1.getDate().compareTo(payment2.getDate());
                            // when dates are the same then consider payment2 > payment1 (insertion order)
                        }
                    }));
                }
                contractPaymentMap.get(key).add(payment);
            }
        }

        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        log.append("contractPaymentMap size: ").append(contractPaymentMap.keySet().size()).append("\n");

        CalculateDiscountsWithVatService calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);

        for (String key : contractPaymentMap.keySet()) {
            TreeSet<ContractPayment> contractPayments = contractPaymentMap.get(key);
            ContractPayment firstPayment = contractPayments.first();
            Double ddAmount = firstPayment.isOneTime() ?
                    contractPayments.stream()
                            .mapToDouble(p -> p.getAmount()).sum() : firstPayment.getAmount();
            Date ddExpiryDate = firstPayment.isOneTime() ?
                    new DateTime(firstPayment.getDate())
                            .plusMonths(ontTimeDDMonthDuration).toDate() :
                    new DateTime(contractPayments.last().getDate())
                            .dayOfMonth().withMaximumValue().toDate();
            DirectDebitType ddType = firstPayment.isOneTime() ?
                    DirectDebitType.ONE_TIME : DirectDebitType.MONTHLY;
            DirectDebitCategory ddCategory = firstPayment.isOneTime() ?
                    DirectDebitCategory.A : DirectDebitCategory.B;
            Double ddAdditionalDiscount = contractPayments.stream()
                    .filter(p -> p.getAdditionalDiscountAmount() != null)
                    .mapToDouble(p -> p.getAdditionalDiscountAmount()).sum();
            Boolean ddProrated = firstPayment.getIsProRated();
            Date ddProratedFrom = firstPayment.getDate();
            Date ddProratedTo = new DateTime(firstPayment.getDate()).dayOfMonth().withMaximumValue().toDate();

            DirectDebit directDebit = createDirectDebit(true,
                    DirectDebitStatus.IN_COMPLETE, DirectDebitStatus.IN_COMPLETE,
                    ddAmount, firstPayment.getContractPaymentTerm(),
                    firstPayment.getDate(), ddExpiryDate, ddType, ddCategory, ddAdditionalDiscount,
                    ddProrated, ddProratedFrom, ddProratedTo, firstPayment.isGenerateManualDDFs());

            result.add(directDebit);
            directDebit.setPayments(new ArrayList<>(contractPayments));

            if (saveChanges) directDebitRepository.save(directDebit);

            contractPayments.forEach(payment -> {
                payment.setDirectDebit(directDebit);
                if (saveChanges) {
                    contractPaymentRepository.save(payment);
                } else {
                    calculateDiscountsWithVatService.updateVatFields(payment, log);
                }
            });
        }
        // sort result by start date
        Collections.sort(result, Comparator.comparing(DirectDebit::getStartDate));

        logger.info(log.toString());
        // ACC-1778
        DDUtils.setDirectDebitsDescription(cpt, result);
        return result;
    }

    public DirectDebit createDirectDebit(
            Boolean nonCompletedInfo, DirectDebitStatus status, DirectDebitStatus MStatus,
            Double amount, ContractPaymentTerm cpt,
            Date startDate, Date expiryDate, DirectDebitType type,
            DirectDebitCategory category, Double additionalDiscount,
            Boolean prorated, Date proratedFrom, Date proratedTo, boolean generateManualDDFs) {

        DirectDebit directDebit = new DirectDebit();
        directDebit.setNonCompletedInfo(nonCompletedInfo);
        directDebit.setStatus(status);
        directDebit.setMStatus(MStatus);
        directDebit.setAmount(amount);
        directDebit.setContractPaymentTerm(cpt);
        directDebit.setStartDate(startDate);
        directDebit.setExpiryDate(expiryDate);
        directDebit.setType(type);
        directDebit.setCategory(category);
        directDebit.setAdditionalDiscount(additionalDiscount);

        // ACC-1511
        if (prorated != null && prorated) {
            directDebit.setProrated(true);
            directDebit.setProratedFrom(proratedFrom);
            directDebit.setProratedTo(proratedTo);
        }
        directDebit.setGenerateManualDDFs(generateManualDDFs);

        return directDebit;
    }

    public List<DirectDebit> getActiveDdsForCancellation(Contract contract) {
        return directDebitRepository.getActiveDdsForCancellation(
                contract, DirectDebitService.notAllowedStatuses, DirectDebitService.notAllowedStatusesWithNotApplicable);
    }

    public List<DirectDebit> getActiveDd(Contract contract) {
        return directDebitRepository.getActiveDD(contract, Arrays.asList(
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED));
    }

    public boolean hasActiveDD(Contract contract) {
        return directDebitRepository.hasActiveDD(contract, Arrays.asList(
            DirectDebitStatus.CANCELED,
            DirectDebitStatus.PENDING_FOR_CANCELLATION,
            DirectDebitStatus.EXPIRED));
    }

    public boolean hasActiveDDWithoutRejected(Contract contract) {
        return directDebitRepository.hasActiveDD(contract, Arrays.asList(
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED,
                DirectDebitStatus.REJECTED));
    }

    public boolean hasActiveDdb(Long contractId) {
        return directDebitRepository.hasActiveDdb(contractId, Arrays.asList(
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED));
    }

    public boolean cptHasConfirmedDd(ContractPaymentTerm cpt) {

        return directDebitRepository.cptHasConfirmedDd(cpt);
    }

    // ACC-5798
    public boolean closeMainDDCTodo(Contract c) {


        if (Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_STOP_CLOSE_MAIN_DDC_TODO)
                .equalsIgnoreCase("true")) {
            return false;
        }

        logger.info("close DDC todo -> c id: " + c.getId());

        try {
            Setup.getApplicationContext()
                    .getBean(InterModuleConnector.class)
                    .call("sales",
                            "appsServiceDDApprovalTodoController",
                            "closeMainDDCTodo",
                            Object.class,
                            new Class[]{Long.class},
                            c.getId());

        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    public void deleteNonCompleteFiles(DirectDebit dd) {
        if (dd.getDirectDebitFiles() == null) return;

        logger.log(Level.INFO, "notCompletedDd DD files size: " + dd.getDirectDebitFiles().size());

        dd.getDirectDebitFiles()
                .stream().filter(f -> f.getStatus().equals(DirectDebitFileStatus.NOT_COMPLETED))
                .forEach(f -> directDebitFileRepository.delete(f));

        if (!dd.getDirectDebitFiles().isEmpty()) dd.getDirectDebitFiles().clear();
    }

    public List<DirectDebitFile> createDirectDebitFiles(
            DirectDebit directDebit,
            List<DirectDebitSignature> currentSignatures,
            DirectDebitMethod method,
            int numOfDDs) {

        List<DirectDebitFile> files = new ArrayList<>();

        for (int i = 0; i < numOfDDs; i++) {
            DirectDebitSignature signature = directDebitSignatureService.selectSignature(currentSignatures, numOfDDs, 0, i);
            files.add(createDDFile(directDebit, signature, true, true, method, null));
        }

        return files;
    }

    public void createDirectDebitFileFromSignatures(
            DirectDebit directDebit,
            List<DirectDebitSignature> currentSignatures,
            boolean isCompletedBankInfo,
            int ddmIndex) {

        Integer numOfDDs  = directDebit.getDdConfiguration().getNumberOfGeneratedDDs();
        if (currentSignatures != null) numOfDDs =  Math.min(numOfDDs, currentSignatures.size());

        logger.info("numOfDDs: " + numOfDDs);

        List<DirectDebitFile> directDebitFiles = new ArrayList();

        for (int i = 0; i < numOfDDs; i++) {
            DirectDebitSignature signature = directDebitSignatureService.selectSignature(currentSignatures, numOfDDs, ddmIndex, i);

            if (directDebit.getCategory().equals(DirectDebitCategory.A) ||
                    (directDebit.getCategory().equals(DirectDebitCategory.B) && directDebit.isGenerateManualDDFsFromConfig())) {

                directDebitFiles.add(createDDFile(directDebit, signature, isCompletedBankInfo, true, DirectDebitMethod.MANUAL, null));
            }

            if (directDebit.getCategory().equals(DirectDebitCategory.B)) {
                directDebitFiles.add(createDDFile(directDebit, signature, isCompletedBankInfo, true, DirectDebitMethod.AUTOMATIC, null));
            }
        }

        if(directDebit.getDirectDebitFiles() == null) {
            directDebit.setDirectDebitFiles(directDebitFiles);
        } else {
            directDebit.getDirectDebitFiles().addAll(directDebitFiles);
        }
    }

    public List<DirectDebitFile> regenerateFilesForDdb(
            DirectDebit d,
            int ddIndex,
            DirectDebitMethod directDebitMethod,
            List<DirectDebitSignature> currentSignatures) {

        Integer numOfDDs  = d.getDdConfiguration().getNumberOfGeneratedDDs();
        List<DirectDebitFile> directDebitFiles = new ArrayList<>();

        if (d.getDirectDebitFiles().stream()
                .filter(ddf -> ddf.getDdMethod()
                        .equals(directDebitMethod)).count() != numOfDDs) {

            List<DirectDebitFile> l = d.getDirectDebitFiles()
                    .stream()
                    .filter(ddf -> ddf.getDdMethod().equals(directDebitMethod) &&
                            Arrays.asList(DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.PENDING)
                                    .contains(ddf.getDdStatus()))
                    .collect(Collectors.toList());

            l.forEach(ddfId -> directDebitFileRepository.delete(ddfId));

            for (int j = 0; j < numOfDDs; j++) {
                DirectDebitSignature signature = directDebitSignatureService.selectSignature(currentSignatures, numOfDDs, ddIndex, j);
                directDebitFiles.add(createDDFile(d, signature, true, true, directDebitMethod, null));

            }
        }

       return directDebitFiles;
    }

    public DirectDebitFile createDDFile(
            DirectDebit directDebit, DirectDebitSignature signature,
            boolean isCompletedBankInfo, boolean generateActivationFile,
            DirectDebitMethod ddMethod, DirectDebitType ddFrequency) {

        DirectDebitFile directDebitFile = new DirectDebitFile();
        directDebitFile.setDirectDebit(directDebit);

        if (signature != null) {
            directDebitFile.setDirectDebitSignature(signature);
            directDebit.setIsSigned(true);
        }

        // ACC-1588
        directDebitFile.setDdMethod(ddMethod);
        directDebitFile.copyDDInfo(directDebit, isCompletedBankInfo);

        if (ddFrequency != null) directDebitFile.setDdFrequency(ddFrequency);
        if (!isCompletedBankInfo) directDebitFile.setStatus(DirectDebitFileStatus.NOT_COMPLETED);

        directDebitFileRepository.save(directDebitFile);

        if (generateActivationFile) createDirectDebitActivationAttachmentIfNotExist(directDebitFile);

        return directDebitFile;
    }

    public Boolean createDirectDebitActivationAttachmentIfNotExist(DirectDebitFile directDebitFile) {
        logger.log(Level.INFO, "directDebitFile id : " + directDebitFile.getId());

        directDebitFile = directDebitFileRepository.findOne(directDebitFile.getId());

        DirectDebit dd = directDebitFile.getDirectDebit();
        if (dd == null || !directDebitFile.getConfirmedBankInfo() ||
                directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION) != null ||
                directDebitFile.getSignatureAttachment() == null) {

            logger.log(Level.INFO, "ConfirmedBankInfo : " + directDebitFile.getConfirmedBankInfo() +
                    "; has DD activation file: " + (directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION) != null) +
                    "; has a signature: " + (directDebitFile.getSignatureAttachment() != null));

            return false;
        }

        InputStream fileStream = generateDDForm(directDebitFile, null, true);
        if (fileStream == null) return false;

        try {
            Attachment directDebitAttachmentFile = Storage.storeTemporary("Direct Debit Activation.pdf", fileStream,
                    DirectDebitFile.FILE_TAG_DD_ACTIVATION, true);
            directDebitFile.addAttachment(directDebitAttachmentFile);
            directDebitFileRepository.save(directDebitFile);
        } finally {
            StreamsUtil.closeStream(fileStream);
        }
        return true;
    }

    public InputStream generateDDForm(DirectDebitFile directDebitFile, Date generationDate, boolean withSignature) {
        DirectDebitSignatureService signatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);

        Map<String, Object> parameters = getTemplateParametersMap(directDebitFile, generationDate);
        logger.info("generateDDForm");

        if (withSignature) {
            Attachment signature = directDebitFile.getSignatureAttachment();
            parameters.put("signature", signature == null ? "" :
                    signatureService.getSignatureImage(
                            signatureService.getImageByteArray(Storage.getStream(signature))));
        } else {
            parameters.put("signature", "");
        }

        logger.info("generateDocument");

        return wordTemplateService.generateDocument(directDebitFile.getDdFrequency().getTemplateName(), parameters);
    }

    private Map<String, Object> getTemplateParametersMap(DirectDebitFile directDebitFile, Date generationDate) {
        // load all relations for direct debit
        DirectDebit directDebit = directDebitRepository.findOne(directDebitFile.getDirectDebit().getId());

        Map<String, Object> parameters = new HashMap<>();
        ContractPaymentTerm contractPaymentTerm = directDebit.getContractPaymentTerm();

        parameters.put("contract_id", utils.getWordTemplateParamValue(directDebitFile.getApplicationId()));
        parameters.put("bank_name", utils.getWordTemplateParamValue(directDebitFile.getBankName()));
        parameters.put("account_name", utils.getWordTemplateParamValue(directDebitFile.getAccountName()));
        //SD-9008, leave the email as empty
        parameters.put("client_email", "");//contractPaymentTerm.getContract().getClient().getEmail() != null ? contractPaymentTerm.getContract().getClient().getEmail() :"");

        // Direct Debit EID Parameters
        String directDebitEid = directDebitFile.getEid();
        if (directDebitEid == null) {
            directDebitEid = "";
        }
        for (int i = 0; i < 20; i++) {
            parameters.put("id" + (i + 1),
                    utils.getWordTemplateParamValue(i < directDebitEid.length() ? directDebitEid.charAt(i) : ""));
        }

        // Client IBAN Parameters
        String ibanNumber = directDebitFile.getIbanNumber();
        if (ibanNumber == null) {
            ibanNumber = "";
        }
        for (int i = 0; i < 32; i++) {
            parameters.put("ic" + (i + 1),
                    utils.getWordTemplateParamValue(i < ibanNumber.length() ? ibanNumber.charAt(i) : ""));
        }

        // Client Mobile Number Parameters
        String mobileNumber = contractPaymentTerm.getContract().getClient().getNormalizedMobileNumber();
        if (Utils.isEmiratiMobileNumber(mobileNumber)) {
            mobileNumber = "0" + mobileNumber.substring(3);
        } else {
            mobileNumber = "";
        }
        for (int i = 0; i < 10; i++) {
            parameters.put("mn" + (i + 1),
                    utils.getWordTemplateParamValue(i < mobileNumber.length() ? mobileNumber.charAt(i) : ""));
        }

        // Start Date Parameters
        String startDateDay = DateUtil.formatDayNumber(directDebitFile.getStartDate());
        if (startDateDay != null) {
            for (int i = 0; i < startDateDay.length(); i++) {
                parameters.put("cd" + (i + 1), utils.getWordTemplateParamValue(startDateDay.charAt(i)));
            }
        }
        String startDateMonth = DateUtil.formatMonthNumber(directDebitFile.getStartDate());
        if (startDateMonth != null) {
            for (int i = 0; i < startDateMonth.length(); i++) {
                parameters.put("cm" + (i + 1), utils.getWordTemplateParamValue(startDateMonth.charAt(i)));
            }
        }
        String startDateYear = DateUtil.formatYear(directDebitFile.getStartDate());
        if (startDateYear != null) {
            for (int i = 0; i < startDateYear.length(); i++) {
                parameters.put("cy" + (i + 1), utils.getWordTemplateParamValue(startDateYear.charAt(i)));
            }
        }

        // End Date Parameters
        String endDateDay = DateUtil.formatDayNumber(directDebitFile.getExpiryDate());
        if (endDateDay != null) {
            for (int i = 0; i < endDateDay.length(); i++) {
                parameters.put("ed" + (i + 1), utils.getWordTemplateParamValue(endDateDay.charAt(i)));
            }
        }
        String endDateMonth = DateUtil.formatMonthNumber(directDebitFile.getExpiryDate());
        if (endDateMonth != null) {
            for (int i = 0; i < endDateMonth.length(); i++) {
                parameters.put("em" + (i + 1), utils.getWordTemplateParamValue(endDateMonth.charAt(i)));
            }
        }
        String endDateYear = DateUtil.formatYear(directDebitFile.getExpiryDate());
        if (endDateYear != null) {
            for (int i = 0; i < endDateYear.length(); i++) {
                parameters.put("ey" + (i + 1), utils.getWordTemplateParamValue(endDateYear.charAt(i)));
            }
        }

        // Fixed Amount Parameters
        String Amount = String.format("%.2f", directDebitFile.getAmount());
        Amount = Amount.replaceAll("\\.", "");
        if (Amount != null) {
            int length = Amount.length();
            for (int i = 0; i < 12; i++) {
                parameters.put("amt" + (i + 1),
                        utils.getWordTemplateParamValue(i < Amount.length() ? Amount.charAt(length - i - 1) : ""));
            }
        }

        parameters.put("current_date", utils.getWordTemplateParamValue(DateUtil.formatNotDashedFullDate(generationDate != null ? generationDate : new Date())));
        //Jirra ACC-2838
        PicklistItem bank = null;
        if (directDebit != null && directDebit.getBank() != null && directDebit.getBank().getId() != null) {
            PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
            bank = picklistItemRepository.findOne(directDebit.getBank().getId());
        }

        if (bank != null && bank.hasTag("add_manger_name_to_dd_form")) {
            parameters.put("manager_name", utils.getWordTemplateParamValue(
                    Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_FORM_MANAGER_NAME)));
        } else {
            parameters.put("manager_name", "");
        }

        return parameters;
    }

    public void copyBankInfo(
            ContractPaymentTerm contractPaymentTerm,
            DirectDebit directDebit,
            Long bankInfoGroup,
            Boolean forceDataEntry) {

        logger.info("copying bank info into DD " + directDebit.getId());

        directDebit.setDdBankInfoGroup(bankInfoGroup);
        directDebit.setEid(contractPaymentTerm.getEid());
        directDebit.setIbanNumber(contractPaymentTerm.getIbanNumber());
        directDebit.setAccountName(contractPaymentTerm.getAccountName());
        directDebit.setBankName(contractPaymentTerm.getBankName());
        directDebit.setBank(contractPaymentTerm.getBank());
        directDebit.setConfirmedBankInfo(!forceDataEntry && isBankInfoConfirmedBefore(directDebit));

        if (directDebit.getConfirmedBankInfo()) {
            directDebit.setStatus(DirectDebitStatus.PENDING);
            directDebit.setMStatus(DirectDebitStatus.PENDING);
        }

        List<Attachment> cptAttachments = contractPaymentTerm.getAttachments();

        if (cptAttachments != null) { //ACC-4657
            if (cptAttachments.stream().anyMatch(att -> att.getTag()
                    .equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR))) {

                directDebit.getAttachments().stream()
                        .filter(att -> BANK_INFO_TAGS.contains(att.getTag()))
                        .forEach(att -> attachmentRepository.delete(att));
            }

            cptAttachments.stream().filter(att ->
                    Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                                    ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN,
                                    ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                                    ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR)
                            .contains(att.getTag())
            ).forEach(att -> directDebit.addAttachment(att));
        }
    }

    public Boolean isBankInfoConfirmedBefore(DirectDebit directDebit) {
        return !directDebit.getNonCompletedInfo() && isBankInfoConfirmedBefore(directDebit.getContractPaymentTerm().getContract().getClient(),
                directDebit.getIbanNumber(), directDebit.getEid(), directDebit.getAccountName());
    }

    public Boolean isBankInfoConfirmedBefore(Client client, String iban, String eid, String accountName) {
        return iban != null && !iban.isEmpty()
                && eid != null && !eid.isEmpty()
                && accountName != null && !accountName.isEmpty()
                && directDebitRepository.existsByIbanNumberAndEidAndAccountNameAndConfirmedBankInfoAndContractPaymentTerm_Contract_Client(
                iban, eid, accountName, true, client);
    }

    public boolean contractHasOpenMainDdcToDo(Long contactId) {
        boolean r = Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .get("/sales/appsserviceddapprovaltodo/contracthasopenmainddctodo?contract=" + contactId,
                Boolean.class);
        logger.info("contactId " + contactId + " r " + r);
        return r;
    }

    public boolean contractHasClosedMainDdcToDo(Long contactId, AppsServiceDDApprovalTodo.DdcTodoType todoType) {
        boolean r = Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .get("/sales/appsserviceddapprovaltodo/contracthastodo?" +
                                "contract=" + contactId +
                                "&isClosed=true" +
                                "&todoType=" + todoType,
                        Boolean.class);
        logger.info("contactId " + contactId + " r " + r);
        return r;
    }

    public boolean contractHasOpenDdcToDo(Long contactId) {
        boolean r = Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .get("/sales/appsserviceddapprovaltodo/contracthastodo?" +
                                "contract=" + contactId +
                                "&isClosed=false",
                        Boolean.class);
        logger.info("contactId " + contactId + " r " + r);
        return r;
    }

    public void setDirectDebitBankInfo(
            DirectDebit directDebit,
            String iban,
            String bankName,
            PicklistItem bank,
            String eid,
            String accountName,
            DirectDebitStatus aStatus,
            DirectDebitStatus MStatus,
            DirectDebitFile ddf) {

        directDebit.setIbanNumber(iban);
        directDebit.setBankName(bankName);
        directDebit.setBank(bank);
        directDebit.setEid(eid);
        directDebit.setAccountName(accountName);
        directDebit.setConfirmedBankInfo(true);

        if (aStatus != null) directDebit.setStatus(aStatus);
        if (MStatus != null) directDebit.setMStatus(MStatus);

        directDebit.setDataEntryNotes(null);

        //ACC-4657
        directDebit.getAttachments().stream()
                .filter(att -> att.getTag().equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR))
                .forEach(att -> attachmentRepository.delete(att));
        ddf.getAttachments().stream().filter(att -> BANK_INFO_TAGS.contains(att.getTag()))
                .forEach(att-> directDebit.addAttachment(att));
    }

    // ACC-6189
    public boolean validateRecreateFiles(
            DirectDebit dd, DirectDebitConfiguration oldConfig, DirectDebitConfiguration newConfig,
            List<DirectDebitSignature> directDebitSignatures, String eid, int i) {

        if (oldConfig == null ||
                (oldConfig.getNumberOfGeneratedDDs().equals(newConfig.getNumberOfGeneratedDDs()) &&
                        oldConfig.isCreateManualForDDB() == newConfig.isCreateManualForDDB()))
            return false;

        logger.info("The bank config was changed -> regenerate files id: " + dd.getId());
        List<DirectDebitFile> oldDdf = dd.getDirectDebitFiles();
        dd.setDirectDebitFiles(new ArrayList<>());

        createDirectDebitFileFromSignatures(dd, directDebitSignatures, true, i);
        directDebitRepository.save(dd);

        oldDdf.forEach(ddfId -> directDebitFileRepository.delete(ddfId));

        dd.getDirectDebitFiles()
                .stream()
                .filter(d -> d.getDirectDebitSignature() != null)
                .forEach(d -> {
                    d.getDirectDebitSignature().setEid(eid);
                    directDebitSignatureRepository.save(d.getDirectDebitSignature());
                });

        dd.setMStatus(dd.getMStatus());
        return true;
    }

    // ACC-5055
    public boolean isFirstDataEntryConfirmed(DirectDebit dd) {
        DirectDebit firstDD = directDebitRepository
                .findTopByContractPaymentTerm_Contract_ClientAndConfirmedBankInfoTrueOrderByConfirmBankInfoDate(
                        dd.getContractPaymentTerm().getContract().getClient());

        return firstDD == null ||
                (dd.getDdBankInfoGroup() != null
                    && dd.getDdBankInfoGroup().equals(firstDD.getDdBankInfoGroup()));
    }

    // ACC-5055
    public Map canUpdateClientName(DirectDebit dd) {
        boolean hasOnlineCreditCard = Setup.getRepository(PaymentRepository.class)
            .existsByMethodOfPaymentAndOnlineAndContract_ClientAndStatusNotIn(PaymentMethod.CARD,
                    true, dd.getContractPaymentTerm().getContract().getClient(),
                    Arrays.asList(PaymentStatus.DELETED,
                            PaymentStatus.CANCELLED));
        // ACC-6021
        boolean isFirstDataEntryConfirmed = isFirstDataEntryConfirmed(dd);

        return  new HashMap<String, Object>(){{
            put("hideClientInfo", !isFirstDataEntryConfirmed);
            put("canUpdateClientName", isFirstDataEntryConfirmed && !hasOnlineCreditCard);
        }};
    }

    public List<String> getMissingBankInfo(ContractPaymentTerm cpt) {
        List<String> missingInfo = new ArrayList<>();

        if (!Setup.getApplicationContext()
                .getBean(DirectDebitSignatureService.class).hasSignature(cpt)) {
            missingInfo.add("Signatures");
        }

        boolean rejectDataEntryRunning = cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected();
        if (!rejectDataEntryRunning && cpt.getAttachments().stream().anyMatch(attachment ->
                Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR,
                                ContractPaymentTermController.FILE_TAG_PAPER_MODE_BANK_INFO_PENDING_OCR)
                        .contains(attachment.getTag()))) {
            return missingInfo;
        }

        if ((cpt.getIbanNumber() == null || cpt.getIbanNumber().isEmpty()) &&
                (cpt.getAttachments().stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN)))) {

            missingInfo.add("IBAN");
        }

        if ((cpt.getAccountName() == null || cpt.getAccountName().isEmpty()) &&
                (cpt.getAttachments().stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME))) &&
                (hasRejectionForAccountName(cpt.getContract().getClient()) || cpt.getIsAccountHolderRejected())) {

            missingInfo.add("Account name");
        }

        if ((cpt.getEid() == null || cpt.getEid().isEmpty()) &&
                (cpt.getAttachments().stream()
                        .noneMatch(a -> a.getTag().equalsIgnoreCase(
                                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID)))) {

            missingInfo.add("EID");
        }

        return missingInfo;
    }

    public void handleCreateNonMonthlyDdForPayingViaCcFlows(DirectDebit dd) {
        if (dd.getContractPaymentTerm() == null ||
                dd.getPayments() == null ||
                dd.getPayments().isEmpty() ||
                dd.getPayments().stream()
                        .anyMatch(c -> PaymentHelper.isMonthlyPayment(c.getPaymentType())) ||
                !flowProcessorService.isPayingViaCreditCard(dd.getContractPaymentTerm().getContract())) return;

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "handleCreateNonMonthlyDdForPayingViaCcFlows_" + dd.getId(),
                        "accounting",
                        "directDebitService",
                        "handleCreateNonMonthlyDdForPayingViaCcFlows")
                        .withRelatedEntity("DirectDebit", dd.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {dd.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(30L * 1000L)
                        .build());

    }

    public void handleCreateNonMonthlyDdForPayingViaCcFlows(Long ddId) {
        DirectDebit dd = directDebitRepository.findOne(ddId);

        if (!flowProcessorService.isPayingViaCreditCard(dd.getContractPaymentTerm().getContract())) return;

        ContractPaymentConfirmationToDo t = Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .createConfirmationTodoFromContractPayments(
                        dd.getContractPaymentTerm(),
                        dd.getContractPayments(),
                        ContractPaymentConfirmationToDo.Source.PAYMENT_REMINDER);
        Setup.getApplicationContext()
                .getBean(UnpaidOnlineCreditCardPaymentService.class)
                .createPaymentReminderFlow(t, dd.getContractPaymentTerm());

        directDebitCancellationService.cancelWholeDD(dd, false, DirectDebitCancellationToDoReason.CLIENT_PAYING_VIA_Credit_Card_FLOW);
    }

    public void moveNotReceivedDdaAndNotMonthlyToNewCpt(ContractPaymentTerm cpt, ContractPaymentTerm newCpt) {

        // All one time
        SelectQuery<ContractPayment> query = new SelectQuery<>(ContractPayment.class);
        query.filterBy("contractPaymentTerm.id", "=", cpt.getId());
        query.filterBy("paymentMethod", "=", PaymentMethod.DIRECT_DEBIT);
        query.filterBy("paymentType.code", "<>", "monthly_payment");
        query.filterBy("directDebit.status", "not in", notAllowedStatuses);
        query.filterBy("directDebit.MStatus", "not in", notAllowedStatuses);
        query.filterBy("directDebit.category", "=", DirectDebitCategory.A);

        //check if payment received
        query.execute()
                .stream()
                .filter(cp ->
                        !paymentRepository.existsByStatusAndContractAndDateOfPaymentAndAmountOfPaymentAndTypeOfPayment(
                                PaymentStatus.RECEIVED, cpt.getContract(), cp.getDate(),
                                cp.getAmount(), cp.getPaymentType()))
                .forEach(cp -> {
                    logger.info("cp id: " + cp.getId());
                    cp.setContractPaymentTerm(newCpt);
                    contractPaymentRepository.save(cp);
                    logger.info("dd id: " + cp.getDirectDebit().getId());
                    cp.getDirectDebit().setContractPaymentTerm(newCpt);
                    directDebitRepository.saveAndFlush(cp.getDirectDebit());
                });
    }

    // ACC-7286
    public boolean handleCreateNonMonthlyDdToBeCollectedByCreditCard(DirectDebit dd) {
        if (dd.isAddedManuallyFromClientProfile() ||
                dd.getContractPaymentTerm() == null ||
                dd.getPayments() == null ||
                dd.getPayments().isEmpty()) return false;

        String s = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_TYPES_TO_BE_COLLECTED_BY_CREDIT_CARD);
        if (s.isEmpty()) return false;

        Map<String, Map<String, String>> codes = new HashMap<>();
        try {
            codes = objectMapper.readValue(s, Map.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map finalCodes = codes;
        if (dd.getPayments().stream()
                        .anyMatch(c -> c.getPaymentType() == null ||
                                c.getPaymentType().getCode() == null ||
                                !finalCodes.containsKey(c.getPaymentType().getCode()))) return false;

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "handleCreateNonMonthlyDdToBeCollectedByCreditCard" + dd.getId(),
                        "accounting",
                        "directDebitService",
                        "handleCreateNonMonthlyDdToBeCollectedByCreditCard")
                        .withRelatedEntity("DirectDebit", dd.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {dd.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(60L * 1000L)
                        .build());

        return true;
    }

    // to fix temp issue on the Production
    @Deprecated
    public void handleCreateNonMonthlyDdToBeCollectedByCreditCard(Long ddIdd, Boolean required) {
        handleCreateNonMonthlyDdToBeCollectedByCreditCard(ddIdd);
    }

    public void handleCreateNonMonthlyDdToBeCollectedByCreditCard(Long ddId) {
        DirectDebit dd = directDebitRepository.findOne(ddId);
        if (dd.isAddedManuallyFromClientProfile()) return;

        Map<String, Object> m =  new HashMap<>();
        m.put("relatedToEntityId", dd.getRelatedEntityId());
        m.put("relatedToEntityType", dd.getRelatedEntityType());
        ContractPaymentConfirmationToDo t = Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .createConfirmationTodoFromContractPayments(
                        dd.getContractPaymentTerm(),
                        dd.getContractPayments(),
                        ContractPaymentConfirmationToDo.Source.PAYMENT_REMINDER,
                        m);

        Setup.getApplicationContext()
                .getBean(UnpaidOnlineCreditCardPaymentService.class)
                .createPaymentReminderFlow(t, dd.getContractPaymentTerm());

        directDebitCancellationService.cancelWholeDD(dd, false, DirectDebitCancellationToDoReason.CLIENT_PAYING_VIA_Credit_Card_FLOW);
    }

    public boolean hasConfirmedDdb(Contract c, DateTime date) {
        if (date == null) {
            date = Setup.getApplicationContext().getBean(PaymentService.class)
                    .getLastReceivedMonthlyPaymentDate(c);
            date = date == null ? null : date.plusMonths(1).dayOfMonth().withMinimumValue();
        }

        if (date == null) {
            date = new DateTime(c.getStartOfContract());
        }

        List<DirectDebit> dds = directDebitRepository.findConfirmedDDbByActiveCpt(c, date.toDate());
        if (dds.isEmpty()) return false;

        DateTime ddbStartDate = new DateTime(dds.get(0).getStartDate());
        if(ddbStartDate.isBefore(date.plusSeconds(1))) return true;

        Set<String> dates = new HashSet<>();
        contractPaymentRepository.getAllContractPaymentDateBetweenTwoDate(c, dds.get(0).getId(), date.toDate(), ddbStartDate.toDate())
                .forEach(d -> dates.add(new DateTime(d).toString("yyyy-MM")));

        for (DateTime d = date; d.isBefore(ddbStartDate); d = d.plusMonths(1)) {
            if (!dates.contains(d.toString("yyyy-MM"))) return false;
        }

        return true;
    }

    public boolean existsByDdStatuses(ContractPaymentTerm cpt, List<DirectDebitStatus> l) {
        return Setup.getRepository(DirectDebitRepository.class)
                .existsByContractPaymentTermAndCategoryBAndStatusIn(
                        cpt,
                        Arrays.asList(DirectDebitCategory.A, DirectDebitCategory.B),
                        l);
    }

    public List<DirectDebit> findByDdStatuses(ContractPaymentTerm cpt, List<DirectDebitStatus> l) {
        return Setup.getRepository(DirectDebitRepository.class)
                .findByContractPaymentTermAndCategoryBAndStatusIn(
                        cpt,
                        Arrays.asList(DirectDebitCategory.A, DirectDebitCategory.B),
                        l);
    }

    public void reactivateDDsFlowAfterReactivateContract(ContractPaymentTerm cpt) throws Exception {

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("id", "=", cpt.getContract().getId());
        query.filterBy("scheduledDateOfTermination", "is not null", null);
        query.filterByChanged("scheduledDateOfTermination");
        query.sortBy("lastModificationDate", false, true);
        query.setLimit(1);

        List<Contract> oldContract = query.execute();
        if (oldContract.isEmpty()) return;

        if (cpt.getIsIBANRejected()) cpt.setIsIBANRejected(false);
        if (cpt.getIsEidRejected()) cpt.setIsEidRejected(false);
        if (cpt.getIsAccountHolderRejected()) cpt.setIsAccountHolderRejected(false);
        cpt = contractPaymentTermRepository.save(cpt);

        reGenerateDdsAfterReactivateContract(cpt, oldContract.get(0).getLastModificationDate());

        DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository = Setup.getRepository(DirectDebitCancelationToDoRepository.class);
        // Close any to-do to Cancel Future DDs
        for (DirectDebitCancelationToDo d : directDebitCancelationToDoRepository.findByContractAndCreationDate(cpt.getContract(), oldContract.get(0).getLastModificationDate())) {
            logger.info("Cancellation todo id: " + d.getId());
            if (dontStopDirectDebitCancellationToDo(d.getDirectDebit())) {
                logger.info("Found received payment for DD: " + d.getDirectDebit().getId() +
                        " -> skipping cancellation todo from stopping");
                continue;
            }
            d.setStopped(true);
            directDebitCancelationToDoRepository.save(d);
        }

        sendSwitchNationalityEmailForContractReactivated(cpt.getContract(), oldContract.get(0).getLastModificationDate());
    }

    private void sendSwitchNationalityEmailForContractReactivated(Contract c, Date d) {
        logger.info("contract id: " + c.getId());
        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);

        if (!accountingEntityPropertyRepository.existsByIsDeletedTrueKeyInAndOriginAndAfterDateOfTermination(
                Arrays.asList(
                        Contract.UPGRADING_NATIONALITY_DATE,
                        Contract.DOWNGRADING_NATIONALITY_DATE,
                        Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID), c.getId(), d)) return;

        Map<String, String> parameters = new HashMap<String, String>(){{
            put("contract_id", c.getId().toString());
        }};

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("contract_reactivated_while_switch_nationality_flow_running",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CONTRACT_REACTIVATED_WHILE_SWITCH_NATIONALITY_FLOW_RUNNING_EMAIL_RECIPIENTS),
                        "Contract Reactivated with Switching Nationality Flow");
    }

    private void reGenerateDdsAfterReactivateContract(ContractPaymentTerm cpt, Date dateOfTermination) throws Exception {
        logger.info("cpt id: " + cpt.getId() + " old contract dateOfTermination: " + dateOfTermination);

        List<DirectDebit> l = directDebitRepository.findAllCanceledDDForAcc6594(
                cpt.getContract().getId(), dateOfTermination);
        List<ContractPayment> contractPayments = new ArrayList<>();

        if (!l.isEmpty()) {
            List<DirectDebit> newDirectDebits = regenerateDdsFromCanceledDds(l, cpt);

            if (!newDirectDebits.isEmpty()) {
                logger.info("newDirectDebits size: " + newDirectDebits.size());

                for (DirectDebit d : newDirectDebits) {
                    logger.info("newDirectDebit id: " + d.getId() +
                            "; contract Payment size: " + d.getPayments().size());
                    contractPayments.addAll(d.getPayments());
                }

                Collections.reverse(contractPayments);
                contractPayments = contractPaymentService.getUniqueAndSortedPayments(cpt.getContract(), contractPayments, null);
            }
        }

        if (contractPayments.isEmpty()) {
            logger.info("Couldn't find old DDs");
            if (Setup.getApplicationContext()
                    .getBean(FlowProcessorService.class)
                    .isPayingViaCreditCard(cpt.getContract()) ||
                directDebitRepository.existsActiveDdByCptAndStatus(cpt,
                            Arrays.asList(
                                DirectDebitStatus.PENDING,
                                DirectDebitStatus.CONFIRMED,
                                DirectDebitStatus.IN_COMPLETE,
                                DirectDebitStatus.PENDING_DATA_ENTRY))) return;

            generateDdsBasedOnCpt(cpt);
            return;
        }

        // SAVE PAYMENTS AND GENERATE DDS for each dd
        logger.info("contractPayments size: " + contractPayments.size());
        if(contractPayments.isEmpty()) return;

        List<String> paymentIDs = contractPayments.stream()
                .map(cp -> cp.getId().toString())
                .collect(Collectors.toList());
        logger.info("paymentIDs: "+ paymentIDs);

        // Generate new dds
        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService.
                getLastSignatureType(cpt, true, false).get("currentSignatures");
        Setup.getApplicationContext().getBean(ContractPaymentTermController.class)
                .createBGTToUpdateContractPaymentTermWithPayments(
                        cpt, true, false, paymentIDs,
                        signatures == null ? new ArrayList<>() :
                                directDebitSignatureService.getSignatureAttachmentsOnly(signatures)
                        , true, false);
    }

    // ACC-9336
    public boolean dontStopDirectDebitCancellationToDo(DirectDebit dd) {
        if (!dd.getCategory().equals(DirectDebitCategory.A)) {
            return false;
        }

        // If there is a received card payment that covers the whole DDA, we should keep the cancellation to-do open
        for (ContractPayment contractPayment : dd.getContractPayments()) {
            // Check if there's a received payment for this contract payment
            if (paymentRepository.paymentReceived(
                    contractPayment.getContractPaymentTerm().getContract(),
                    contractPayment.getPaymentType(),
                    new LocalDate(contractPayment.getDate()).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate(contractPayment.getDate()).dayOfMonth().withMaximumValue().toDate())) {
                return true;
            }
        }

        return false;
    }

    private void generateDdsBasedOnCpt(ContractPaymentTerm cpt) throws Exception {
        logger.info("cpt id: " + cpt.getId());
        // get Default DDs By cpt in parameter of method And Save Payments and generate dds
        cpt.setForceGenerateDds(true);
        List<ContractPayment> contractPayments = (List<ContractPayment>) contractPaymentTermServiceNew
                .getContractPaymentTermByContractWithDirectDebitPayments(cpt).get("payments");

        contractPayments.removeIf(p -> new LocalDate(p.getDate()).isBefore(new LocalDate(cpt.getContract().getPaidEndDate())));

        // SAVE PAYMENTS AND GENERATE DDS for each dd
        logger.info("contractPayments size: " + contractPayments.size());
        if(contractPayments.isEmpty()) return;

        ContractPayment payment = contractPayments.stream()
                .filter(p -> PaymentHelper.isMonthlyPayment(p.getPaymentType()))
                .findFirst()
                .orElse(null);
        if (payment != null && !payment.isOneTime()) {
            payment.setOneTime(true);
        }

        // Generate new dds
        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService.
                getLastSignatureType(cpt, true, false).get("currentSignatures");
        contractPaymentTermServiceNew.updateContractPaymentTermWithPayments(
                cpt, true, false, contractPayments,
                signatures == null ? new ArrayList<>() :
                        directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                true, signatures == null, true,
                false, false, true, true, new HashMap<>());
    }

    private List<DirectDebit> regenerateDdsFromCanceledDds(List<DirectDebit> l, ContractPaymentTerm cpt) {
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        List<DirectDebit> newDirectDebits = new ArrayList<>();
        Set<String> s = new HashSet<>();
        l.forEach(d -> {
            logger.info("DD id: " + d.getId());

            List<ContractPayment> contractPayments = getUniquePaymentsInAllDDs(d.getContractPayments(), s);
            if (contractPayments.isEmpty()) return;

            contractPayments = contractPaymentService.getUnReceivedPayment(contractPayments, cpt.getContract());
            if (contractPayments.isEmpty()) return;
            logger.info("contractPayment size : " + contractPayments.size());

            LocalDate ped = new LocalDate(cpt.getContract().getPaidEndDate());
            if (d.getCategory().equals(DirectDebitCategory.A)) {
                LocalDate startDate = new LocalDate(d.getStartDate());

                if (PaymentHelper.isMonthlyPayment(d.getPaymentType()) && startDate.isBefore(ped)) {
                    logger.info("Monthly DDA start date before PED dd id: " + d.getId());
                    return;
                }

                if (!PaymentHelper.isMonthlyPayment(d.getPaymentType()) && startDate.isBefore(new LocalDate())) {
                    startDate = new LocalDate();
                }

                logger.info("start Date: " + startDate.toString("yyyy-MM-dd"));
                for (ContractPayment c : contractPayments) {
                    c.setDate(new java.sql.Date(startDate.toDate().getTime()));
                }
                d.setContractPayments(new ArrayList<>(contractPayments));


                Map<String, Object> map = new HashMap<>();
                map.put("ddStartDate", startDate);
                map.put("expiryDate", startDate.plusMonths(ontTimeDDMonthDuration));
                newDirectDebits.add(regenerateDd(d, map));
            } else {
                if (d.getExpiryDate().getTime() <= ped.toDate().getTime()) return;

                if (d.getStartDate().getTime() > ped.toDate().getTime()) {
                    d.setContractPayments(contractPayments);
                    newDirectDebits.add(regenerateDd(d, new HashMap<>()));
                    return;
                }

                Map<String, Object> map = new HashMap<>();
                map.put("directDebit", d);
                map.put("ontTimeDDMonthDuration", ontTimeDDMonthDuration);

                contractPayments = contractPayments.stream()
                        .filter(cp -> cp.getDate().after(cpt.getContract().getPaidEndDate()))
                        .collect(Collectors.toList());

                if (contractPayments.isEmpty()) return;

                ContractPayment dda = contractPayments.get(0);
                dda.setDate(ped.plusDays(1).toDate());
                contractPayments.remove(0);

                // Regenerate DDA
                map.put("ddStartDate", new LocalDate(dda.getDate()));
                map.put("contractPayments", Collections.singletonList(dda));
                logger.info("generate first cp as dda");
                newDirectDebits.add(regenerateDdaFromDdb(map));

                // Regenerate DDB
                if (!contractPayments.isEmpty()) {
                    logger.info("generate ddb");
                    map.put("ddStartDate", new LocalDate(dda.getDate()).plusMonths(1));
                    map.put("contractPayments", contractPayments);
                    newDirectDebits.add(regenerateMonthlyDdb(map));
                }
            }

            newDirectDebits.forEach(dd ->
                    logger.info("Update newDirectDebit id: " + dd.getId() +
                            "; contract Payment size: " + dd.getPayments().size()));

        });

        return newDirectDebits;
    }

    // ACC-8920
    // Check If Duplicated Contract Payments of Direct Debit
    private List<ContractPayment> getUniquePaymentsInAllDDs(List<ContractPayment> l, Set<String> s) {
        logger.info("contractPayment size : " + l.size());
        List<ContractPayment> contractPayments = new ArrayList<>();

        StringBuilder log = new StringBuilder();
        l.forEach(cp -> {
            String key = new LocalDate(cp.getDate()).toString("yyyy-MM") + "_" +
                    cp.getPaymentType().getCode() + "_" +
                    cp.getAmount();
            log.append("key: ")
                    .append(key);

            if (s.contains(key)) return;
            s.add(key);
            contractPayments.add(cp);
        });

        logger.info("Keys: " + log + "; contractPayments size: " + contractPayments.size());
        return contractPayments;
    }

    private DirectDebit regenerateDdaFromDdb(Map<String, Object> map) {
        DirectDebit d = (DirectDebit) map.get("directDebit");
        LocalDate ddStartDate = (LocalDate) map.get("ddStartDate");
        List<ContractPayment> l = (List<ContractPayment>) map.get("contractPayments");
        int ontTimeDDMonthDuration = (int) map.get("ontTimeDDMonthDuration");

        logger.info("dd id: " + d.getId() +  "; l size: " + l.size() +
                "; ddStartDate: " + ddStartDate.toString("yyyy-MM-dd"));

        l.get(0).setDate(new java.sql.Date(ddStartDate.toDate().getTime()));
        d.setContractPayments(l);
        map.put("expiryDate", ddStartDate.plusMonths(ontTimeDDMonthDuration));
        map.put("directDebitCategory", DirectDebitCategory.A);
        map.put("directDebitType", DirectDebitType.ONE_TIME);
        return regenerateDd(d, map);
    }

    private DirectDebit regenerateMonthlyDdb(Map<String, Object> map) {
        DirectDebit d = (DirectDebit) map.get("directDebit");
        List<ContractPayment> l = (List<ContractPayment>) map.get("contractPayments");

        logger.info("dd id: " + d.getId() + "; l size: " + l.size() +
                "; ddStartDate: " + ((LocalDate) map.get("ddStartDate")).toString("yyyy-MM-dd"));
        d.setContractPayments(l);
        return regenerateDd(d, map);
    }

    private DirectDebit regenerateDd(DirectDebit d, Map<String, Object> map) {
        // ACC-9487
        // Calculate appropriate amount for the new DD
        List<ContractPayment> cps = d.getContractPayments().stream()
                .filter(cp -> {
                    boolean isAfterStartDateOrEqual = !cp.getDate().before(d.getStartDate());
                    logger.info("cp date: " + new DateTime(cp.getDate()).toString("yyyy-MM-dd HH:mm:ss") +
                            "dd start date: " + new DateTime(d.getStartDate()).toString("yyyy-MM-dd HH:mm:ss") +
                            "isAfterStartDateOrEqual: "  + isAfterStartDateOrEqual);
                    return isAfterStartDateOrEqual;
                })
                .collect(Collectors.toList());

        Double newAmount = cps.get(0).isOneTime() ?
                cps.stream().mapToDouble(ContractPayment::getAmount).sum() :
                cps.get(0).getAmount();
        map.put("ddAmount", newAmount);

        DirectDebit newDD = d.clone(DirectDebitStatus.IN_COMPLETE, map);
        newDD.setDirectDebitRejectionToDo(null);
        newDD.setDirectDebitBouncingRejectionToDo(null);
        newDD.setImageForDD(null);
        newDD.setSource(DirectDebitSource.REACTIVATE_CONTRACT_API);
        directDebitRepository.save(newDD);
        d.cloneChildDds(newDD, 0, false);

        logger.info("old dd id: " + d.getId() + " new dd id: " + newDD.getId() + "; contract payments size: " + newDD.getPayments().size());
        return directDebitRepository.findOne(newDD.getId());
    }

    public static boolean shouldRunDDsCancellationScheduledJob(boolean greaterThanJobDay) {
        Calendar c = Calendar.getInstance();

        logger.info("sendEmailsJob 1: " + c.get(Calendar.DATE) +
                "; 2: " + c.getActualMaximum(Calendar.DAY_OF_MONTH) +
                "; 3: " + Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.DDS_CANCELLATION_SCHEDULED_JOB_START_DAY)));

        int jobDay = c.getActualMaximum(Calendar.DAY_OF_MONTH) - Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.DDS_CANCELLATION_SCHEDULED_JOB_START_DAY));
        return greaterThanJobDay ? c.get(Calendar.DATE) >= jobDay : c.get(Calendar.DATE) == jobDay;
    }

    public void generateDDsAfterUnFreezingContract(ContractPaymentTerm cpt, List<ContractPayment> contractPayments) throws Exception {
        logger.info("cpt id: " + cpt.getId());


        contractPayments = contractPayments.stream()
                .filter(cp -> new LocalDate(cp.getDate()).isAfter(new LocalDate().dayOfMonth().withMaximumValue()) &&
                        PaymentHelper.isMonthlyPayment(cp.getPaymentType()))
                .collect(Collectors.toList());

        double proRatedAmount = paymentService
                .getUnFrozenContractProRatedAmount(cpt, new LocalDate(), false);

        if (proRatedAmount > 0) {
            ContractPayment contractPayment = contractPaymentTermHelper.createProratedPayment(cpt, new DateTime(), proRatedAmount);
            contractPayment.setIsCalculated(true);
            contractPayment.setOneTime(true);
            contractPayment.setPaymentMethod(PaymentMethod.DIRECT_DEBIT);
            contractPayment.setDescriptionForSigningScreen(contractPaymentTermHelper.getPaymentDescriptionForSigningScreen(contractPayment));
            contractPayments.add(0, contractPayment);
        }

        if (contractPayments.isEmpty()) return;

        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) directDebitSignatureService.
                getLastSignatureType(cpt, true, false).get("currentSignatures");

        contractPaymentTermServiceNew.updateContractPaymentTermWithPayments(
                cpt, true, false, contractPayments,
                signatures == null ?
                        new ArrayList<>() :
                        directDebitSignatureService.getSignatureAttachmentsOnly(signatures),
                true, signatures == null, true,
                false, false, true, false, new HashMap<>());
    }

    public void addDdsToContractAcc8101(Sheet sheet, boolean excludeInvalidContracts) {

        logger.info( "Sheet Name = " + sheet.getSheetName());
        logger.info( "last row number = " + sheet.getLastRowNum());

        UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService = Setup.getApplicationContext().getBean(UnpaidOnlineCreditCardPaymentService.class);
        FlowProcessorService flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);

        int ddaMonthDuration = Integer.parseInt(Setup
                .getParameter(
                        Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));

        List<DirectDebitReportCSV> l = new ArrayList<>();
        List<PaymentsInfoCSV> l1 = new ArrayList<>();
        for (Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                logger.info("Row Num: " + row.getRowNum());

                Contract contract = contractRepository.findOne((long) row.getCell(1).getNumericCellValue());
                ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                logger.info("contract id: " + contract.getId() + "; cpt id: " + cpt.getId());

                // 1- Read variables from Row of Sheet
                DateTime startDate = new DateTime(DateUtil.resolveDateFromCsvFile(row.getCell(2).getStringCellValue()));
                Double amount = row.getCell(3) != null ? row.getCell(3).getNumericCellValue() : null;
                String type = row.getCell(4) != null ? row.getCell(4).getStringCellValue() : null;
                DirectDebitCategory category = DirectDebitCategory.valueOf(row.getCell(5).getStringCellValue());
                boolean createWithDDb = row.getCell(6) != null && row.getCell(6).getStringCellValue().equalsIgnoreCase("true");

                // ACC-8456
                boolean execludedCurrentContract = excludeInvalidContracts && excludeInvalidContractsAcc8101(
                        contract, type,
                        new LocalDate(startDate).dayOfMonth().withMinimumValue().toDate(),
                        new LocalDate(startDate).dayOfMonth().withMaximumValue().toDate(),
                        l1);

                if (execludedCurrentContract) continue;

                // 2- init variables
                PicklistItem paymentType = Setup.getItem("TypeOfPayment",
                        type != null && !type.isEmpty() ?
                                type :
                                "Pre_collected_Payment");


                // 3- If Paying Via CC => Start Online Reminder Flow
                if (flowProcessorService.isPayingViaCreditCard(contract)) {
                    if (DirectDebitCategory.B.equals(category)) {
                        logger.info("The contract is paying via cc and the Direct Debit Category is 'B' -> exiting");

                        l.add(DirectDebitReportCSV.builder()
                                .contractId(contract.getId().toString())
                                .startDate(new LocalDate(startDate).toString("yyyy-MM-dd"))
                                .payingViaCc(true)
                                .category(String.valueOf(DirectDebitCategory.B))
                                .paymentType(paymentType.getName())
                                .amount(amount)
                                .note("The contract is paying via cc and the Direct Debit Category is 'B'")
                                .status("Failed")
                                .build());
                        continue;
                    }

                    FlowProcessorEntity f = unpaidOnlineCreditCardPaymentService
                            .createConfirmationTodoFromContractPaymentsAndStartReminderFlow(
                                    Collections.singletonList(contractPaymentService
                                            .createContractPaymentForDda(cpt, startDate.toDate(), amount, paymentType)),
                                    cpt);

                    if (f == null) {
                        l.add(DirectDebitReportCSV.builder()
                                .contractId(contract.getId().toString())
                                .flowId("NULL")
                                .payingViaCc(true)
                                .paymentType(paymentType.getName())
                                .amount(amount)
                                .status("Failed")
                                .build());
                    } else {
                        ContractPaymentWrapper w = f.getContractPaymentConfirmationToDo().getContractPaymentList().get(0);
                        l.add(DirectDebitReportCSV.builder()
                                .contractId(contract.getId().toString())
                                .flowId(f.getId().toString())
                                .startDate(new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd"))
                                .endDate(new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd"))
                                .payingViaCc(true)
                                .paymentType(w.getPaymentType().getName())
                                .amount(w.getAmount())
                                .status("Success")
                                .build());
                    }
                    continue;
                }

                // 3- Add New DD (DDa or DDb)
                addNewDdToContractAcc8101(
                        cpt, category, createWithDDb,
                        startDate, amount, paymentType,
                        ddaMonthDuration, l);

            } catch (Exception e) {
                e.printStackTrace();

                try {
                    l.add(DirectDebitReportCSV.builder()
                            .contractId(String.valueOf((long) row.getCell(1).getNumericCellValue()))
                            .note(e.getMessage())
                            .status("Failed")
                            .build());
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
            }
        }

        // 4- Send Email
        addDdsToContractAcc8101SendEmail(l);
    }

    public void addNewDdToContractAcc8101(
            ContractPaymentTerm cpt, DirectDebitCategory category, boolean createWithDDb,
            DateTime startDate, Double amount, PicklistItem paymentType,
            int ddaMonthDuration, List<DirectDebitReportCSV> l) throws Exception {

        // 1- Init variables
        DateTime endDate;
        DirectDebitType ddType;
        if (DirectDebitCategory.A.equals(category)) {
            ddType = DirectDebitType.ONE_TIME;
            endDate = startDate.plusMonths(ddaMonthDuration);
        } else {
            ddType = DirectDebitType.MONTHLY;
            endDate = getEndDateOfDDB(cpt);
        }

        logger.info("Start Date: " + startDate.toString("yyyy-MM-dd HH:mm:ss") +
                "; End Date: " + endDate.toString("yyyy-MM-dd HH:mm:ss"));

        // 2- Add New DD (DDA or DDB)
        Map<String, Object> lastSignatureType = directDebitSignatureService
                .getLastSignatureType(cpt, false, false);
        boolean useOldSignatures = ((Boolean) lastSignatureType.get("useApprovedSignature") ||
                (Boolean) lastSignatureType.get("useNonRejectedSignature"));

        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);

        List<DirectDebit> dds;
        if (createWithDDb &&
                DirectDebitCategory.A.equals(category) &&
                PaymentHelper.isMonthlyPayment(paymentType)) {

            dds = contractService.generateDDAfterSalaryChanged(cpt, startDate.toLocalDate())
                    .stream()
                    .filter(cp -> cp.getDirectDebit() != null && cp.getDirectDebit().getId() != null)
                    .map(ContractPayment::getDirectDebit)
                    .distinct()
                    .collect(Collectors.toList());
        } else {

            dds = Collections.singletonList(
                    contractPaymentTermServiceNew.addNewDD(
                            cpt.getContract(), startDate.toDate(), endDate.toDate(),
                            null, null, null,
                            amount, null,
                            ddType, paymentType, useOldSignatures, null,
                            amount != null, true, true, cpt, true));
        }

        dds.forEach(dd -> {

            logger.info("DD id: " + dd.getId() + "; Category: " + dd.getCategory());
            PicklistItem ddPaymentType = dd.getPayments() != null && !dd.getPayments().isEmpty() ?
                    dd.getPayments().get(0).getPaymentType() : null;

            l.add(DirectDebitReportCSV.builder()
                    .contractId(cpt.getContract().getId().toString())
                    .ddId(dd.getId().toString())
                    .startDate(new LocalDate(dd.getStartDate()).toString("yyyy-MM-dd"))
                    .endDate(new LocalDate(dd.getExpiryDate()).toString("yyyy-MM-dd"))
                    .category(String.valueOf(dd.getCategory()))
                    .payingViaCc(false)
                    .paymentType(ddPaymentType != null ? ddPaymentType.getName() : "")
                    .amount(dd.getAmount())
                    .note(category.equals(DirectDebitCategory.A) &&
                            dd.getCategory().equals(DirectDebitCategory.B) ?
                            ("The DDb: " + dd.getId()  + " created after the DDa id: " + dds.get(0).getId()) : "")
                    .status("Success")
                    .build());
        });
    }

    // ACC-9285
    // used in DDClientProjection
    public PicklistItem getPaymentType(DirectDebit directDebit) {
        if (!directDebit.getPayments().isEmpty()) {
            return directDebit.getPayments().get(0).getPaymentType();
        }

        DirectDebitRejectionToDo toDo = directDebit.getDirectDebitRejectionToDo();
        if (toDo != null && toDo.getLastDirectDebit() != null) {
            List<ContractPayment> payments = toDo.getLastDirectDebit().getPayments();
            if (payments != null && !payments.isEmpty()) {
                return payments.get(0).getPaymentType();
            }
        }
        return null;
    }

    // ACC-9775
    public String getDDRejectionGroupId(DirectDebit directDebit) {
        if (directDebit.getStatus() == DirectDebitStatus.CONFIRMED) {
            return null;
        }
        DirectDebitRejectionToDo rejectionToDo = directDebit.getDirectDebitRejectionToDo();
        DirectDebitRejectionToDo bouncingRejectionToDo = directDebit.getDirectDebitBouncingRejectionToDo();

        if (rejectionToDo != null) {
            return "normal_rejection_" + rejectionToDo.getId();
        }
        if (bouncingRejectionToDo != null) {
            return "bouncing_rejection" + bouncingRejectionToDo.getId();
        }
        return null;
    }

    public DateTime getEndDateOfDDB(ContractPaymentTerm cpt) {

        DateTime contractStartDate = new DateTime(cpt.getContract().getStartOfContract());
        if (cpt.getProRatedDays() > 0) {
            contractStartDate.plusDays(cpt.getProRatedDays());
        }

        int paymentsDuration = cpt.getContract().getPaymentsDuration() +
                Setup.getRepository(ContractPaymentTermExtendRepository.class)
                        .getExtendDurationByContractTerm(cpt);
        logger.info("Contract Start Date " + contractStartDate.toString("yyyy-MM-dd HH:mm:ss") +
                "; Payments Duration " + paymentsDuration);

        return contractStartDate.plusMonths(cpt.isIsProRated() ? paymentsDuration : paymentsDuration - 1)
                .withDayOfMonth(1);
    }

    public void addDdsToContractAcc8101SendEmail(List<DirectDebitReportCSV> l) {
        try {
            logger.info("Direct Debit Report CSV size: " + l.size());
            if (l.isEmpty()) return;

            List<EmailRecipient> recipients = EmailHelper.getRecipients(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_SEND_REPORT_IN_MIGRATION_API_EMAILS));

            if (recipients.isEmpty()) return;

            String[] headers = {"Contract ID",  "Status", "DD Id", "Flow Id", "Paying Via CC", "Category", "Amount", "Start Date", "End Date", "Payment Type", "Note"};
            String[] names = {"contractId", "status", "ddId", "flowId", "payingViaCc", "category", "amount", "startDate", "endDate", "paymentType", "note"};

            File file = CsvHelper.generateCsv(l, DirectDebitReportCSVProjection.class, headers, names,
                    "Bulk add DDs ACC-8101_" + new LocalDate().toString("yyyy-MM-dd"), ".csv");
            TextEmail mail = new TextEmail("The following information from ACC-8101 Bulk add DDs " + new DateTime().toString("yyyy-MM-dd HH:mm:ss"), "");
            mail.addAttachement(file);
            Setup.getMailService().sendEmail(recipients, mail, EmailReceiverType.Office_Staff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean excludeInvalidContractsAcc8101(
            Contract contract, String type, Date startDate, Date endDate, List<PaymentsInfoCSV> paymentsInfoCSVList) {

        List<DirectDebit> l = directDebitRepository.findByContractAndStatusAndTypeAndDate(contract,
                type, DirectDebitService.notAllowedStatuses, startDate, endDate);

        if (!l.isEmpty()) {
            paymentsInfoCSVList.add(new PaymentsInfoCSV(String.valueOf(contract.getId()),
                    "Has Active DD",
                    new LocalDate(l.get(0).getStartDate()).toString("yyyy-MM-dd"),
                    String.valueOf(l.get(0).getAmount()),
                    l.get(0).getStatus().getLabel()));
            return true;
        }

        Payment payment = paymentRepository.findFirstByContractAndStatusNotAndTypeOfPayment_CodeAndDateOfPaymentGreaterThanEqualAndDateOfPaymentLessThanEqual(
                contract, PaymentStatus.DELETED, type, startDate, endDate);
        if (payment != null) {
            paymentsInfoCSVList.add(new PaymentsInfoCSV(String.valueOf(contract.getId()),
                    "Has Active Payment",
                    new LocalDate(payment.getDateOfPayment()).toString("yyyy-MM-dd"),
                    String.valueOf(payment.getAmountOfPayment()),
                    payment.getStatus().getLabel()));
            return true;
        }

        List<ContractPaymentConfirmationToDo> toDos = contractPaymentConfirmationToDoRepository
                .findByContractAndSourceAndPaymentTypeAndDate(contract, type, startDate, endDate);
        if (!toDos.isEmpty()) {
            ContractPaymentWrapper w = toDos.get(0).getContractPaymentList().get(0);
            paymentsInfoCSVList.add(new PaymentsInfoCSV(String.valueOf(contract.getId()),
                    "Has Active Flow: " + toDos.get(0).getSource(),
                    new LocalDate(w.getPaymentDate().getTime()).toString("yyyy-MM-dd"),
                    String.valueOf(w.getAmount()),
                    ""));
            return true;
        }

        return false;
    }

    public void addDdaToContractAcc8101SendEmail(List<PaymentsInfoCSV> l) {
        try {
            logger.info("PaymentsInfoCSV size: " + l.size());
            if (l.isEmpty()) return;

            List<EmailRecipient> recipients = EmailHelper.getRecipients(
                    Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_CONTRACT_EXCLUDED_FROM_ACC8101_BULK_ADD_DDS_EMAILS));

            if (recipients.isEmpty()) return;

            String[] headers = {"Contract ID", "Reason", "Date", "Status", "Amount"};
            String[] names = {"contractId", "reason", "date", "status", "amount"};

            File file = CsvHelper.generateCsv(l, PaymentsInfoProjection.class, headers, names,
                    "Contract excluded from ACC-8101 Bulk add DDs_" + new LocalDate().toString("yyyy-MM-dd"), ".csv");
            TextEmail mail = new TextEmail("The following contract excluded from ACC-8101 Bulk add DDs" + new LocalDate().toString("yyyy-MM-dd"), "");
            mail.addAttachement(file);
            Setup.getMailService().sendEmail(recipients, mail, EmailReceiverType.Office_Staff);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean checkActiveCPTAndIfPaymentMatchedWithPTC(DirectDebit dd) {

        return dd.getContractPaymentTerm().isActive() &&
                checkIfPaymentMatchedWithPTC(dd.getContractPaymentTerm(),
                        dd.getContractPayments() == null || dd.getContractPayments().isEmpty() ?
                                dd.getPayments() : dd.getContractPayments());
    }

    public boolean checkIfPaymentMatchedWithPTC(ContractPaymentTerm cpt, List<ContractPayment> contractPayments) {

        Map<String, Object> map = new HashMap<String, Object>() {
            @Override
            public Object getOrDefault(Object key, Object defaultValue) {
                return this.get(key) != null ? super.getOrDefault(key, defaultValue) : defaultValue;
            }
        };
        map.put("paymentTypeConfigs", cpt.getContractPaymentTypes());
        map.put("contractPayments", contractPayments);
        map.put("abstractPaymentTerm", cpt.getPaymentTermConfig());

        map.put("creditNoteMonths", cpt.getCreditNoteMonths());
        map.put("creditNoteAmount", cpt.getCreditNote());
        map.put("additionalDiscountMonths", cpt.getAdditionalDiscountMonths());
        map.put("additionalDiscount", cpt.getAdditionalDiscount());
        map.put("waivedMonths", cpt.getContract().getWaivedMonths());
        map.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
        map.put("isProrated", cpt.getContract().getIsProRated());
        map.put("isProratedPlusMonth", cpt.getContract().getProRatedPlusMonth());
        map.put("contractStartDate", new DateTime(cpt.getContract().getStartOfContract()).toString(("yyyy-MM-dd HH:mm:ss")));
        map.put("contractId", cpt.getContract().getId());
        map.put("dailyRateAmount", cpt.getPaymentTermConfig().getDailyRateAmount());
        logger.info("map: " + map);

        return (Boolean) Setup.getApplicationContext()
                .getBean(ContractPaymentService.class)
                .checkIfPaymentMatchesWithPTC(map).get("matched");
    }

    public void cancelDDList(Sheet sheet) {

        logger.info( "Sheet Name = " + sheet.getSheetName());
        logger.info( "last row number = " + sheet.getLastRowNum());

        for (Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                logger.info("Row Num: " + row.getRowNum());

                DirectDebit d = directDebitRepository.findOne((long) row.getCell(0).getNumericCellValue());
                if (d == null) continue;
                logger.info("dd id: " + d.getId());
                directDebitCancellationService.cancelWholeDD(d, DirectDebitCancellationToDoReason.MANUALLY_FROM_ERP);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void createHandleDdPaymentsAfterGetConfirmed(DirectDebit entity, DirectDebit oldDd) {
        DirectDebitFile manualDirectDebitFile = null, autoDirectDebitFile = null;

        if (entity.getManualDdfFile() != null && entity.getManualDdfFile().getId() != null)
            manualDirectDebitFile = Setup.getRepository(DirectDebitFileRepository.class).findOne(entity.getManualDdfFile().getId());
        if (entity.getAutoDdfFile() != null && entity.getAutoDdfFile().getId() != null)
            autoDirectDebitFile = Setup.getRepository(DirectDebitFileRepository.class).findOne(entity.getAutoDdfFile().getId());

        if (entity.getCategory().equals(DirectDebitCategory.A)) {
            if (entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                logger.log(Level.INFO,"DD is Cat A and mStatus = Confirmed");
                oldDd.transferPaymentsToPdpOrCreate(manualDirectDebitFile);
            }
        } else if (entity.getCategory().equals(DirectDebitCategory.B)) {

            if (entity.getStatus().equals(DirectDebitStatus.CONFIRMED)) {
                oldDd.transferPaymentsToPdpOrCreate(autoDirectDebitFile);
                logger.log(Level.INFO,"DD is Cat B and ddStatus = Confirmed");
            } else if (!entity.getStatus().equals(DirectDebitStatus.CONFIRMED)
                    && entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                oldDd.transferPaymentsToPdpOrCreate(manualDirectDebitFile);
                logger.log(Level.INFO,"DD is Cat B and mStatus = Confirmed and ddStatus != Confirmed");
            }

            if (entity.getStatus().equals(DirectDebitStatus.CONFIRMED)
                    && entity.getMStatus().equals(DirectDebitStatus.CONFIRMED)) {
                logger.log(Level.INFO,"ddStatus = Confirmed and the mStatus = Confirmed.");
                if (autoDirectDebitFile != null && manualDirectDebitFile != null) {
                    PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
                    List<Payment> pdpPaymentsLinkedToManualDirectDebitFile =
                            paymentRepository.findByDirectDebitIdAndDirectDebitFileIdAndStatus(
                                    oldDd.getId(), manualDirectDebitFile.getId(), PaymentStatus.PDC);
                    PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
                    if (!pdpPaymentsLinkedToManualDirectDebitFile.isEmpty()) {
                        for (Payment payment : pdpPaymentsLinkedToManualDirectDebitFile) {
                            if(payment.getDateOfPayment().before(autoDirectDebitFile.getStartDate()))
                                continue;
                            payment.setDirectDebitFileId(autoDirectDebitFile.getId());
                            paymentService.updatePaymentSilent(payment);
                        }
                    }
                    else {
                        logger.log(Level.INFO,"couldn't find any PDC payment linked to the manual file");
                    }
                }
                else {
                    logger.log(Level.INFO,"autoDirectDebitFile or manualDirectDebitFile not linked to DD ");
                }
            }
        }
    }

    public void startDdExpiryFlow(Sheet sheet) {

        logger.info( "Sheet Name = " + sheet.getSheetName());
        logger.info( "last row number = " + sheet.getLastRowNum());

        PaymentExpiryService paymentExpiryService = Setup.getApplicationContext()
                .getBean(PaymentExpiryService.class);
        FlowEventConfig flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW);
        FlowSubEventConfig flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.GENERATE_DDS_AFTER_PAID_END_DATE,
                        flowEventConfig);
        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");

        for (Row row : sheet) {
            try {
                if (row.getRowNum() == 0) continue;
                logger.info("Row Num: " + row.getRowNum());

                Contract c = contractRepository.findOne((long) row.getCell(0).getNumericCellValue());
                if (c == null) continue;
                logger.info("contract id: " + c.getId());
                paymentExpiryService.createPaymentExpiryFlow(c, monthlyPayment, flowEventConfig, flowSubEventConfig);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}