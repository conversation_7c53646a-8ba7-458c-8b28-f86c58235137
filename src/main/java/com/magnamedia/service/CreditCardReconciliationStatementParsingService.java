package com.magnamedia.service;

import com.magnamedia.controller.ExpensePaymentController;
import com.magnamedia.controller.ExpenseRequestTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.*;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails.CRDRAction;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails.MatchType;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2/1/2021
 */
@Service
public class CreditCardReconciliationStatementParsingService {

    private final DataFormatter dataFormatter = new DataFormatter();

    @Autowired
    CreditCardReconciliationStatementRepository statementRepository;

    @Autowired
    CreditCardReconciliationStatementDetailsRepository detailsRepository;

    @Autowired
    CurrencyExchangeSevice currencyExchangeSevice;

    @Autowired
    ReconciliationTransactionRepository reconciliationTransactionRepository;

    @Autowired
    private ExpensePaymentService expensePaymentService;

    private static final Logger logger =
            Logger.getLogger(CreditCardReconciliationStatementParsingService.class.getName());

    public CreditCardReconciliationStatement parseStatement(Bucket creditCard, Attachment attachment, CreditCardReconciliationStatement statement) {
        logger.info("Start Parsing CreditCardReconciliationStatement#" + statement.getId());

        Double authorizedAmount = 0.0;
        List<Record> records = null;
        //reading excel

        Workbook workbook = null;
        try {
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            Sheet sheet = workbook.getSheetAt(0);
            //reach first data row
            for (Row row : sheet) {
                if (isRowEmpty(row))
                    continue;

                logger.info("Row" + row.getRowNum());
                String stringCellValue = dataFormatter.formatCellValue(row.getCell(0));
                logger.info("90 -> stringCellValue: " + stringCellValue);
                if (stringCellValue.toLowerCase().startsWith("authorised")) {
                    //authorised
                    logger.info("Row Starts with Authorised");


                    authorizedAmount = parseAuthorizedList(sheet, row.getRowNum() + 3);
                } else if (stringCellValue.toLowerCase().contains("posted")) {
                    //posted
                    logger.info("Posted");
                    records = parsePostedList(sheet, row.getRowNum() + 3);

                    break;
                } else {
                    logger.info("Row Doesn't start Neither with Posted nor Authorized");
                }
            }
        } catch (IOException | ParseException e) {
            e.printStackTrace();
        }

        List<CreditCardReconciliationStatementDetails> details = processRecords(statement, records, creditCard);
        //create statement
        statement.setAuthorizedTransactions(authorizedAmount);
        statement.setDetails(details);
        statement.setStatus(CreditCardReconciliationStatement.Status.PENDING);
        statement.setParsingDate(new Date());
        statement.addAttachment(attachment);
        statement = statementRepository.save(statement);
        return statement;
    }

    private Double parseAuthorizedList(Sheet sheet, int index) throws ParseException {
        logger.info("Parse Authorized List");

        for (int i = index; i <= sheet.getLastRowNum(); i++) {
            if (!isRowEmpty(sheet.getRow(i))) break;
        }

        Double authorizedAmount = 0.0;

        for (int i = index; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);

            if (row == null) {
                logger.info("Row" + i + " is NULL");
                return authorizedAmount;
            }

            logger.info("Row" + row.getRowNum());
            if (isRowEmpty(row))
                break;

            dataFormatter.formatCellValue(row.getCell(0));
            String stringCellValue = dataFormatter.formatCellValue(row.getCell(3));

            if (!isDouble(stringCellValue)) {
                logger.info("not Double -> break");
                break;
            }

            Double amount = row.getCell(3).getNumericCellValue();
            logger.info("Cell[3] Amount: " + amount);
            authorizedAmount += amount;
        }

        return authorizedAmount;
    }

    private List<Record> parsePostedList(Sheet sheet, int index) throws ParseException {
        logger.info("Parse Posted List");

        List<Record> records = new ArrayList();

        for (int i = index; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (isRowEmpty(row))
                break;

            logger.info("Row" + row.getRowNum());

//            String stringCellValue = dataFormatter.formatCellValue(row.getCell(0).getDateCellValue());

            Date date = row.getCell(0).getDateCellValue();
            String description = row.getCell(1).getStringCellValue();
            String action = row.getCell(2).getStringCellValue();
            Double amount = row.getCell(3).getNumericCellValue();

            Record r = new Record(date, description, action, amount);
            logger.info("new Record: " + System.lineSeparator() + r.toString());
            records.add(r);
        }

        return records;
    }

    private List<CreditCardReconciliationStatementDetails> processRecords(CreditCardReconciliationStatement statement, List<Record> records, Bucket creditCard) {
        //first check prev done
        logger.info("process Records");
        logger.info("Records Size: " + (records != null ? records.size() : "NULL"));

        List<CreditCardReconciliationStatementDetails> details = new ArrayList();

        if (records == null) return details;

        Map<Record, Integer> map = new HashMap();
        for (Record r : records) {
            Integer val = map.get(r);
            map.put(r, val == null ? 1 : val + 1);
        }

        map.entrySet().forEach(entry -> {
            //key is record, value is frequency
            Record record = entry.getKey();
            List<CreditCardReconciliationStatementDetails> dbEntries = detailsRepository.findByRecordTransactionDateAndRecordDescriptionAndRecordAmountAndCrdrAction(record.date, record.description, record.amount, record.action);
            //add new records
            int numOfNew = entry.getValue() - dbEntries.size();
            for (int i = 0; i < entry.getValue(); ++i) {
                CreditCardReconciliationStatementDetails detail = new CreditCardReconciliationStatementDetails();
                detail.setCreditCardReconciliationStatement(statement);
                detail.setRecordAmount(record.amount);
                detail.setRecordDescription(record.description);
                detail.setRecordTransactionDate(record.date);
                detail.setCrdrAction(record.action);
                detail.setMatchType(MatchType.UNMATCHED);

                detail = detailsRepository.save(detail);
                if (i < numOfNew) {
                    details.add(detail);
                }
            }
        });

        matchRecords(details, creditCard);
        return details;
    }

    private void matchRecords(List<CreditCardReconciliationStatementDetails> details, Bucket creditCard) {
        //get all records in Expenses Requests that are NOT CONFIRMED and their payment bucket is from this card.
        SelectQuery<ExpenseRequestTodo> expenseRequestTodoSelectQuery = new SelectQuery(ExpenseRequestTodo.class);
        expenseRequestTodoSelectQuery.leftJoin("expensePayment");
        expenseRequestTodoSelectQuery.filterBy(new SelectFilter("confirmed", "IS NULL", null)
                .or("confirmed", "=", Boolean.FALSE));
        expenseRequestTodoSelectQuery.filterBy("expensePayment.fromBucket", "=", creditCard);
        List<ExpenseRequestTodo> expenseRequestTodosToMatchExpense = expenseRequestTodoSelectQuery.execute();

        //get all Expenses that are tagged as auto-deduct with payment method = Credit Card.
        SelectQuery<Expense> expenseSelectQuery = new SelectQuery(Expense.class);
        expenseSelectQuery.filterBy("autoDeducted", "=", Boolean.TRUE);
        List<Expense> expenseAutoDetuctToMatch = expenseSelectQuery.execute();
        expenseAutoDetuctToMatch = expenseAutoDetuctToMatch.stream().filter(e -> e.getPaymentMethods().contains(ExpensePaymentMethod.CREDIT_CARD)).collect(Collectors.toList());

        // find CONFIRMED and Refund_Confirmed = 0 and tagged as REFUND and their payment bucket is from this card
        SelectQuery<ExpenseRequestTodo> refundExpenseRequestTodoSelectQuery = new SelectQuery(ExpenseRequestTodo.class);
        //refundExpenseRequestTodoSelectQuery.filterBy("confirmed", "=", Boolean.TRUE);
        refundExpenseRequestTodoSelectQuery.filterBy("isRefunded", "=", Boolean.TRUE);
        refundExpenseRequestTodoSelectQuery.filterBy(new SelectFilter("refundConfirmed", "IS NULL", null)
                .or("refundConfirmed", "=", Boolean.FALSE));
        refundExpenseRequestTodoSelectQuery.filterBy("expensePayment.fromBucket", "=", creditCard);
        List<ExpenseRequestTodo> expenseRequestTodosToMatchRefund = refundExpenseRequestTodoSelectQuery.execute();

        // match it with the approved replenishments for this credit card for this amount and are NOT CONFIRMED.
        SelectQuery<BucketReplenishmentTodo> replenishmentTodoSelectQuery = new SelectQuery(BucketReplenishmentTodo.class);
        replenishmentTodoSelectQuery.filterBy(new SelectFilter("confirmed", "IS NULL", null)
                .or("confirmed", "=", Boolean.FALSE));
        replenishmentTodoSelectQuery.filterBy("auditManagerApproved", "=", Boolean.TRUE);
        replenishmentTodoSelectQuery.filterBy("bucket", "=", creditCard);
        List<BucketReplenishmentTodo> replenishmentTodosToMatch = replenishmentTodoSelectQuery.execute();


        for (CreditCardReconciliationStatementDetails detail : details) {
            // match with expenseRequest
            if (matchWithExpenseRequest(detail, expenseRequestTodosToMatchExpense, creditCard)) {
                logger.info("matchWithExpenseRequest, MATCHED");
                expenseRequestTodosToMatchExpense.remove(processExpenseRequest(detail, creditCard));
            }
            // match with expense auto deduct
            else if (matchWithAutoDeductExpense(detail, expenseAutoDetuctToMatch)) {
                logger.info("matchWithAutoDeductExpense, MATCHED");
                processAutoDeducted(detail, creditCard);
            }
            // match with Refund
            else if (matchWithRefund(detail, expenseRequestTodosToMatchRefund)) {
                logger.info("matchWithRefund, MATCHED");
                expenseRequestTodosToMatchRefund.remove(processRefund(detail, creditCard));
            }
            // match with Replenishment
            else if (matchWithReplenishment(detail, replenishmentTodosToMatch)) {
                logger.info("matchWithReplenishment, MATCHED");
                replenishmentTodosToMatch.remove(processReplenishment(detail));
            } else {
                detail.setMatchType(MatchType.UNMATCHED);
            }

            detailsRepository.save(detail);
        }
    }

    private boolean matchWithAutoDeductExpense(CreditCardReconciliationStatementDetails detail, List<Expense> expenses) {
        logger.info("matchWithAutoDeductExpense, expenses size: " + expenses.size());
        logger.info("matchWithAutoDeductExpense, detail: " + System.lineSeparator() + detail);

        if (detail.getCrdrAction() == CRDRAction.DR) {
            for (Expense expense : expenses) {
                logger.info("expense id " + expense.getId());

                if (isNameExistInDescription(expense, detail.getRecordDescription())) {
                    detail.setMatchType(MatchType.AUTO_DEDUCTED);
                    detail.setMatchedAutoDeduct(expense);
                    return true;
                }
            }
        }
        return false;
    }

    private boolean matchWithReplenishment(CreditCardReconciliationStatementDetails detail, List<BucketReplenishmentTodo> replenishmentTodosToMatch) {
        logger.info("matchWithReplenishment, replenishmentTodosToMatch size: " + replenishmentTodosToMatch.size());
        logger.info("matchWithReplenishment, detail: " + System.lineSeparator() + detail);

        if (detail.getCrdrAction() == CRDRAction.CR &&
                detail.getRecordDescription() != null &&
                detail.getRecordDescription().contains("PAYMENT RECEIVED")) {
            for (BucketReplenishmentTodo replenishmentTodo : replenishmentTodosToMatch) {
                logger.info("matchWithReplenishment, replenishmentTodo ID: " + replenishmentTodo.getId());
                logger.info("matchWithReplenishment, replenishmentTodo Amount: " + replenishmentTodo.getAmount());

                if (replenishmentTodo.getAmount().equals(detail.getRecordAmount())) {
                    ExpensePayment expensePayment = replenishmentTodo.getExpensePayments() != null && replenishmentTodo.getExpensePayments().size() > 0 ?
                            replenishmentTodo.getExpensePayments().get(0) : null;
                    if (expensePayment == null) {
                        logger.severe("matchWithReplenishment, replenishmentTodo has no Expense Payment");
                        return false;
                    }

                    detail.setMatchType(MatchType.REPLENISHMENT);
                    detail.setReplenishmentTodo(replenishmentTodo);
                    detail.setPaymentDate(expensePayment.getCreationDate());
                    detail.setRequestAmount(replenishmentTodo.getAmount());
                    detail.setRequestAmountInLocalCurrency(replenishmentTodo.getAmount());
                    detail.setRequestCurrency(currencyExchangeSevice.getLocalCurrency());
                    return true;
                }
            }
        }
        return false;
    }

    private boolean matchWithRefund(CreditCardReconciliationStatementDetails detail, List<ExpenseRequestTodo> expenseRequestTodosToMatchRefund) {
        logger.info("matchWithRefund, expenseRequestTodosToMatchRefund size: " + expenseRequestTodosToMatchRefund.size());
        logger.info("matchWithRefund, detail: " + System.lineSeparator() + detail);

        if (detail.getCrdrAction() == CRDRAction.CR) {
            for (ExpenseRequestTodo expenseRequestTodo : expenseRequestTodosToMatchRefund) {
                logger.info("matchWithRefund, expenseRequestTodo ID: " + expenseRequestTodo.getId());
                logger.info("matchWithRefund, expenseRequestTodo Beneficiary Type: " + expenseRequestTodo.getBeneficiaryType());

                //check supplier financial name
                if (expenseRequestTodo.getBeneficiaryType() != ExpenseBeneficiaryType.SUPPLIER)
                    continue;
                Supplier supplier = Setup.getRepository(SupplierRepository.class).findOne(expenseRequestTodo.getBeneficiaryId());
                if (supplier != null && checkIfContainsNameInFinancialStatement(detail.getRecordDescription(), supplier.getNameInFinancialStatement())) {
                    logger.info("matchWithRefund, Name Exists");
                    logger.info("matchWithRefund, expenseRequestTodo Refund Amount: " + expenseRequestTodo.getRefundAmount());

                    //check refund amount
                    boolean isLocalCurrency = expenseRequestTodo.getCurrency().getId().equals(currencyExchangeSevice.getLocalCurrency().getId());
                    if (isLocalCurrency) {
                        if (Objects.equals(expenseRequestTodo.getRefundAmount(), detail.getRecordAmount())) {
                            logger.info("Matched with Local Currency");
                            matchRecord(detail, MatchType.REFUND, "Refund", expenseRequestTodo);
                            return true;
                        }
                    } else {
                        if (expenseRequestTodo.getRefundAmount() != null &&
                                (detail.getRecordDescription().contains(String.format("%.0f", expenseRequestTodo.getRefundAmount())) ||
                                        detail.getRecordDescription().contains(expenseRequestTodo.getRefundAmount().toString()))) {
                            logger.info("Matched with Foreign Currency");
                            matchRecord(detail, MatchType.REFUND, "Refund", expenseRequestTodo);
                            return true;
                        }
                    }
                } else {
                    logger.info("matchWithRefund, Name Doesn't Exist");
                }
            }
        }
        return false;
    }

    private boolean matchWithExpenseRequest(
            CreditCardReconciliationStatementDetails detail,
            List<ExpenseRequestTodo> expenseRequestTodos,
            Bucket creditCard) {

        logger.info("size: " + expenseRequestTodos.size());
        logger.info("detail: " + System.lineSeparator() + detail);

        if (detail.getCrdrAction() == CRDRAction.DR) {
            for (ExpenseRequestTodo expenseRequestTodo : expenseRequestTodos) {
                logger.info("ID: " + expenseRequestTodo.getId());
                logger.info("Beneficiary Type: " + expenseRequestTodo.getBeneficiaryType());

                if (expenseRequestTodo.getBeneficiaryType() != ExpenseBeneficiaryType.SUPPLIER) continue;

                Supplier supplier = Setup.getRepository(SupplierRepository.class).findOne(expenseRequestTodo.getBeneficiaryId());
                if (supplier == null) {
                    logger.info("Supplier IS NULL");
                    continue;
                }

                // ACC-4505
                if(supplier.getIsTicketNumberRequired()) {
                      if(expenseRequestTodo.getTicket() != null &&
                              expenseRequestTodo.getTicket().getTicketNumber() != null &&
                              detail.getRecordDescription() != null &&
                              (creditCard.getSupplierNameIgnored() || checkIfContainsNameInFinancialStatement(
                                      detail.getRecordDescription(), supplier.getNameInFinancialStatement())) &&
                              detail.getRecordDescription().contains(
                                      expenseRequestTodo.getTicket().getTicketNumber())) {

                          matchRecord(detail, MatchType.EXISTING_EXPENSE, expenseRequestTodo.getStatus().getLabel(), expenseRequestTodo);
                          return true;
                      } else {
                          continue;
                      }
                }

                if(creditCard.getSupplierNameIgnored()) {
                    int difference = Math.abs(Hours.hoursBetween(
                            new DateTime(expenseRequestTodo.getCreationDate()), new DateTime(detail.getRecordTransactionDate())).getHours());

                    int parameterValue = Integer.parseInt( Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_HOURS_TO_EXPIRED_MATCHING_CREDIT_CARD_WHEN_PARSING_STATEMENT));
                    if(difference > parameterValue) continue;
                } else {
                    boolean nameExist = detail.getRecordDescription() != null && checkIfContainsNameInFinancialStatement(detail.getRecordDescription(), supplier.getNameInFinancialStatement());
                    if(!nameExist)continue;
                }

                logger.info("Amount: " + expenseRequestTodo.getAmount());

                boolean isLocalCurrency = expenseRequestTodo.getCurrency().getId().equals(currencyExchangeSevice.getLocalCurrency().getId());
                if (isLocalCurrency) {
                    if (Objects.equals(expenseRequestTodo.getAmount(), detail.getRecordAmount())) {
                        logger.info("Matched with Local Currency");
                        matchRecord(detail, MatchType.EXISTING_EXPENSE, expenseRequestTodo.getStatus().getLabel(), expenseRequestTodo);
                        return true;
                    }
                } else {
                    if (expenseRequestTodo.getAmount() != null &&
                            (detail.getRecordDescription().contains(String.format("%.0f", expenseRequestTodo.getAmount())) ||
                                    detail.getRecordDescription().contains(expenseRequestTodo.getAmount().toString()))) {
                        logger.info("Matched with Foreign Currency");
                        matchRecord(detail, MatchType.EXISTING_EXPENSE, expenseRequestTodo.getStatus().getLabel(), expenseRequestTodo);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private ExpenseRequestTodo processExpenseRequest(
            CreditCardReconciliationStatementDetails detail,
            Bucket creditCard) {

        ExpenseRequestTodo matchedExpenseRequest = detail.getMatchedExpenseRequest();

        ReconciliationTransaction transaction = new ReconciliationTransaction();
        transaction.setFromBucket(creditCard);
        transaction.setExpense(matchedExpenseRequest.getExpenseToPost() != null ?
                matchedExpenseRequest.getExpenseToPost() :
                matchedExpenseRequest.getExpense());

        if (matchedExpenseRequest.getExpensePayment().getVatAmount() != null &&
                !matchedExpenseRequest.getExpensePayment().getVatAmount().equals(0D)) {

            transaction.setVatType(VatType.IN);
            transaction.setVatAmount(matchedExpenseRequest.getExpensePayment().getVatAmount());
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else {
            transaction.setVatType(null);
            transaction.setVatAmount(null);
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
        }

        for (Attachment attachment : expensePaymentService.getAttachments(matchedExpenseRequest.getExpensePayment())) {
            transaction.addAttachment(attachment);
        }

        transaction.setAmount(detail.getRecordAmount());
        transaction.setTransactionDate(new Date());
        transaction.setPnlValueDate(new Date());
        transaction.setDescription(matchedExpenseRequest.getDescription());
        transaction.setPaymentType(PaymentMethod.valueOf(matchedExpenseRequest.getPaymentMethod().toString()));
        transaction.setMissingTaxInvoice(isMissingTaInvoice(transaction));
        transaction = reconciliationTransactionRepository.save(transaction);

        detail.setReconciliationTransaction(transaction);

        return matchedExpenseRequest;
    }

    private void processAutoDeducted(
            CreditCardReconciliationStatementDetails detail,
            Bucket creditCard) {

        Expense matchedAutoDeduct = detail.getMatchedAutoDeduct();

        // CREATING EXPENSE REQUEST
        ExpenseRequestTodo expenseRequestTodo = new ExpenseRequestTodo();
        expenseRequestTodo.setExpense(matchedAutoDeduct);
        expenseRequestTodo.setBeneficiaryType(ExpenseBeneficiaryType.SUPPLIER);
        List<Supplier> suppliers = matchedAutoDeduct.getSuppliers();

        if (suppliers == null || suppliers.size() != 1)
            throw new RuntimeException("Expense with auto deduct flag should have only one supplier");

        expenseRequestTodo.setBeneficiaryId(suppliers.get(0).getId());
        expenseRequestTodo.setStatus(ExpenseRequestStatus.PAID);
        expenseRequestTodo.setExpenseRequestType(ExpenseRequestType.AUTO_DEDUCT);
        expenseRequestTodo.setAmount(detail.getRecordAmount());
        expenseRequestTodo.setAmountToPay(detail.getRecordAmount());
        expenseRequestTodo.setAmountInLocalCurrency(detail.getRecordAmount());
        expenseRequestTodo.setCompleted(Boolean.TRUE);
        expenseRequestTodo.setStopped(Boolean.TRUE);
        expenseRequestTodo.setCurrency(currencyExchangeSevice.getLocalCurrency());
        expenseRequestTodo.setPaymentMethod(ExpensePaymentMethod.CREDIT_CARD);
        expenseRequestTodo = (ExpenseRequestTodo) Setup.getApplicationContext().getBean(ExpenseRequestTodoController.class)
                .createEntity(expenseRequestTodo).getBody();

        // CREATING PAYMENT
        ExpensePayment payment = Setup.getApplicationContext().getBean(ExpensePaymentService.class)
                .fillPaymentAttributesFromRequest(expenseRequestTodo);
        payment.setPaymentDate(new Date());
        payment.setConfirmed(true);
        payment.setStatus(suppliers.get(0).getVatRegistered() ?
                ExpensePaymentStatus.PAID_PENDING_INVOICE : ExpensePaymentStatus.PAID);
        payment.setMethod(ExpensePaymentMethod.CREDIT_CARD);
        payment.setExpenseToPost(matchedAutoDeduct);
        payment.setFromBucket(creditCard);
        payment.setCompleted(Boolean.TRUE);
        payment.setStopped(Boolean.TRUE);
        payment = (ExpensePayment) Setup.getApplicationContext().getBean(ExpensePaymentController.class)
                .createEntity(payment).getBody();

        expenseRequestTodo.setExpensePayment(payment);
        Setup.getRepository(ExpenseRequestTodoRepository.class).save(expenseRequestTodo);

        ReconciliationTransaction transaction = new ReconciliationTransaction();
        transaction.setFromBucket(payment.getFromBucket());
        transaction.setExpense(payment.getExpenseToPost());
        transaction.setAmount(detail.getRecordAmount());
        transaction.setVatAmount(payment.getVatAmount());
        transaction.setVatType(transaction.getVatAmount() != null && !transaction.getVatAmount().equals(0D) ? VatType.IN : null);
        transaction.setTransactionDate(new Date());
        transaction.setPnlValueDate(new Date());
        transaction.setPaymentType(PaymentMethod.CREDIT_CARD);
        transaction.setDescription(expenseRequestTodo.getDescription());
        transaction.setMissingTaxInvoice(isMissingTaInvoice(transaction));
        transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                transaction.getVatAmount() != null && !transaction.getVatAmount().equals(0D) ?
                        AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM :
                        AccountingModule.PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM));
        transaction = reconciliationTransactionRepository.save(transaction);

        detail.setReconciliationTransaction(transaction);
        detail.setMatchedExpenseRequest(expenseRequestTodo);
    }

    private ExpenseRequestTodo processRefund(
            CreditCardReconciliationStatementDetails detail,
            Bucket creditCard) {

        ExpenseRequestTodo matchedRefund = detail.getMatchedExpenseRequest();

        ReconciliationTransaction transaction = new ReconciliationTransaction();
        transaction.setFromBucket(creditCard);
        transaction.setExpense(matchedRefund.getExpenseToPost() != null ?
                matchedRefund.getExpenseToPost() : matchedRefund.getExpense());
        transaction.setAmount(detail.getRecordAmount() * (-1));//in negative
        transaction.setTransactionDate(new Date());
        transaction.setPnlValueDate(new Date());
        transaction.setDescription(matchedRefund.getDescription());

        if (matchedRefund.getExpensePayment().getVatAmount() != null &&
                !matchedRefund.getExpensePayment().getVatAmount().equals(0D)) {

            transaction.setVatType(VatType.IN);
            transaction.setVatAmount(matchedRefund.getExpensePayment().getVatAmount() * (-1));//in negative
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else {
            transaction.setVatType(null);
            transaction.setVatAmount(null);//in negative
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
        }

        for (Attachment attachment : expensePaymentService.getAttachments(matchedRefund.getExpensePayment())) {
            transaction.addAttachment(attachment);
        }

        transaction.setPaymentType(PaymentMethod.valueOf(detail.getMatchedExpenseRequest().getPaymentMethod().toString()));
        transaction.setMissingTaxInvoice(isMissingTaInvoice(transaction));
        transaction = reconciliationTransactionRepository.save(transaction);

        detail.setReconciliationTransaction(transaction);
        return matchedRefund;
    }

    private BucketReplenishmentTodo processReplenishment(
            CreditCardReconciliationStatementDetails detail) {

        BucketReplenishmentTodo matchedReplenishment = detail.getReplenishmentTodo();

        ReconciliationTransaction transaction = new ReconciliationTransaction();
        transaction.setFromBucket(matchedReplenishment.getFillFrom());
        transaction.setExpense(null);
        transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
        transaction.setAmount(detail.getRecordAmount());
        transaction.setVatType(null);
        transaction.setVatAmount(0D);
        transaction.setTransactionDate(new Date());
        transaction.setPnlValueDate(new Date());
        transaction.setMissingTaxInvoice(isMissingTaInvoice(transaction));

        for (ExpensePayment expensePayment : matchedReplenishment.getExpensePayments()) {
            if (expensePayment.getMethod().equals(ExpensePaymentMethod.CASH)) continue;

            transaction.setPaymentType(PaymentMethod.valueOf(expensePayment.getMethod().toString()));
            transaction.setToBucket(expensePayment.getToBucket());
            transaction.setDescription(expensePayment.getDescription());
        }

        transaction = reconciliationTransactionRepository.save(transaction);

        detail.setReconciliationTransaction(transaction);
        return matchedReplenishment;
    }

    private boolean checkIfContainsNameInFinancialStatement(String value, String nameInFinancialStatement) {
        if (nameInFinancialStatement == null || nameInFinancialStatement.isEmpty()) return false;
        for (String name : nameInFinancialStatement.split("[\\r\\n]+")) {
            if (value.contains(name)) return true;
        }
        return false;
    }

    private void matchRecord(CreditCardReconciliationStatementDetails detail, MatchType matchType, String status, ExpenseRequestTodo expenseRequestTodo) {
        detail.setMatchType(matchType);
        detail.setStatus(status);
        detail.setMatchedExpenseRequest(expenseRequestTodo);
        detail.setRequestAmount(expenseRequestTodo.getAmount());
        detail.setRequestAmountInLocalCurrency(expenseRequestTodo.getAmountInLocalCurrency());
        detail.setRequestCurrency(expenseRequestTodo.getCurrency());

        ExpensePayment expensePayment = expenseRequestTodo.getExpensePayment() != null ? expenseRequestTodo.getExpensePayment() : null;
        detail.setPaymentDate(expensePayment != null ? expensePayment.getCreationDate() : null);
    }

    private Boolean isNameExistInDescription(Expense expense, String desc) {
        for (String name : expense.getNamesInFinancialStatements()) {
            if (desc.contains(name)) {
                logger.info("isNameExistInDescription, expense:" + expense.getId() + ", desc: " + desc);
                return true;
            }
        }
        return false;
    }

    private boolean isDouble(String s) {
        try {
            s = s.trim();
            double w = Double.parseDouble(s);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private class Record {
        Date date;
        String description;
        CRDRAction action;
        Double amount;

        Record(Date date, String description, String action, Double amount) {
            this.date = date;
            this.description = description;
            this.action = CRDRAction.valueOf(action);
            this.amount = amount;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Record record = (Record) o;
            return Objects.equals(date, record.date) &&
                    Objects.equals(description, record.description) &&
                    action == record.action &&
                    Objects.equals(amount, record.amount);
        }

        @Override
        public int hashCode() {
            return date.hashCode() + description.hashCode() + action.hashCode() + amount.hashCode();
        }

        @Override
        public String toString() {
            return "Date: " + date.toString() + ", description: " + description + ", action: " + action + ", amount: " + amount;
        }
    }


    private boolean isRowEmpty(Row row) {
        if (row == null) {
            logger.info("Null Row");
            return true;
        }
        if (row.getLastCellNum() <= 0) {
            logger.info("no Cells in Row");
            return true;
        }
        for (int cellNum = row.getFirstCellNum(); cellNum < row.getLastCellNum(); cellNum++) {
            Cell cell = row.getCell(cellNum);
            if (cell != null && cell.getCellType() != Cell.CELL_TYPE_BLANK && StringUtils.isNotBlank(cell.toString())) {
                logger.info("Cell found in Row");
                return false;
            }
        }

        logger.info("no Cells in Row");

        return true;
    }

    public boolean isMissingTaInvoice(ReconciliationTransaction transaction) {
        return isMissingTaInvoice(transaction.getAttachments(), transaction.getVatAmount());
    }

    public boolean isMissingTaInvoice(List<Attachment> attachments, Double vatAmount) {
        logger.info("isMissingTaInvoice Start");
        boolean hasVat = vatAmount != null && !vatAmount.equals(0D);

        logger.info("Has Vat: " + hasVat);
        if (!hasVat) return false;

        boolean hasVatAttachment = false;

        if (attachments != null) {
            for (Attachment attachment : attachments) {
                logger.info("Attachment Tag: " + attachment.getTag());
                hasVatAttachment = attachment.getTag().toLowerCase().startsWith("vat_") || expensePaymentService.isVatAttachment(attachment);

                if (hasVatAttachment) break;
            }
        }

        logger.info("Has Vat Attachment: " + hasVatAttachment);

        logger.info("isMissingTaInvoice End");
        return !hasVatAttachment;
    }

    public void initTransactionRelatedToData(Transaction transaction, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Long relatedToId) {
        expensePaymentService.initTransactionRelatedToData(transaction, relatedToType, relatedToId);
    }
}
