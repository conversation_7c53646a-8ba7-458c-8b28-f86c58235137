package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.VisaRequestExpenseController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.repository.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigInteger;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class VisaStatementService {
    protected static final Logger logger = Logger.getLogger(VisaStatementService.class.getName());

    @Autowired
    private VisaStatementRepository visaStatementRepository;
    @Autowired
    private NewVisaRequestExpenseRepository newVisaRequestExpenseRepository;
    @Autowired
    private VisaStatementTransactionRepository visaStatementTransactionRepository;
    @Autowired
    private NoqodiStatementRecordRepository noqodiStatementRecordRepository;
    @Autowired
    private AmwalStatementRecordRepository amwalStatementRecordRepository;
    @Autowired
    private VisaExpenseService expenseService;
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private ObjectMapper objectMapper;

    private static final int BATCH_SIZE = 50;

    @Transactional
    public void createAllVisaTransactions(Map<String, Object> payload) {
        VisaStatement statement = visaStatementRepository.findOne(Long.parseLong(payload.get("entityId").toString()));
        if (statement == null) return;

        Attachment attachment = null;
        if (statement.getAttachmentsCount() > 0){
            attachment = statement.getAttachments().get(0);
        }

        if (attachment == null) return;

        try (InputStream inputStream = Storage.getStream(attachment)) {
            Workbook workbook;
            try {
                workbook = new XSSFWorkbook(inputStream);
            } catch (Exception e) {
                workbook = new HSSFWorkbook(inputStream);
            }

            if (workbook == null) return;

            logger.info("start parsing records for statement id : " + statement.getId());
            if (VisaStatementType.Amwal.equals(statement.getType())) {
                extractAmwalRecordsAndMappingThem(statement, workbook.getSheetAt(0));
            } else if (VisaStatementType.Noqodi.equals(statement.getType())) {
                extractNoqodiRecordsAndMappingThem(statement, workbook.getSheetAt(0));
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE,"Error while parsing file " + attachment.getName() + " for visaStatement #" + statement.getId(), e);
            throw new RuntimeException("Error while parsing file " + attachment.getName());
        }

        statement.setStatus(VisaStatementStatus.PENDING);
        visaStatementRepository.save(statement);
    }

    /** Handle Noqodi Records */
    public void extractNoqodiRecordsAndMappingThem(VisaStatement visaStatement, Sheet sheet) throws ParseException {
        logger.info("Starting optimized Noqodi records extraction");

        List<NoqodiStatementRecord> records = new ArrayList<>();

        // 1.Parse Noqodi Records and store them in records list
        parseNoqodiRecords(sheet, visaStatement, records);

        // 2.Save records
        records = noqodiStatementRecordRepository.saveAll(records);

        // 3.Create Brothers and master for common records based on Transaction Number
        handleNoqodiBrothersRecords(records);

        // 4.Process Transactions
        processNoqodiTransactions(visaStatement, records.stream()
                .map(NoqodiStatementRecord::getTransactionNumber).collect(Collectors.toList()));

        logger.info("Noqodi Processing Finished");
    }

    private void parseNoqodiRecords(Sheet sheet, VisaStatement visaStatement, List<NoqodiStatementRecord> records) throws ParseException {
        DataFormatter formatter = new DataFormatter();
        NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

        Iterator<Row> rowIterator = sheet.iterator();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String srNumber = formatter.formatCellValue(row.getCell(0)).trim();
            if (!StringUtils.isNumeric(srNumber)) continue;

            String transactionDateString = formatter.formatCellValue(row.getCell(3)).trim(); // Column D (index 3) – Date in new format
            Date transactionDate = DateUtil.parseNoqodiDate(transactionDateString);
            String description = formatter.formatCellValue(row.getCell(5)).trim(); // Column F (index 5) – Description in new format
            Double debit = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0; // Column G (index 6) – Debit in new format
            Double credit = !formatter.formatCellValue(row.getCell(7)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(7)).trim()).doubleValue() : 0.0; // Column H (index 7) – Credit in new format
            Double balance = !formatter.formatCellValue(row.getCell(9)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(9)).trim()).doubleValue() : 0.0; // Column J (index 9) – Balance in new format

            String transactionNumber;
            // If description starts with 'DHA Salem', use Merchant Order ID (column L, index 11) as transaction number
            if (description != null && description.startsWith("DHA Salem")) {
                transactionNumber = formatter.formatCellValue(row.getCell(11)).trim(); // Column L (index 11)
            } else {
                transactionNumber = formatter.formatCellValue(row.getCell(4)).trim(); // Column E (index 4) – Noqodi Reference No. in new format
            }

            NoqodiStatementRecord record = new NoqodiStatementRecord();
            record.setSrNumber(srNumber);
            record.setTransactionDate(transactionDate);
            record.setTransactionNumber(transactionNumber);
            record.setDescription(description);
            record.setDebit(debit);
            record.setCredit(credit);
            record.setBalance(balance);
            record.setStatement(visaStatement);

            records.add(record);
        }

        logger.info("Parsing Operation Finished for visaStatementId : " + visaStatement.getId() +
                " with records size : " + records.size());
    }

    private void handleNoqodiBrothersRecords(List<NoqodiStatementRecord> records) {
        // Grouping records based on transactionNumber or extracted reference number for refunds
        Map<String, List<NoqodiStatementRecord>> brothersMap = records.stream()
                .collect(Collectors.groupingBy(record -> {
                    // If Debit = 0 (refund), use extracted reference number from description
                    if (record.getDebit() == 0 &&
                            record.getCredit() > 0 &&
                            record.getDescription() != null &&
                            !record.getDescription().startsWith("DHA Salem")) {
                        String extractedRef = extractRefundReferenceNumber(record.getDescription());
                        if (extractedRef != null) {
                            logger.info("Using extracted reference number for grouping: " + extractedRef +
                                       " from description: " + record.getDescription());
                            return extractedRef;
                        }
                    }
                    // Otherwise, use original transaction number
                    return record.getTransactionNumber();
                }));

        for (Map.Entry<String, List<NoqodiStatementRecord>> entry : brothersMap.entrySet()) {
            List<NoqodiStatementRecord> brothersList = entry.getValue();

            if (brothersList != null && brothersList.size() > 1) {
                logger.info("Brothers found, size: " + brothersList.size() + " for transaction number: " + entry.getKey());
                createMasterRecordNoqodi(brothersList);
            }
        }
    }

    // Process Transactions
    private void processNoqodiTransactions(VisaStatement visaStatement, List<String> transactionNumbers) {

        // Pre-Load all expenses to avoid N+1 queries
        Map<String, List<Object[]>> expenseMap = preloadExpenses(transactionNumbers);
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");

        Long lastId = -1L;
        Page<NoqodiStatementRecord> p;
        List<VisaStatementTransaction> matched = new ArrayList<>();
        List<VisaStatementTransaction> missingFromErp = new ArrayList<>();
        List<VisaStatementTransaction> sameReferenceNumberButDifferentAmount = new ArrayList<>();

        int processedCount = 0;

        do {
            p = noqodiStatementRecordRepository.findByStatementAndIsFinal(lastId, visaStatement, false, PageRequest.of(0, BATCH_SIZE));
            if (p.isEmpty()) break;

            // Process records in current page
            for (NoqodiStatementRecord record : p.getContent()) {
                VisaStatementTransaction t = new VisaStatementTransaction();

                if (record.getCredit() > 0) {
                    t.setStatement(visaStatement);
                    t.setAmount(record.getCredit());
                    t.setCredit(true);
                    t.setReferenceNumber(record.getTransactionNumber());
                    t.setRowRecordDate(record.getTransactionDate());
                    if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                        t.setAmounts(record.getAmounts());
                    }

                    if (record.getDescription() != null && record.getDescription().toLowerCase()
                            .contains("ADCB Purchase Credit".toLowerCase())) {

                        t.setType(VisaStatementTransactionType.Ignore);
                        t.setFinished(true);
                    } else {
                        // MC-137 updates
                        // Handle refund matching based on transaction type
                        String refundRef = extractRefundReferenceNumber(record.getDescription());
                        t.setReferenceNumber(refundRef);
                        t.setRefundReferenceNumber(record.getTransactionNumber());

                        List<Object[]> visaExpenses = newVisaRequestExpenseRepository.findByExpenseReferenceNumber(refundRef);

                        if (visaExpenses.isEmpty()) {
                            t.setType(VisaStatementTransactionType.MissingFromERP);
                        } else {
                            t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, record.getCredit(), maidVisa);
                            t.setRowRecordDate(record.getTransactionDate());
                            t.setCredit(true);
                            t.setRefundReferenceNumber(record.getTransactionNumber());
                            // Get ERP amount
                            double erpAmount = getErpFullAmountOfRefund(visaExpenses.get(0));

                            // For credit transactions (refunds), compare credit amount with ERP amount
                            // Credit amount should match the absolute value of ERP amount (since ERP stores positive values)
                            if (record.getCredit() != null && Math.abs(record.getCredit()) == Math.abs(erpAmount)) {
                                t.setType(VisaStatementTransactionType.Matched);
                            } else {
                                double threshold = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                                        AccountingModule.PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD));

                                if (Math.abs(Math.floor(Math.abs(record.getCredit()) - Math.abs(erpAmount))) <= threshold) {
                                    // Auto-adjust the ERP amount
                                    VisaExpense expense = expenseService.getVisaExpenseByType(
                                            t.getVisaExpenseType(), t.getVisaRequestExpenseID());

                                    expense.setAmount(-Math.abs(record.getCredit()));
                                    expense.setCharge(null);
                                    expense.setVatCharge(null);
                                    expenseService.saveVisaExpenseByType(t.getVisaExpenseType(), expense);

                                    // Set as Matched
                                    t.setType(VisaStatementTransactionType.Matched);
                                } else {
                                    // The Difference is too large for auto-adjustment
                                    t.setType(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                                }
                            }
                        }
                    }

                } else if (record.getDebit() > 0) {
                    // Use pre-Loaded expense data
                    List<Object[]> visaExpenses = expenseMap.getOrDefault(record.getTransactionNumber(), Collections.emptyList());

                    t.setRowRecordDate(record.getTransactionDate());
                    t.setReferenceNumber(record.getTransactionNumber());
                    t.setAmount(record.getDebit());
                    t.setStatement(visaStatement);

                    if (record.getAmounts() != null && !record.getAmounts().isEmpty()) {
                        t.setAmounts(record.getAmounts());
                    }

                    if (visaExpenses.isEmpty()) {
                        t.setType(VisaStatementTransactionType.MissingFromERP);
                    } else {
                        t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, record.getDebit(), maidVisa);
                        t.setRowRecordDate(record.getTransactionDate());

                        // Get ERP amount
                        double erpAmount = getErpFullAmount(visaExpenses.get(0));

                        if (record.getDebit() != null &&
                                record.getDebit().equals(erpAmount)) {
                            t.setType(VisaStatementTransactionType.Matched);

                        } else {

                            double threshold = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                                    AccountingModule.PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD));

                            if (Math.abs(Math.floor(record.getDebit() - erpAmount)) <= threshold) {

                                // Auto-adjust the ERP amount
                                VisaExpense expense = expenseService.getVisaExpenseByType(
                                        t.getVisaExpenseType(), t.getVisaRequestExpenseID());

                                expense.setAmount(record.getDebit());
                                expense.setCharge(null);
                                expense.setVatCharge(null);
                                expenseService.saveVisaExpenseByType(t.getVisaExpenseType(), expense);

                                // Set as Matched
                                t.setType(VisaStatementTransactionType.Matched);
                            } else {
                                // The Difference is too large for auto-adjustment
                                t.setType(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                            }
                        }
                    }
                }

                if (t.getType().equals(VisaStatementTransactionType.Matched)) {
                    t.setDescriptionByDateAndReferenceNumber(t.getRowRecordDate() != null ? t.getRowRecordDate() : t.getExpenseCreationDate(),
                            t.isCredit() ? t.getRefundReferenceNumber() : t.getReferenceNumber());
                }

                t = visaStatementTransactionRepository.save(t);

                if (t.getType().equals(VisaStatementTransactionType.Matched)) {
                    matched.add(t);
                } else if (t.getType().equals(VisaStatementTransactionType.MissingFromERP)) {
                    missingFromErp.add(t);
                } else if (t.getType().equals(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount)) {
                    sameReferenceNumberButDifferentAmount.add(t);
                }

                record.setTransaction(t);
                noqodiStatementRecordRepository.save(record);
            }

            if(!p.isEmpty())
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            processedCount += p.getContent().size();

        } while (!p.isEmpty());

        expenseMap.clear();
        logger.info("Completed processing " + processedCount + " transaction records");

        missingFromErp.forEach(t -> {
            sendImmediateAlertForMissing(t,
                    missingFromErp.stream().filter(record -> !Objects.equals(record.getId(), t.getId()) &&
                            t.getAmount() == record.getAmount()).collect(Collectors.toList()),
                    matched);
        });

        createConfirmMatchedTransactionsBackgroundTask(visaStatement);
        sendImmediateAlertForSameReferenceNumberButDifferentAmount(sameReferenceNumberButDifferentAmount);
    }

    public void createMasterRecordNoqodi(List<NoqodiStatementRecord> records) {
        double amount = 0;
        double amountCredit = 0;
        StringBuilder amounts = new StringBuilder();
        StringBuilder amountsCredit = new StringBuilder();
        NoqodiStatementRecord master = new NoqodiStatementRecord();

        for (NoqodiStatementRecord statementRecord : records) {
            amount += statementRecord.getDebit();
            amountCredit+= statementRecord.getCredit();
            amounts.append(statementRecord.getDebit()).append(";");
            amountsCredit.append(statementRecord.getCredit()).append(";");
            statementRecord.setMaster(master);
            statementRecord.setFinal(true);
        }

        master.setTransactionNumber(records.get(0).getTransactionNumber());
        master.setTransactionDate(records.get(0).getTransactionDate());
        master.setDebit(amount);
        master.setCredit(amountCredit);
        master.setAmounts(amount >0  ? amounts.toString() : amountsCredit.toString());
        master.setStatement(records.get(0).getStatement());
        
        // Set description from the first record to enable reference number extraction
        // This is needed for refund processing where description contains reference numbers
        master.setDescription(records.get(0).getDescription());

        noqodiStatementRecordRepository.save(master);
        noqodiStatementRecordRepository.saveAll(records);
    }

    public double getErpFullAmountOfRefund(Object[] visaExpense) {
        return ((double) Math.round((
                (visaExpense[2] == null ? 0 : (Double) visaExpense[2]) -
                        (visaExpense[3] == null ? 0 : (Double) visaExpense[3]) -
                        (visaExpense[4] == null ? 0 : (Double) visaExpense[4])) * 100) / 100) ;
    }

    /**
     * Extract reference number from refund description
     * If Description starts with "Reversal DHA Salem Ref", extract the number between "Reversal DHA Salem Ref" and "Noqodi"
     * Otherwise, extract the "P1P..." reference number after "Noqodi Ref"
     */
    private String extractRefundReferenceNumber(String description) {
        if (description == null) return null;

        // Check for "Reversal DHA Salem Ref" pattern
        if (description.startsWith("Reversal DHA Salem Ref")) {
            // Extract number between "Reversal DHA Salem Ref" and "Noqodi"
            String pattern = "Reversal DHA Salem Ref\\s*([^\\s]+)\\s*,\\s*Noqodi";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(description);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        // Check for "P1P..." pattern after "Noqodi Ref"
        if (description.contains("Noqodi Ref")) {
            // Extract P1P... reference number after "Noqodi Ref"
            String pattern = "Noqodi Ref\\s*(P1P[^\\s]*)";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(description);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        return null;
    }

    // Call this after parsing for immediate alert
    private void sendImmediateAlertForMissing(
            VisaStatementTransaction record, List<VisaStatementTransaction> missing, List<VisaStatementTransaction> matched) {

        // Create a Map from the VisaStatementTransaction to match fillParams signature
        Map<String, Object> recordMap = new HashMap<>();
        recordMap.put("rowRecordDate", record.getRowRecordDate());
        recordMap.put("referenceNumber", record.getReferenceNumber());
        recordMap.put("amount", record.getAmount());
        recordMap.put("type", record.getType());

        Setup.getApplicationContext()
                .getBean(MessagingService.class).
                sendEmailToOfficeStaff(
                        "missing_visa_expense_alert",
                        fillParams(recordMap, matched.stream()
                                        .filter(t -> t.getAmount() == record.getAmount())
                                        .map(t -> {
                                            Map<String, Object> map = new HashMap<>();
                                            map.put("referenceNumber", t.getReferenceNumber());
                                            return map;
                                        })
                                        .collect(Collectors.toList()),
                                missing.size(),
                                new Date()),
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS),
                        "ACTION NEEDED: A Transaction is Missing in Visa Expenses");
    }

    // Call this after parsing for immediate alert for same reference number but different amount
    private void sendImmediateAlertForSameReferenceNumberButDifferentAmount(
            List<VisaStatementTransaction> sameReferenceNumberButDifferentAmount) {

        if (sameReferenceNumberButDifferentAmount.isEmpty()) return;

        Setup.getApplicationContext()
                .getBean(MessagingService.class).
                sendEmailToOfficeStaff(
                        "same_reference_number_but_different_amount_visa_expense_alert",
                        fillParamsForSameReferenceNumberButDifferentAmount(sameReferenceNumberButDifferentAmount),
                        Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_VISA_EXPENSE_SAME_REFERENCE_NUMBER_BUT_DIFFERENT_AMOUNTS_ALERT_RECIPIENTS),
                        "ACTION NEEDED: Same Reference Number but Different Amount in Visa Expenses");
    }

    // Trigger rematch after creating visa expense
    public void rematchVisaExpenseAfterCreation(String visaRequestExpenseType, VisaExpense expense) {
        List<VisaStatementTransaction> missingList = visaStatementTransactionRepository
                .findByTypeAndReferenceNumberAfterAndFinishedFalse(VisaStatementTransactionType.MissingFromERP, expense.getReferenceNumber());

        logger.info("start rematch visa expense after creation -> Visa Expense Entity Type: " + expense.getEntityType() +
                "; ID: " + expense.getId());

        if (missingList.isEmpty()) {
            expenseService.saveVisaExpenseByType(visaRequestExpenseType, expense);
            return;
        }

        logger.info("missing List size: " + missingList.size());

        List<VisaStatementTransaction> sameReferenceNumberButDifferentAmounts = new ArrayList<>();

        missingList.forEach(t -> {
            double threshold = t.getStatement().getType().equals(VisaStatementType.Noqodi) ? Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                    com.magnamedia.module.AccountingModule.PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD)) :
                    Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD));

            boolean predicate = t.isCredit() ?
                    t.getAmount() == Math.abs(expense.getAmount()) ||
                            Math.abs(Math.floor(Math.abs(t.getAmount()) - Math.abs(expense.getAmount()))) <= threshold :
                    t.getAmount() == expense.getAmount() ||
                            Math.abs(Math.floor(t.getAmount() - expense.getAmount())) <= threshold;

            logger.info("visa transaction statement amount: " + t.getAmount() +
                    "; visa expense amount: " + expense.getAmount() +
                    "; compare: " + (Double.compare(Math.abs(Math.floor(t.getAmount() - expense.getAmount())), threshold)));

            // Auto-adjust ERP amount
            t.fillVisaStatementTransactionInfo( new Object[] { BigInteger.valueOf(expense.getId()), expense.getEntityType() },
                    t.getStatement(), t.getAmount(), PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                            "maidvisa.ae_prospect"));

            if (predicate) {

                expense.setAmount(t.isCredit() ? -t.getAmount() : t.getAmount());
                expense.setCharge(null);
                expense.setVatCharge(null);
                // Save the expense with adjusted amount
                t.setType(VisaStatementTransactionType.Matched);
            } else {
                t.setType(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                sameReferenceNumberButDifferentAmounts.add(t);
            }

            // Update description when transaction becomes matched to use correct date
            if (t.getType().equals(VisaStatementTransactionType.Matched)) {
                t.setDescriptionByDateAndReferenceNumber(t.getRowRecordDate() != null ? t.getRowRecordDate() : t.getExpenseCreationDate(),
                        t.isCredit() ? t.getRefundReferenceNumber() : t.getReferenceNumber());
            }

            expenseService.saveVisaExpenseByType(visaRequestExpenseType, expense);
            visaStatementTransactionRepository.save(t);
            createConfirmMatchedTransactionsBackgroundTask(t.getStatement());
        });

        sendImmediateAlertForSameReferenceNumberButDifferentAmount(sameReferenceNumberButDifferentAmounts);
    }

    /** Handle Amwal Records */
    public void extractAmwalRecordsAndMappingThem(VisaStatement visaStatement, Sheet sheet) throws ParseException {
        logger.info("Starting optimized Amwal records extraction");

        List<AmwalStatementRecord> records = new ArrayList<>();

        // 1.Parse Amwal Records and save them into records list
        parseAmwalRecords(sheet, visaStatement, records);

        // 2.Process Transactions
        processAmwalTransactions(visaStatement, records);

        // 3.Auto Confirm Matched Records
        createConfirmMatchedTransactionsBackgroundTask(visaStatement);

        logger.info("Amwal Processing Finished");
    }

    private void parseAmwalRecords(Sheet sheet, VisaStatement visaStatement, List<AmwalStatementRecord> records) throws ParseException {
        DataFormatter formatter = new DataFormatter();
        NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
        Iterator<Row> rowIterator = sheet.iterator();

        if (rowIterator.hasNext()) {
            // Skip header
            rowIterator.next();
        }

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String transactionDateString = formatter.formatCellValue(row.getCell(0)).trim();
            if (transactionDateString.isEmpty()) break;

            Date transactionDate = DateUtil.parseAmwalDate(transactionDateString);
            String transactionType = formatter.formatCellValue(row.getCell(1)).trim();
            String transactionNumber = formatter.formatCellValue(row.getCell(2)).trim();
            String transactionStatue = formatter.formatCellValue(row.getCell(3)).trim();
            Double oldBalance = !formatter.formatCellValue(row.getCell(4)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(4)).trim()).doubleValue() : 0.0;
            Double availableBalance = !formatter.formatCellValue(row.getCell(5)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(5)).trim()).doubleValue() : 0.0;
            Double amount = !formatter.formatCellValue(row.getCell(6)).trim().isEmpty() ?
                    nf_in.parse(formatter.formatCellValue(row.getCell(6)).trim()).doubleValue() : 0.0;

            AmwalStatementRecord record = new AmwalStatementRecord();
            record.setStatement(visaStatement);
            record.setTransactionDate(transactionDate);
            record.setTransactionType(transactionType);
            record.setTransactionNumber(transactionNumber);
            record.setTransactionStatue(transactionStatue);
            record.setOldBalance(oldBalance);
            record.setAvailableBalance(availableBalance);
            record.setAmount(amount);

            records.add(record);
        }

        logger.info("Parsing operation Finished for visaStatementId : " + visaStatement.getId() +
                " with records size : " + records.size());
    }

    // Process Transactions
    private void processAmwalTransactions(VisaStatement visaStatement, List<AmwalStatementRecord> records) {
        // Initialize lists for collecting transactions by type
        List<VisaStatementTransaction> matched = new ArrayList<>();
        List<VisaStatementTransaction> missingFromErp = new ArrayList<>();
        List<VisaStatementTransaction> sameReferenceNumberButDifferentAmount = new ArrayList<>();

        int rowCount = 0;
        PicklistItem maidVisa = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE,
                "maidvisa.ae_prospect");
        String thresholdStr = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD);

        // Pre-Load all expenses
        Map<String, List<Object[]>> expenseMap = preloadExpenses(records.stream()
                .map(AmwalStatementRecord::getTransactionNumber).collect(Collectors.toList()));
        for (AmwalStatementRecord record : records) {

            // Use pre-loaded expense data instead of individual database calls
            List<Object[]> visaExpenses = expenseMap.getOrDefault(record.getTransactionNumber(), Collections.emptyList());

            VisaStatementTransaction t = new VisaStatementTransaction();
            t.setStatement(visaStatement);
            t.setAmount(record.getAmount());
            t.setReferenceNumber(record.getTransactionNumber());
            t.setRowRecordDate(record.getTransactionDate());

            if (visaExpenses.isEmpty()) {
                t.setType(VisaStatementTransactionType.MissingFromERP);
            } else {
                t = new VisaStatementTransaction(visaExpenses.get(0), visaStatement, t.getAmount(), maidVisa);

                // Get ERP amount
                double erpAmount = getErpFullAmount(visaExpenses.get(0));

                // Check if amounts match exactly
                if (record.getAmount() != null && record.getAmount().equals(erpAmount)) {
                    t.setType(VisaStatementTransactionType.Matched);
                } else {
                    // Check if the difference is less than the threshold for auto-adjustment
                    if (Math.abs(record.getAmount() - erpAmount) <= Double.parseDouble(thresholdStr)) {
                        // Auto-adjust the ERP amount
                        VisaExpense expense = expenseService.getVisaExpenseByType(
                                t.getVisaExpenseType(), t.getVisaRequestExpenseID());
                        expense.setAmount(record.getAmount());
                        expense.setCharge(null);
                        expense.setVatCharge(null);
                        expenseService.saveVisaExpenseByType(t.getVisaExpenseType(), expense);

                        // Set as Matched
                        t.setType(VisaStatementTransactionType.Matched);
                    } else {
                        // Difference is too large for auto-adjustment
                        t.setType(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount);
                    }
                }
            }

            t = visaStatementTransactionRepository.save(t);
            record.setTransaction(t);
            amwalStatementRecordRepository.save(record);

            // Add transaction to appropriate list based on type
            if (t.getType().equals(VisaStatementTransactionType.Matched)) {
                matched.add(t);
            } else if (t.getType().equals(VisaStatementTransactionType.MissingFromERP)) {
                missingFromErp.add(t);
            } else if (t.getType().equals(VisaStatementTransactionType.SameReferenceNumberButDifferentAmount)) {
                sameReferenceNumberButDifferentAmount.add(t);
            }
        }

        createConfirmMatchedTransactionsBackgroundTask(visaStatement);

        // Send immediate alerts for missing and mismatched transactions
        sendImmediateAlertForSameReferenceNumberButDifferentAmount(sameReferenceNumberButDifferentAmount);

        expenseMap.clear();
        logger.info("Completed processing " + rowCount + " Amwal records");

        createConfirmMatchedTransactionsBackgroundTask(visaStatement);
        missingFromErp.forEach(t -> {
            sendImmediateAlertForMissing(t,
                    missingFromErp.stream().filter(record -> !Objects.equals(record.getId(), t.getId()) &&
                            t.getAmount() == record.getAmount()).collect(Collectors.toList()),
                    matched);
        });
        // Send immediate alerts for missing and mismatched transactions
        sendImmediateAlertForSameReferenceNumberButDifferentAmount(sameReferenceNumberButDifferentAmount);
    }

    public double getErpFullAmount(Object[] visaExpense) {
        return ((double) Math.round((
                (visaExpense[2] == null ? 0 : (Double) visaExpense[2]) +
                (visaExpense[3] == null ? 0 : (Double) visaExpense[3]) +
                (visaExpense[4] == null ? 0 : (Double) visaExpense[4])) * 100) / 100) ;
    }

    /**
     * Pre-loads all expenses for fast lookup
     * This eliminates the N+1 query problem by loading all data upfront
     */
    private Map<String, List<Object[]>> preloadExpenses(List<String> transactionsNumbers) {
        logger.info("Pre-loading expenses");

        Map<String, List<Object[]>> expenseMap = new HashMap<>();

        logger.info("Found " + transactionsNumbers.size() + " unique reference numbers");

        if (transactionsNumbers.isEmpty()) {
            return expenseMap;
        }

        // Use optimized batch query to reduce database calls
        for (int i = 0; i < transactionsNumbers.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, transactionsNumbers.size());
            List<String> batch = transactionsNumbers.subList(i, endIndex).stream()
                    .filter(ref -> ref != null && !ref.trim().isEmpty())
                    .collect(Collectors.toList());

            if (!batch.isEmpty()) {
                // Use the new optimized batch query
                List<Object[]> batchResults = newVisaRequestExpenseRepository
                        .findByExpenseReferenceNumbersAndCreationDateBatch(batch);

                // Group results by reference number
                for (Object[] result : batchResults) {
                    String refNumber = (String) result[0];
                    // Create expense array without the reference number (shift indices)
                    // To match the result of `findByExpenseReferenceNumberAndCreationDate`
                    Object[] expense = new Object[result.length - 1];
                    System.arraycopy(result, 1, expense, 0, expense.length);

                    expenseMap.computeIfAbsent(refNumber, k -> new ArrayList<>()).add(expense);
                }
            }
        }

        logger.info("Pre-loaded " + expenseMap.size() + " expense mappings with " +
                   expenseMap.values().stream().mapToInt(List::size).sum() + " total expenses");
        return expenseMap;
    }

    /**
     * Creates a background task to confirm all matched transactions for a visa statement
     *
     * @param visaStatement The visa statement
     */
    public void createConfirmMatchedTransactionsBackgroundTask(VisaStatement visaStatement) {
        if (visaStatement == null) return;

        Map<String, Object> payload = new HashMap<>();
        payload.put("entityId", visaStatement.getId().toString());

        if (QueryService.existsEntity( BackgroundTask.class, "e.name = :p0 and e.status not in :p1",
                new Object[]{ "Confirm_Visa_Statement_Transactions_Records", Arrays.asList(
                        BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed) })) {
            // CREATE PROPERTY TO BE RUN IN AccountingModuleMainJob
            try {
                AccountingEntityProperty a = new AccountingEntityProperty();
                a.setKey(AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL);
                a.setPurpose(UploadStatementEntityType.ConfirmVisaStatementTransactions.toString());
                a.setValue(objectMapper.writeValueAsString(payload));
                accountingEntityPropertyRepository.save(a);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            // Create the background task
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .create(new BackgroundTask.builder(
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.toString(),
                            "accounting",
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetBean(),
                            UploadStatementEntityType.ConfirmVisaStatementTransactions.getTargetMethod())
                            .withRelatedEntity("VisaStatement", visaStatement.getId())
                            .withParameters(new Class[] { Map.class }, new Object[] { payload })
                            .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                            .build());
        }
    }

    /**
     * called from BackgroundTaskHelper.createBGTPreConfirmAllVisaStatementRecords
     * create Background task for each record related into VisaStatement
     * @param payload
     * */
    public void createBGTPreConfirmAllVisaStatementRecords(Map<String, Object> payload) {
        VisaStatement visaStatement = visaStatementRepository.findOne(Long.parseLong(payload.get("entityId").toString()));

        List<Map<String, Object>> l = visaStatementTransactionRepository.findRecordsToBeConfirmed(
                visaStatement, VisaStatementTransactionType.Matched);

        if (l.isEmpty()) {
            throw new BusinessException("there isn't any matched records to be confirmed");
        }

        l.forEach(v -> backgroundTaskService.create(new BackgroundTask.builder(
                UploadStatementEntityType.ConfirmVisaStatementTransaction.toString(),
                "accounting",
                UploadStatementEntityType.ConfirmVisaStatementTransaction.getTargetBean(),
                UploadStatementEntityType.ConfirmVisaStatementTransaction.getTargetMethod())
                .withRelatedEntity("VisaStatement",
                        Long.parseLong(payload.get("entityId").toString()))
                .withParameters(
                        new Class[] { Long.class },
                        new Object[] { v.get("visaStatementTransactionId") })
                .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                .build()));
    }


    /**
     * executed using BGT for confirm record
     * @param visaStatementTransactionId
     * */
    public void confirmTransaction(Long visaStatementTransactionId) {
        VisaStatementTransaction v = visaStatementTransactionRepository.findOne(visaStatementTransactionId);
        logger.info("visa statement transaction id : " + v.getId());

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(new java.util.Date().getTime()));
        transaction.setDescription(v.getDescription());
        transaction.setAmount(v.getAmount());
        transaction.setExpense(v.getExpense());
        transaction.setFromBucket(v.getFromBucket());
        transaction.setPaymentType(PaymentMethod.CARD);

        if (v.getEmployeeType().equals(EmployeeType.Officestaff)) {
            transaction.setTransactionType(TransactionEntityType.OFFICE_STAFF);
            OfficeStaffTransaction officeStaffTransaction = new OfficeStaffTransaction();
            officeStaffTransaction.setOfficeStaff(v.getOfficeStaff());
            transaction.setOfficeStaffs(Collections.singletonList(officeStaffTransaction));
        } else {
            transaction.setTransactionType(TransactionEntityType.HOUSEMAID);
            HousemaidTransaction housemaidTransaction = new HousemaidTransaction();
            housemaidTransaction.setHousemaid(v.getHousemaid());
            transaction.setHousemaids(Collections.singletonList(housemaidTransaction));
        }

        ResponseEntity<?> response = confirm(v, transaction);
        logger.info("after confirm records : " + v.getId() + " , response : " + response.getStatusCode());
    }

    public ResponseEntity<?> confirm(
            VisaStatementTransaction visaStatementTransaction,
            Transaction transaction) {

        visaStatementTransaction.setFinished(true);
        visaStatementTransaction.setExpense(transaction.getExpense());
        visaStatementTransaction.setDescription(transaction.getDescription());
        visaStatementTransaction.setFromBucket(transaction.getFromBucket());

        ResponseEntity<?> response = Setup.getApplicationContext().getBean(VisaRequestExpenseController.class)
                .addVisaRequestExpenseTransaction(visaStatementTransaction.getVisaRequestExpenseID(),
                        visaStatementTransaction.getVisaExpenseType(), transaction);

        transaction = Setup.getRepository(TransactionRepository.class).findOne(transaction.getId());
        visaStatementTransaction.setTransaction(transaction);
        visaStatementTransactionRepository.save(visaStatementTransaction);

        refreshStatement(visaStatementTransaction.getStatement());
        return response;
    }

    public void refreshStatement(VisaStatement statement) {
        if (statement == null || !statement.getCanBeDeleted()) return ;

        //ACC-9272 Call update API using IMC for handle OptimisticLockingFailureException
        Map body = new HashMap();
        body.put("id", statement.getId());
        body.put("canBeDeleted", Boolean.FALSE);
        Setup.getApplicationContext().getBean(InterModuleConnector.class)
                .postJsonAsync("accounting/visaStatement/update", body);
    }

    public Map<String, String> fillParams(Map record,
                                          List<Map> matched, long missingRecordsCount, Date fileUploadDate) {


        Map<String, String> params = new HashMap<>();
        params.put("date", record.get("rowRecordDate") != null ? new DateTime(record.get("rowRecordDate")).toString("yyyy-MM-dd HH:mm") : "");
        params.put("referenceNumber", record.get("referenceNumber") != null ? record.get("referenceNumber").toString() : "");
        params.put("amount", record.get("amount") != null ? String.valueOf(record.get("amount")) : "");

        params.put("matchedCount", String.valueOf(matched.size()));
        params.put("missingCount", String.valueOf(missingRecordsCount));
        params.put("today", new LocalDate(fileUploadDate).toString("yyyy-MM-dd"));

        params.put("sentence", !matched.isEmpty() ?
                "There are the reference numbers of the matched expenses with the same amount: " +
                        String.join(", ", matched.stream()
                                .filter(m -> m.get("referenceNumber") != null)
                                .map(m -> m.get("referenceNumber").toString())
                                .collect(Collectors.joining(", "))) :
                "");
        return params;
    }

    public Map<String, String> fillParamsForSameReferenceNumberButDifferentAmount(
            List<VisaStatementTransaction> sameReferenceNumberButDifferentAmountRecords) {

        Map<String, String> params = new HashMap<>();

        params.put("creationDates", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getExpenseCreationDate() != null ? new DateTime(record.getExpenseCreationDate()).toString("yyyy-MM-dd HH:mm") : "")
                .collect(Collectors.joining(", "))));

        params.put("names", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getName() != null ? record.getName() : "")
                .collect(Collectors.joining(", "))));

        params.put("erpAmounts", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getErpAmount() != null ? String.valueOf(record.getErpAmount()) : "")
                .collect(Collectors.joining(", "))));

        params.put("fileAmounts", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> String.valueOf(record.getAmount()))
                .collect(Collectors.joining(", "))));

        params.put("referenceNumbers", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getReferenceNumber() != null ? record.getReferenceNumber() : "")
                .collect(Collectors.joining(", "))));

        params.put("descriptions", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getDescription() != null ? record.getDescription() : "")
                .collect(Collectors.joining(", "))));

        params.put("bucketFroms", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getFromBucket() != null ? record.getFromBucket().getName() : "")
                .collect(Collectors.joining(", "))));

        params.put("expenseNames", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getExpense() != null ? record.getExpense().getName() : "")
                .collect(Collectors.joining(", "))));

        params.put("contractTypes", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getContractType() != null ? record.getContractType() : "")
                .collect(Collectors.joining(", "))));

        params.put("employeeTypes", String.join(", ", sameReferenceNumberButDifferentAmountRecords.stream()
                .map(record -> record.getEmployeeType() != null ? String.valueOf(record.getEmployeeType()) : "")
                .collect(Collectors.joining(", "))));

        return params;
    }
}