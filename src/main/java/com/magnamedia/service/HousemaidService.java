package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.NewVisaRequestRepository;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Karra
 * Created on Dec 28 , 2024
 */
@Service
public class HousemaidService {
    private static final Logger logger = Logger.getLogger(HousemaidService.class.getName());

    @Autowired
    private NewVisaRequestRepository newVisaRequestRepository;
    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private QueryService queryService;

    public static boolean hasBaseAdditionalInfoByKey(Long housemaidId, String key, String value) {
        return QueryService.existsEntity(BaseAdditionalInfo.class,
                "e.ownerId = :p0 and e.ownerType = 'Housemaid' and e.infoKey = :p1 and e.infoValue = :p2",
                new Object[]{ housemaidId, key, value });
    }

    public List<Housemaid> getHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        validateParameters(contractId, mobileNumber, eidNumber, firstName, middleName, lastName);
        validateNameFiltration(firstName, middleName, lastName);
        return fetchHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, contractId);
    }

    private List<Housemaid> fetchHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        List<Housemaid> housemaids = new ArrayList<>();

        if (StringUtils.isNotBlank(mobileNumber)) {
            housemaids = findMaidByMobileNumber(mobileNumber);
        }

        if (housemaids.isEmpty() && StringUtils.isNotBlank(eidNumber)) {
            housemaids = findHousemaidByEid(eidNumber);
        }

        if (housemaids.isEmpty() && !StringUtils.isAllBlank(firstName, middleName, lastName)) {
            housemaids = findByName(firstName, middleName, lastName);
        }

        if (housemaids.isEmpty() && contractId != null) {
            Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
            if (contract != null && contract.getHousemaid() != null) {
                housemaids.add(contract.getHousemaid());
            }
        }

        return housemaids;
    }

    public List<Housemaid> findMaidByMobileNumber(String mobileNumber) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        String normalizedPhoneNumber = com.magnamedia.extra.StringUtils.NormalizePhoneNumber(mobileNumber);
        query.filterBy("phoneNumber", "=", mobileNumber)
                .or("whatsAppPhoneNumber", "=", mobileNumber)
                .or("normalizedPhoneNumber", "=", normalizedPhoneNumber)
                .or("normalizedWhatsAppPhoneNumber", "=", normalizedPhoneNumber);

        query.sortBy("creationDate", false);
        return query.execute();
    }

    public List<Housemaid> findHousemaidByEid(String eidNumber) {
        List<NewRequest> newRequests = newVisaRequestRepository.findVisaNewRequestByNewEidNumberOrderByCreationDate(eidNumber);

        return newRequests.isEmpty() || newRequests.get(0).getHousemaid() == null ? new ArrayList<>()
                : Collections.singletonList(newRequests.get(0).getHousemaid());
    }

    public List<Housemaid> findByName(String firstName, String middleName, String lastName) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("name", "like", Stream.of(firstName, middleName, lastName)
                .filter(StringUtils::isNotBlank).collect(Collectors.joining("%")) + "%");
        query.sortBy("creationDate", false);
        return query.execute();
    }

    private void validateParameters(Long contractId, String mobileNumber, String eidNumber, String firstName, String middleName, String lastName) {
        if (contractId == null && StringUtils.isAllBlank(mobileNumber, eidNumber, firstName, middleName, lastName)) {
            throw new BusinessException("At least one of the parameters must be provided.");
        }
    }

    private void validateNameFiltration(String firstName, String middleName, String lastName) {
        if (StringUtils.isBlank(firstName) &&
                (StringUtils.isBlank(firstName) && !StringUtils.isAllBlank(middleName, lastName)) ||
                (StringUtils.isBlank(lastName) && StringUtils.isNotBlank(middleName))) {
            throw new BusinessException("Name based filtering should be passed as combination like either (firstName), (firstName, lastName) or (firstName, middleName, lastName).");
        }
    }

    public Date getHousemaidAssignmentDate(Contract contract) {
        if (contract.getHousemaid() == null) {
            return contract.getStartOfContract();
        }

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("id", "=", contract.getId());
        query.filterBy("housemaid", "=", contract.getHousemaid());
        query.sortBy("lastModificationDate", true); // Most recent first
        query.setLimit(1);

        List<Contract> history = query.execute();
        if (!history.isEmpty()) {
            return history.get(0).getLastModificationDate();
        }

        return contract.getStartOfContract();
    }

    public Map<String, Object> getHousemaidExpenseRequests(
            Long housemaidId, Long contractId, Date fromDate, Date toDate, boolean onlyAmount, Pageable pageable) {

        String fromStatement = " from ExpenseRequestTodo e " +
                "inner join e.expense ex " +
                "left join e.expensePayment ep";

        String query = "select new map(" +
                "e.id as id, " +
                "ex.id as expenseId, " +
                "ex.name as expenseName, " +
                "ex.caption as expenseCaption, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.purposeAdditionalDescription) as purposeAdditionalDescription, " +
                "e.creationDate as creationDate, " +
                "e.amount as amount, " +
                "e.loanAmount as loanAmount, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.currency) as currency, " +
                "e.paymentMethod as paymentMethod, " +
                "e.description as description, " +
                "e.status as status, " +
                "e.rejectionNotes as rejectionNotes, " +
                "(select u.fullName from User u where u = e.requestedBy) as requestedBy, " +
                "e.approvedBy as approvedBy, " +
                "e.notes as notes, " +
                "ep.id as paymentId," +
                "e.contractId as contractId) ";

        String conditions = " where e.relatedToId = :housemaidId and e.relatedToType = 'MAID' and ep.transaction is null ";
        conditions += " and e.status != 'DISMISSED' ";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("housemaidId", housemaidId);

        if (fromDate != null) {
            conditions += " and e.creationDate >= :fromDate ";
            parameters.put("fromDate", fromDate);
        }

        Map<String, Object> result = new HashMap<>();

        if (onlyAmount) {
            result.put("totalAmount", (Double) new SelectQuery<>("select sum(e.amountInLocalCurrency) " +
                    fromStatement + conditions,"",
                    Double.class, parameters).execute().get(0));
            return result;
        }

        if (contractId != null) {
            conditions += " and e.contractId = :contractId ";
            parameters.put("contractId", contractId);
        }

        if (toDate != null) {
            conditions += " and e.creationDate <= :toDate ";
            parameters.put("toDate", toDate);
        }

        query += fromStatement + conditions;

        query = queryService.sortDirectQueryByPageable(pageable, query, "e");

        String countQuery = "select count(e.id) " + fromStatement + conditions;

        SelectQuery<Map> q = new SelectQuery<>(query, countQuery, Map.class, parameters);

        Page<Map> p = q.execute(pageable);

        // fill attachments
        List<Map> attachments = queryService.getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(e -> (Long) e.get("id")).collect(Collectors.toList()), "ExpenseRequestTodo");
        p.getContent().forEach(e -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> e.get("id").equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            e.put("attachments", attachmentList);
        });

        result.put("data", new AccountingPage(p.getContent(), pageable, p.getTotalElements(), (Double) new SelectQuery<>("select sum(e.amountInLocalCurrency) " +
                fromStatement + conditions,"",
                Double.class, parameters).execute().get(0)));

        return result;
    }

    public Double getHousemaidExpenseRequestsTotalAmount(Long housemaidId, Date fromDate) {
        Map<String, Object> result = getHousemaidExpenseRequests(
                housemaidId, null, fromDate, null, true, null);
        return result.containsKey("totalAmount") ? (Double) result.get("totalAmount") : 0.0;
    }

    // ACC-9739
    /**
     * Verifies payment status for a bulk list of maid IDs for a specific month
     *
     * @param maidIds List of maid IDs to verify
     * @param targetMonth Target month (if null, uses previous month)
     * @return Map with maid ID as key and boolean as value (true = payment with us, false = payment not with us)
     */
    public Map<Long, Boolean> verifyMaidPaymentStatus(List<Long> maidIds, LocalDate targetMonth) {
        Map<Long, Boolean> results = new HashMap<>();

        logger.info("Verifying payment status for maids: " + maidIds +
                " for month: " + targetMonth.toString("yyyy-MM"));

        PicklistItem mv = PicklistHelper.getItem(AccountingModule.PICKLIST_PROSPECTTYPE, AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE);
        for (Long maidId : maidIds) {
            try {
                results.put(maidId, verifyMaidPaymentStatus(maidId, targetMonth, mv));
            } catch (Exception e) {
                logger.severe("Error verifying payment for maid ID: " + maidId);
                results.put(maidId, false); // Default to false on error
                e.printStackTrace();
            }
        }

        logger.info("result: " + results);
        return results;
    }

    /**
     * Verifies payment status for a single maid
     */
    private boolean verifyMaidPaymentStatus(Long maidId, LocalDate targetMonth, PicklistItem mv) {
        // Get the most recent CC contract for this maid (regardless of status)
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Long[] contractIdsByMaid = contractRepository.findContractsSwitchedFromCcToMvByHousemaid(maidId, mv.getId())
                .stream()
                .findFirst()
                .orElse(null);

        Contract mvContract = null, lastCcContract = null;
        if (contractIdsByMaid != null && contractIdsByMaid[0] != null && contractIdsByMaid[1] != null) {
            List<Contract> contracts = contractRepository.findAll(Arrays.asList(contractIdsByMaid[0], contractIdsByMaid[1]));
            for (Contract contract : contracts) {
                if (contract.isMaidVisa()) {
                    mvContract = contract;
                } else {
                    lastCcContract = contract;
                }
            }
        }

        if (mvContract == null || lastCcContract == null) {
            logger.info("No MV or CC contract found for maid ID: " + maidId);
            return false;
        }

        logger.info("Last MV Contract: " + mvContract.getId() +
                "; Last CC Contract: " + lastCcContract.getId());

        // Get the payment for the target month
        Payment ccPayment = getPaymentForMonth(lastCcContract, targetMonth);

        // Check condition 1: Payment not received
        if (ccPayment == null) {
            logger.info("No received payment found for maid ID: " + maidId +
                    " for month: " + targetMonth.toString("yyyy-MM"));
            return false;
        }

        // Check condition 2: Refund detected (hasMatchingRefund)
        if (hasMatchingRefund(lastCcContract, ccPayment)) {
            logger.info("Matching refund found for maid ID: " + maidId);
            return false;
        }

        // Check condition 3: Has Credit Note Under Mv Contract
        if (QueryService.existsEntity(ContractPaymentTermDetails.class,
                "e.contractPaymentTerm.contract.id = :p0 and e.source = :p1 and e.amount = :p2 and e.creationDate > :p3 ",
                new Object[]{
                        mvContract.getId(), ContractPaymentTermDetails.Source.CREDIT_NOTE,
                        ccPayment.getAmountOfPayment(),
                        ccPayment.getDateChangedToReceived()})) {
            logger.info("Credit note found under MV contract for maid ID: " + maidId);
            return false;
        }

        // All checks passed - payment is with us
        logger.info("Payment verified as with us for maid ID: " + maidId);
        return true;
    }

    /**
     * Gets the most recent CC contract for the maid (regardless of status)
     */
    private Contract getLastCcContract(Housemaid h, Client c, PicklistItem cc) {
        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("id", "in", c.getAllContracts().stream().map(Contract::getId).collect(Collectors.toList()));
        query.filterBy("housemaid", "=", h);
        query.filterBy("contractProspectType", "=", cc);
        query.filterByChanged("housemaid");
        query.sortBy("lastModificationDate", false, true); // DESC order
        query.setLimit(1);

        List<Contract> contracts = query.execute();
        return contracts.isEmpty() ? null : contracts.get(0);
    }

    /**
     * Gets the most recent active MV contract for the maid
     */
    private Contract getLastMvContract(Long maidId) {
        SelectQuery<Contract> query = new SelectQuery<>(Contract.class);
        query.filterBy("housemaid.id", "=", maidId);
        query.filterBy("contractProspectType.code", "=", AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE);
        query.filterBy("status", "=", ContractStatus.ACTIVE);
        query.sortBy("creationDate", false, true);
        query.setLimit(1);

        List<Contract> contracts = query.execute();
        return contracts.isEmpty() ? null : contracts.get(0);
    }

    /**
     * Gets payment for specific month and year from contract
     */
    private Payment getPaymentForMonth(Contract contract, LocalDate targetMonth) {
        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("contract.id", "=", contract.getId());
        query.filterBy("status", "=", PaymentStatus.RECEIVED);
        query.filterBy("typeOfPayment.code", "=", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        query.filterBy("dateOfPayment", ">=", targetMonth.dayOfMonth().withMinimumValue().toDate());
        query.filterBy("dateOfPayment", "<=", targetMonth.dayOfMonth().withMaximumValue().toDate());
        query.setLimit(1);

        List<Payment> payments = query.execute();
        return payments.isEmpty() ? null : payments.get(0);
    }

    private boolean hasMatchingRefund(Contract lastCcContract, Payment ccPayment) {
        SelectQuery<ClientRefundToDo> query = new SelectQuery<>(ClientRefundToDo.class);
        query.filterBy("contract.id", "=", lastCcContract.getId());
        query.filterBy("creationDate", ">", ccPayment.getDateOfPayment());
        query.filterBy("status", "in", Arrays.asList(ClientRefundStatus.PENDING, ClientRefundStatus.PAID));

        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "amount");
        double totalAmount = aggQuery.execute().doubleValue();

        if (totalAmount >= ccPayment.getAmountOfPayment()) {
            logger.info("Matching refund found with Total amount: " + totalAmount);
            return true;
        }

        return false;
    }
}