package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowProgressPeriod;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class IncompleteDirectDebitService {
    private static final Logger logger = Logger.getLogger(IncompleteDirectDebitService.class.getName());

    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private DirectDebitService directDebitService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    public void startIncompleteFlowMissingBankInfo(ContractPaymentTerm cpt, List<DirectDebit> l) {
        // the new trigger of the flow is having an Incomplete DD not only due to missing Signatures but due to the miss of any needed bank info
        // and there is no “Incomplete flow / Data entry rejection” running on those DDs
        if (cpt.getIsIBANRejected() ||
                cpt.getIsEidRejected() ||
                cpt.getIsAccountHolderRejected()) return;

        Map<String, Object> m = validateFlowStopping(cpt.getContract(), null);
        if ((boolean) m.get("stopped") || (boolean) m.get("completed")) return;

        if (cpt.getContract().isSigningPaperMode()) {
            List<DirectDebitSignature> signatures = Setup.getApplicationContext()
                    .getBean(DirectDebitSignatureService.class)
                    .getSignatureSortedList(cpt.getContract().getClient(), cpt.getEid());

            if (!signatures.isEmpty()) {
                signatures.forEach(d -> d.setDisable(true));
                Setup.getRepository(DirectDebitSignatureRepository.class).save(signatures);
            }
        }

        DirectDebit dd = l.stream()
                .filter(d -> d.getDirectDebitRejectionToDo() == null && d.getDirectDebitBouncingRejectionToDo() == null)
                .sorted(Comparator.comparing(DirectDebit::getCategory).reversed())
                .sorted(Comparator.comparing(d -> d.getPayments() != null && !d.getPayments().isEmpty() ?
                        d.getPayments().get(0).getPaymentType().getId() : 0))
                .min(Comparator.comparing(DirectDebit::getStartDate))
                .orElse(null);

        logger.info("start incomplete flow cpt id: " + cpt.getId());
        Map<String, Object> map = new HashMap<>();
        map.put("trials", 1);
        map.put("reminders", 0);
        map.put("lastExecutionDate", new Date());

        if (dd != null) {
            map.put("directDebit", dd);
        }

        FlowSubEventConfig.FlowSubEventName flowSubEventName = IncompleteFlowMissingDdInfoService
                .getDDMessagingFlowStatus(
                        cpt.getContract(),
                        FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);

        flowProcessorService.createFlowProcessor(
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO,
                flowSubEventName, cpt, map);
    }

    public Map<String, Object> validateFlowStopping(Contract c, FlowProcessorEntity f) {
        Map<String, Object> m = new HashMap<>();
        m.put("completed", false);
        m.put("stopped", false);

        if (c.isPayingViaCreditCard()) { // ACC-6703
            logger.log(Level.INFO, "entity id: {0}; Flow stopping because the contract flagged as paying via credit card", (f == null ? "Null" : f.getId()));
            m.put("stopped", true);
            return m;
        }

        if (f != null && flowProcessorService.clientProvidesSignatureAndBankInfo(c, f.getCreationDate())) {
            logger.log(Level.INFO, "entity id: {0}; Flow stopping because client provides missing bank info", f.getId());
            m.put("completed", true);
            return m;
        }

        // ACC-8526
        if (!directDebitService.existsByDdStatuses(c.getActiveContractPaymentTerm(), Collections.singletonList(DirectDebitStatus.IN_COMPLETE))) {
            logger.info("entity id: " + (f == null ? "NULL" : f.getId()) + "; Flow stopping because there isn't any incomplete DDs");
            m.put("stopped", true);
            return m;
        }
        return m;
    }

    public String getSignDdLinkForIncompleteFlowMissingBankInfo(Map<String, Object> map, boolean withShortLink) {

        StringBuilder currentFlow = new StringBuilder("&currentFlow=incomplete_flow_missing_bank_info");
        List<String> l = Setup.getApplicationContext()
                        .getBean(DirectDebitService.class)
                                .getMissingBankInfo(((ContractPaymentTerm) map.get("cpt")));

        for (String s : l) {
            switch (s){
                case "Signatures":
                    currentFlow.append("&signatureIsMissing=true");
                    break;
                case "IBAN":
                    currentFlow.append("&ibanIsMissing=true");
                    break;
                case "Account name":
                    currentFlow.append("&accountNameIsMissing=true");
                    break;
                case "EID":
                    currentFlow.append("&eidIsMissing=true");
                    break;
            }
        }

        if (currentFlow.toString().equals("&currentFlow=incomplete_flow_missing_bank_info")) {
            currentFlow.append("&signatureIsMissing=true");
        }

        return AccountingLinkService.getSignDdLink(map, currentFlow.toString(), withShortLink);
    }

    public void stopMissingBankInfoFlow(Contract c) {
        stopMissingBankInfoFlow(c, false);
    }

    public void stopMissingBankInfoFlow(Contract c, boolean isCompleted) {
        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);

        FlowProcessorEntity f = flowProcessorService.getFirstRunningFlow
                (c, FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);
        if (f == null) return;

        logger.info("flow id: " + f.getId());
        if (isCompleted) {
            f.setCompleted(true);
        } else {
            f.setStopped(true);
        }
        flowProcessorEntityRepository.save(f);

        pushNotificationHelper.stopDisplaying(
                Setup.getRepository(DisablePushNotificationRepository.class)
                        .findActiveNotificationsByDDMessagingType(
                                c.getClient().getId().toString(),
                                DDMessagingType.IncompleteDDClientHasNoApprovedSignature,
                                c.getId()));
    }

    public boolean validateAndStartIncompleteFlowMissingBankInfo(List<DirectDebit> dds, ContractPaymentTerm cpt) {

        if (dds.stream().anyMatch(dd -> !dd.isHidden() && cpt.getContract().getStatus().equals(ContractStatus.ACTIVE) &&
                (dd.getDirectDebitRejectionToDo() == null && dd.getDirectDebitBouncingRejectionToDo() == null) &&
                ((dd.getCategory().equals(DirectDebitCategory.A) && dd.getMStatus().equals(DirectDebitStatus.IN_COMPLETE)) ||
                        (dd.getCategory().equals(DirectDebitCategory.B) && dd.getStatus().equals(DirectDebitStatus.IN_COMPLETE))))) {

            logger.info("startIncompleteFlowMissingBankInfo for ddc Id: " + cpt.getDdcId());
            Setup.getApplicationContext()
                    .getBean(IncompleteDirectDebitService.class)
                    .startIncompleteFlowMissingBankInfo(dds.get(0).getContractPaymentTerm(), dds);
            return true;
        }
        return false;
    }

    // Related Missing Bank Info Flow
    public void messagingFilter(
            SelectFilter selectFilter,
            FlowProcessorEntity entity) {

        String contractType = DDMessagingService.isContractMvSdr(entity.getContract()) ?
                DDMessagingContractType.MAID_VISA_SDR.getLabel() :
                entity.getContract().getContractProspectType().getCode();

        selectFilter.and("contractProspectTypes", "like", "%" + contractType + "%");
    }

    // ACC-8951
    // Related Missing Bank Info Flow And Data Entry Rejected Flow
    public void updateToPaidFlow(FlowProcessorEntity entity) {

        if (Arrays.asList(
                FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO_PAID,
                FlowSubEventConfig.FlowSubEventName.MISSING_DD_INFO_PAID)
                .contains(entity.getCurrentSubEvent().getName())) return;

        FlowSubEventConfig.FlowSubEventName flowSubEventName = IncompleteFlowMissingDdInfoService.getDDMessagingFlowStatus(
                entity.getContract(),
                entity.getFlowEventConfig());

        // Check if the flow is changing to Paid
        if (Arrays.asList(
                FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO_NOT_PAID,
                        FlowSubEventConfig.FlowSubEventName.MISSING_DD_INFO_NOT_PAID)
                .contains(flowSubEventName)) return;

        logger.info("Update To Paid Flow id: " + entity.getId());
        FlowSubEventConfig subEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig_Name(flowSubEventName, entity.getFlowEventConfig().getName());

        entity.setCurrentSubEvent(subEventConfig);
        flowProcessorEntityRepository.save(entity);
    }

    // ACC-8951
    // Related Missing Bank Info Flow And Data Entry Rejected Flow
    public boolean shouldDelayMessage(FlowProcessorEntity flow) throws ParseException {
        logger.info("Flow Trials: " + flow.getTrials() + "; Flow reminders: " + flow.getReminders());

        // 1- Check stop flow
        if (flowProcessorService.validateFlowStopping(flow)) return true;

        // 2- Check the flow
        if (!Arrays.asList(FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO,
                FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO)
                .contains(flow.getFlowEventConfig().getName())) return false;

        // 3- Check is not paid flow
        if (!Arrays.asList(FlowSubEventConfig.FlowSubEventName.MISSING_BANK_INFO_PAID,
                        FlowSubEventConfig.FlowSubEventName.MISSING_DD_INFO_PAID)
                .contains(flow.getCurrentSubEvent().getName())) return false;

        // 4- Check Max Trial or Remainder
        int inCompleteBeforeXDaysPED = Utils.parseValue(
                flow.getFlowEventConfig().getTagValue("incomplete_flow_before_x_days_paid_end_date_period").getValue(), Integer.class);

        // Check Terminate on max reminder && delay normal message
        if (!flow.getCurrentSubEvent().isTerminateContractOnMaxReminders() ||
                flow.getReminders() != flow.getCurrentSubEvent().getMaxReminders() - inCompleteBeforeXDaysPED) return false;

        // 5- Check PED - xDays in the future
        if (!new LocalDate(flow.getContract().getPaidEndDate()).minusDays(inCompleteBeforeXDaysPED).isBefore(new LocalDate())) return false;

        int reminders = Math.min(flow.getReminders() + 1, flow.getCurrentSubEvent().getMaxReminders());
        FlowProgressPeriod flowProgressPeriod = flow.getCurrentSubEvent().getProgressPeriods()
                .stream()
                .filter(progress ->
                        flow.getTrials() == progress.getTrials() && reminders == progress.getReminders())
                .findFirst()
                .orElse(null);

        return flowProcessorService.processFlowLastExecutionDateAndNextTrialSendDate(flow, flowProgressPeriod);
    }

    public void handleFlowAfterTheClientProvidesNewInfo(DirectDebit directDebit) {
        FlowProcessorEntity flow = flowProcessorService.getFirstRunningFlow(
                        directDebit.getContractPaymentTerm().getContract(),
                        FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO);

        // When the client provides information after the termination message has been sent,
        // the contract is already in SFT status, and the flow has stopped.
        if (flow == null) {
            flow = flowProcessorEntityRepository.findMissingDdInfoFlowStoppedByContractAndDdBankInfoGroup(
                            directDebit.getContractPaymentTerm().getContract().getId(),
                            FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO,
                            directDebit.getDdBankInfoGroup())
                    .stream()
                    .findFirst()
                    .orElse(null);
        }

        if (flow == null) return;

        flow.setTrials(flow.getTrials() + 1);
        flow.setReminders(0);
        flow.setStatus(FlowProcessorEntity.FlowProcessorStatus.FROZEN);
        if (flow.isStopped()) { flow.setStopped(false); }
        flowProcessorEntityRepository.save(flow);
    }
}