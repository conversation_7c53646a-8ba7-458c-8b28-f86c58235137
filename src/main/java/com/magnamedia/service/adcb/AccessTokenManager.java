package com.magnamedia.service.adcb;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Component
@Log4j2
public class AccessTokenManager {

    /*@Value("${system.property.adcb.client.id}")
    private String clientId;

    @Value("${system.property.adcb.client.secret}")
    private String clientSecret;

    @Value("${system.property.adcb.scope}")
    private String scope;

    @Value("${system.property.adcb.grant.type}")
    private String grantType;

    @Value("${system.property.adcb.token.url}")
    private String tokenUrl;*/

   /* private final String PASSWORD;
    private final String CERTIFICATE_PATH;

    @Autowired
    public AccessTokenManager(
            @Value("${adcb.cert.keystore.password}") String password,
            @Value("${adcb.cert.keystore.file.path}") String certificatePath) {
        this.PASSWORD = password;
        this.CERTIFICATE_PATH = certificatePath;
    }

    public Map<String, String> getSecrets() {
        return new HashMap<String, String>() {{
            put("password", PASSWORD);
            put("certificate", CERTIFICATE_PATH);
        }};
    }*/

    private static String token;
    private static long expiryTime = 0;

    public synchronized String getAccessToken(HashMap<String, Object> body) {
        if (token == null || System.currentTimeMillis() >= expiryTime) {
            refreshAccessToken(body);
        }
        return token;
    }

    private void refreshAccessToken(HashMap<String, Object> body) {
        try {
            if (body.get("requestType").equals("query")) {
                refreshAccessTokenQuery(body);
                return;
            }

            RestTemplate restTemplate = new RestTemplate();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            /*log.info("client_id : {}, client_secret : {}, scope : {}, grant_type : {}",
                    clientId, clientSecret, scope, grantType);
            log.info("tokenUrl : {}", body.get("tokenUrl"));*/

            map.add("client_id", (String) body.get("clientId"));
            map.add("client_secret", (String) body.get("clientSecret"));
            map.add("scope", (String) body.get("scope"));
            map.add("grant_type", (String) body.get("grantType"));

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity((String) body.get("tokenUrl"), request, Map.class);
            log.info("response status : {}", response.getStatusCode());
            log.info("response body : {}", response.getBody());

            token = (String) response.getBody().get("access_token");
            long expiresIn = Long.parseLong(String.valueOf(response.getBody().get("expires_in")));
            log.info("expiresIn : {}", expiresIn);
            expiryTime = System.currentTimeMillis() + (expiresIn * 1000);
        } catch (Exception e) {
            log.error("Error : {}", e.getMessage());
            e.printStackTrace();
        }
    }

    private void refreshAccessTokenQuery(HashMap<String, Object> body) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            UriComponentsBuilder builder = UriComponentsBuilder.fromUri(new URI((String) body.get("tokenUrl")))
                    .queryParam("client_id", body.get("clientId"))
                    .queryParam("client_secret", body.get("clientSecret"))
                    .queryParam("scope", body.get("scope"))
                    .queryParam("grant_type", body.get("grantType"));

            HttpEntity<Void> request = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    builder.toUriString(),
                    HttpMethod.POST,
                    request,
                    Map.class
            );

            log.info("response status : {}", response.getStatusCode());
            log.info("response body : {}", response.getBody());

            token = (String) response.getBody().get("access_token");
            long expiresIn = Long.parseLong(String.valueOf(response.getBody().get("expires_in")));
            expiryTime = System.currentTimeMillis() + (expiresIn * 1000);

            log.info("expiresIn : {}", expiresIn);
        } catch (Exception e) {
            log.error("Error : {}", e.getMessage());
            e.printStackTrace();
        }
    }
}