package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.CreditCardOfferProperties;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.entity.ContractPaymentConfirmationToDo.Source.INCOMPLETE_FLOW_MISSING_BANK_INFO;

// ACC-6624
@Service
public class CreditCardOfferService {
    private static final Logger logger = Logger.getLogger(CreditCardOfferService.class.getName());

    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private Shortener shortener;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private ContractPaymentService contractPaymentService;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private IncompleteDirectDebitService incompleteDirectDebitService;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private QueryService queryService;

    public void createTodoIfNotExists(ContractPaymentTerm cpt, DDMessaging ddMessaging, Map<String, String> parameters) throws Exception {

        if (!flowProcessorMessagingService.isDdMessagingContainsCreditCardOfferLink(ddMessaging)) return;

        CreditCardOfferProperties creditCardOfferProperties = CreditCardOfferProperties.fill();
        if (creditCardOfferProperties == null || !creditCardOfferProperties.isEnableFeature() ||
                (!creditCardOfferProperties.getExcludedFlows().isEmpty() &&
                        creditCardOfferProperties.getExcludedFlows().contains(ddMessaging.getEvent()))) return;

        Map<String, Object> m = getFlowInfo(ddMessaging, parameters, cpt);
        if (m.isEmpty()) return;
        Long relatedToEntityId = (Long) m.get("relatedToEntityId");
        String relatedToEntityType = (String) m.get("relatedToEntityType");
        int maxTrial = (int) m.get("maxTrial");
        int maxReminder = (int) m.get("maxReminder");
        ContractPaymentConfirmationToDo.Source source = (ContractPaymentConfirmationToDo.Source) m.get("source");

        if (Math.max((maxTrial - Integer.parseInt(ddMessaging.getTrials()) -
                creditCardOfferProperties.getNumberBeforeMaxTrialOrReminder()), 0) > 0 &&
                Math.max((maxReminder - Integer.parseInt(ddMessaging.getReminders()) -
                        creditCardOfferProperties.getNumberBeforeMaxTrialOrReminder()), 0) > 0) {
            logger.info("maxTrial: " + maxTrial +
                    "; maxReminder: " + maxReminder +
                    "; numberBeforeMaxTrialOrReminder: " + creditCardOfferProperties.getNumberBeforeMaxTrialOrReminder());
            return;
        }

        List<ContractPaymentConfirmationToDo> l = contractPaymentConfirmationToDoRepository.findAllConfirmationToDoByContractPaymentTermAndSource(
                cpt,
                Collections.singletonList(source));

        logger.info("ContractPaymentConfirmationToDo size: " + l.size());
        ContractPaymentConfirmationToDo todo;
        if (l.isEmpty()) {
            List<ContractPayment> contractPayments = getMatchedContractPayment(cpt, relatedToEntityType, relatedToEntityId, source);
            if (contractPayments == null || contractPayments.isEmpty()) return;

            contractPayments = contractPaymentService.getUniqueAndSortedPayments(
                    cpt.getContract(),
                    contractPayments,
                    null);
            logger.info("UniqueAndSortedContractPayment size: " + contractPayments.size());

            Map<String, Object> map = new HashMap<>();
            map.put("required", false);
            map.put("creditCardOffer", true);
            map.put("relatedToEntityId", relatedToEntityId);
            map.put("relatedToEntityType", relatedToEntityType);
            todo = contractPaymentConfirmationToDoService.createConfirmationTodoFromContractPayments(
                    cpt, contractPayments, source, map);
        } else {
            todo = l.get(0);
        }

        logger.info("todo id: " + todo.getId());

        String link  = contractPaymentConfirmationToDoService.getPayingViaCreditCardLink(todo);
        boolean isMonthly = todo.getContractPaymentList().stream().anyMatch(p -> PaymentHelper.isMonthlyPayment(p.getPaymentType()));
        boolean hasConfirmedDdb = Setup.getApplicationContext().getBean(DirectDebitService.class)
                .hasConfirmedDdb(cpt.getContract(),
                        contractPaymentConfirmationToDoService.getUpcomingMonthlyPaymentDatePerTodo(todo));
        logger.info("hasConfirmedDdb : " + hasConfirmedDdb);

        parameters.put("sms_cc_offer_sentence", (isMonthly && !hasConfirmedDdb ?
                creditCardOfferProperties.getMonthlySmsSentence() : creditCardOfferProperties.getNonMonthlySmsSentence())
                .replace("@online_payment_link@", link));
        parameters.put("cc_offer_sentence", isMonthly && !hasConfirmedDdb ?
                creditCardOfferProperties.getMonthlyNotificationSentence() : creditCardOfferProperties.getNonMonthlyNotificationSentence());

        parameters.put("credit_card_offer_link", link);
        parameters.put("todoUuid", todo.getUuid());
        parameters.put("todoTotalAmount", todo.getTotalAmount().toString());
        parameters.put("cc_offer_cta_label", creditCardOfferProperties.getCtaLabel());
    }

    private Map<String, Object> getFlowInfo(DDMessaging ddMessaging, Map<String, String> parameters, ContractPaymentTerm cpt) {
        Map<String, Object> m = new HashMap<>();

        switch (ddMessaging.getEvent()) {
//            case BouncedPayment:
//                m.put("relatedToEntityId", Long.valueOf(parameters.get("bouncedPaymentId")));
//                m.put("relatedToEntityType", "Payment");
//                m.put("maxTrial", Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), cpt.getContract().isMaidCc() ?
//                        AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC : AccountingModule.PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV)));
//                m.put("maxReminder", 3);
//                m.put("source", ContractPaymentConfirmationToDo.Source.BOUNCED_PAYMENT_FLOW);
//                break;
            case IncompleteDDClientHasNoApprovedSignature:
                m.put("relatedToEntityId", Long.valueOf(parameters.get("directDebitId")));
                m.put("relatedToEntityType", "DirectDebit");
                FlowProcessorEntity flowProcessorEntity = Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .getFirstRunningFlow(cpt.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO);

                m.put("maxTrial", flowProcessorEntity.getCurrentSubEvent().getMaxTrials());
                m.put("maxReminder", flowProcessorEntity.getCurrentSubEvent().getMaxReminders());
                m.put("source", ContractPaymentConfirmationToDo.Source.INCOMPLETE_FLOW_MISSING_BANK_INFO);
                break;
            case DirectDebitRejected:
                m.put("relatedToEntityId", Long.valueOf(parameters.get("directDebitId")));
                m.put("relatedToEntityType", "DirectDebit");
                m.put("maxTrial", ddMessaging.getRejectCategory().equals(DirectDebitRejectCategory.Signature) ?
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS)):
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS)));
                m.put("maxReminder", ddMessaging.getRejectCategory().equals(DirectDebitRejectCategory.Signature) ?
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_SIGN_REMINDERS)):
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_REMINDERS)));
                m.put("source", ContractPaymentConfirmationToDo.Source.DD_REJECTED);
                break;
            case IncompleteDDRejectedByDataEntry:
                m.put("relatedToEntityId", Long.valueOf(parameters.get("directDebitId")));
                m.put("relatedToEntityType", "DirectDebit");

                FlowProcessorEntity flow = Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .getFirstRunningFlow(cpt.getContract(), FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO);

                m.put("maxTrial", flow.getCurrentSubEvent().getMaxTrials());
                m.put("maxReminder", flow.getCurrentSubEvent().getMaxReminders());
                m.put("source", ContractPaymentConfirmationToDo.Source.INCOMPLETE_FLOW_DATA_ENTRY_REJECTION);
                break;
        }

        return m;
    }

    public List<ContractPayment> getMatchedContractPayment(ContractPaymentTerm cpt, String relatedToEntityType, Long relatedToEntityId, ContractPaymentConfirmationToDo.Source source) {
        logger.info("cpt id: " + cpt.getId() +
                "; relatedToEntityType: " + relatedToEntityType +
                "; relatedToEntityId: " + relatedToEntityId +
                "; source: " + source);

        switch (relatedToEntityType) {
            case "DirectDebit":
                List<ContractPayment> l = getAllDdPayments(cpt, source);
                if (!l.isEmpty()) return l;

                return getNearestContractPayment(cpt, source);
//            case "Payment":
//                Payment payment = paymentRepository.findOne(relatedToEntityId);
//                contractPayments = contractPaymentRepository.findMatchedContractPayment(
//                        cpt.getContract(),
//                        payment.getMethodOfPayment(), payment.getTypeOfPayment(),
//                        new LocalDate(payment.getDateOfPayment()).dayOfMonth().withMinimumValue().toDate(),
//                        new LocalDate(payment.getDateOfPayment()).dayOfMonth().withMaximumValue().toDate(),
//                        payment.getAmountOfPayment());
//
//                return contractPayments.isEmpty() ? null : contractPayments.get(0);
        }

        return null;
    }

    public List<ContractPayment> getAllDdPayments(ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Source source) {

        switch (source) {
            case INCOMPLETE_FLOW_MISSING_BANK_INFO:
            case INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
                return queryService.getAllDdPaymentsRelatedFlows(source, null,cpt);
            case DD_REJECTED:
                List<ContractPayment> contractPayments = queryService.getAllDdPaymentsRelatedFlows(source, true,cpt);
                if (!contractPayments.isEmpty()) return contractPayments;

                contractPayments = queryService.getAllDdPaymentsRelatedFlows(source, false,cpt);
                logger.info("RejectionToDo contractPayments size: " + contractPayments.size());
                return contractPayments;
            default:
                return new ArrayList<>();
        }
    }

    public List<ContractPayment> getNearestContractPayment(ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Source source) {

        List<Object[]> l;
        switch (source) {
            case INCOMPLETE_FLOW_MISSING_BANK_INFO:
            case INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
               l = queryService.getAllUpcomingContractPaymentsByRelatedFlow(
                       source, null, cpt.getId(),
                       new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                       Collections.singletonList(DirectDebitStatus.IN_COMPLETE));
                break;
            case DD_REJECTED:
               l = queryService.getAllUpcomingContractPaymentsByRelatedFlow(
                       source, true, cpt.getId(),
                       new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                       DirectDebitService.activeWithoutConfirmedStatuses);

                if (l.isEmpty()) {
                    l = queryService.getAllUpcomingContractPaymentsByRelatedFlow(
                            source, false, cpt.getId(),
                            new LocalDate().plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                            DirectDebitService.activeWithoutConfirmedStatuses);
                }
                break;
            default:
                return new ArrayList<>();
        }

        logger.info("Upcoming Contract Payment size: " + l.size());
        if (l.isEmpty()) return null;

        List <Long> cpIds = new ArrayList<>();
        String date = new LocalDate(l.get(0)[1]).toString("yyyy-MM-dd");
        for(Object[] cp : l) {
            if(!date.equals(new LocalDate(cp[1]).toString("yyyy-MM-dd"))) break;
            cpIds.add(Long.parseLong(cp[0].toString()));
        }
        List<ContractPayment> contractPayments = contractPaymentRepository.findAll(cpIds);

        logger.info("contractPayments size: " + contractPayments.size());
        return contractPayments;
    }

    public void paymentReceived(ContractPaymentConfirmationToDo t) {

        ContractPaymentTerm cpt = t.getContractPaymentTerm();
        if (t.getSource().equals(INCOMPLETE_FLOW_MISSING_BANK_INFO)) {

            incompleteDirectDebitService.stopMissingBankInfoFlow(cpt.getContract());

        } else if (t.getRelatedEntityType() != null && t.getRelatedEntityType().equals("DirectDebit")) {
            List<PushNotification> notifications = Setup.getRepository(DisablePushNotificationRepository.class)
                    .findActiveNotificationsByOwner(t.getRelatedEntityId(), t.getRelatedEntityType());

            logger.info("Found notifications: " + notifications.size());
            pushNotificationHelper.stopDisplaying(notifications);

            if (paymentRepository.existsByDirectDebitIdAndStatus(t.getRelatedEntityId(), PaymentStatus.BOUNCED)) {
                logger.info("related to bounced payment -> existing");
                return;
            }
        }

        boolean isMonthly = t.getContractPaymentList().stream().anyMatch(p -> PaymentHelper.isMonthlyPayment(p.getPaymentType()));
        boolean hasConfirmedDdb = Setup.getApplicationContext().getBean(DirectDebitService.class)
                .hasConfirmedDdb(cpt.getContract(),
                        contractPaymentConfirmationToDoService.getUpcomingMonthlyPaymentDatePerTodo(t));
        boolean switchToPayingCC = isMonthly && !hasConfirmedDdb;

        List<DirectDebit> dds;
        switch (t.getSource()) {
            case INCOMPLETE_FLOW_MISSING_BANK_INFO:
            case INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
                 dds = queryService.getAllDdByRelatedFlow(t.getSource(), null, cpt.getId(), Collections.singletonList(DirectDebitStatus.IN_COMPLETE));
                break;
            case DD_REJECTED:
                dds = queryService.getAllDdByRelatedFlow(t.getSource(), true, cpt.getId(), DirectDebitService.activeWithoutConfirmedStatuses);
                logger.info("DDs related rejection due bouncing size: " + dds.size());

                dds.addAll(queryService.getAllDdByRelatedFlow(t.getSource(), false, cpt.getId(), DirectDebitService.activeWithoutConfirmedStatuses));
                break;
            default:
                return;
        }

        logger.info("dds size: " + dds.size());
        dds.forEach(ddId -> {
            logger.info("dd id: " + ddId);
        });

        // if switchToPayingCC ==> Then Generation NonMonthly DDs Else ==> Generation Monthly and NonMonthly DDs
        createBGTforGenerationPostponeDDs(cpt,
                dds.stream()
                        .filter(dd -> dd.getContractPayments() != null && !dd.getContractPayments().isEmpty() &&
                                (!switchToPayingCC || !PaymentHelper.isMonthlyPayment(dd.getPaymentType())))
                        .map(dd -> {
                            Map<String, String> map =  new HashMap<>();
                            map.put("id", dd.getId().toString());
                            map.put("status", dd.getCategory().equals(DirectDebitCategory.B) ? dd.getStatus().toString() : dd.getMStatus().toString());
                            return map;
                        })
                        .collect(Collectors.toList()));

        // Cancel All DDs
        Setup.getApplicationContext()
                .getBean(DirectDebitCancellationService.class)
                .cancelDdsAndStopRejectionFlow(switchToPayingCC ? DirectDebitCancellationToDoReason.CLIENT_PAYING_VIA_Credit_Card_FLOW :
                        DirectDebitCancellationToDoReason.CONTRACT_ADJUSTED_END_DATE_UPDATED, dds);

        if (!switchToPayingCC) return;

        startPayingViaCreditCardFlow(cpt);
    }

    public void createBGTforGenerationPostponeDDs(ContractPaymentTerm cpt, List<Map<String, String>> dds){

        logger.info("dds size: " + dds.size());
        if(dds.isEmpty()) return;

        backgroundTaskService.create(new BackgroundTask.builder(
                "generationPostponeDDs_" + cpt.getId(),
                "accounting",
                "creditCardOfferService",
                "generationPostponeDDs")
                .withParameters(
                        new Class[] { Long.class, List.class },
                        new Object[] { cpt.getId(), dds})
                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                .withDelay(2 * 60 * 1000L)
                .build());
    }

    public void generationPostponeDDs(Long cptId, List<Map<String, String>> ddsMap){

        logger.info("ContractPaymentTerm id: " + cptId);

        ContractPaymentTerm cpt = Setup.getRepository(ContractPaymentTermRepository.class).findOne(cptId);
        if(cpt == null) return;

        List<DirectDebit> dds = directDebitRepository.findAll(ddsMap.stream().map(m -> Long.parseLong(m.get("id"))).collect(Collectors.toList()));
        if(dds.isEmpty()) return;

        logger.info("Contract PaidEndDate : " + cpt.getContract().getPaidEndDate());
        DirectDebitGenerationPlanService directDebitGenerationPlanService = Setup.getApplicationContext().getBean(DirectDebitGenerationPlanService.class);

        List<DirectDebitGenerationPlan> newPlans = directDebitGenerationPlanService.generatePlansAfterCancelPaymentDdWithoutSave(
                cpt, new DateTime(cpt.getContract().getPaidEndDate()), dds,
                CreditCardOfferProperties.getX_DaysBeforeDdStartDate())
                .stream()
                // filter just Validated Plans
                .filter(plan -> !directDebitGenerationPlanService.existsPaymentReceivedOrCoveredContractPayment(
                        plan.getContract(), plan.getPaymentType().getCode(),
                        new LocalDate(plan.getDDSendDate()).dayOfMonth().withMinimumValue().toDate(),
                        new LocalDate(plan.getDDSendDate()).dayOfMonth().withMaximumValue().toDate()))
                .collect(Collectors.toList());

        logger.info("newPlans size : " + newPlans.size());
        directDebitGenerationPlanService.saveAllGeneratedPlans(newPlans, ddsMap);
    }

    public void startPayingViaCreditCardFlow(ContractPaymentTerm cpt) {
        logger.info("cpt id: " + cpt.getId());

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePayingViaCreditCardFlag(cpt.getContract(), true);

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "startPayingViaCreditCardFlow_" + cpt.getId(),
                        "accounting",
                        "creditCardOfferService",
                        "startPayingViaCreditCardFlow")
                        .withRelatedEntity("ContractPaymentTerm", cpt.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {cpt.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(60L * 1000L)
                        .build());
    }

    public void startPayingViaCreditCardFlow(Long cptId) throws Exception {
        logger.info("cpt id: " + cptId);

        ContractPaymentTerm cpt = Setup.getRepository(ContractPaymentTermRepository.class)
                .findOne(cptId);

        List<DirectDebit> dds = directDebitRepository.getAllDDsByContractPaymentTermAndStatus(
                cpt, DirectDebitService.notAllowedStatuses);
        // Cancel All DDs
        Setup.getApplicationContext()
                .getBean(DirectDebitCancellationService.class)
                .cancelDdsAndStopRejectionFlow(DirectDebitCancellationToDoReason.CLIENT_PAYING_VIA_Credit_Card_FLOW, dds);

        List<ContractPayment> l = Setup.getApplicationContext().getBean(ContractPaymentService.class)
                .getUniqueAndSortedPayments(
                        cpt.getContract(),
                        contractPaymentService.getUnReceivedContractPayment(cpt, false),
                        null);

        incompleteDirectDebitService.stopMissingBankInfoFlow(cpt.getContract());

        l = l.stream().filter(c -> !PaymentHelper.isMonthlyPayment(c.getPaymentType())).collect(Collectors.toList());

        if (l.isEmpty()) return;

        unpaidOnlineCreditCardPaymentService.createConfirmationTodoFromContractPaymentsAndStartReminderFlow(l, cpt);
    }
}