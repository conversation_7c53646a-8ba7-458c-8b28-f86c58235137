package com.magnamedia.report;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.helper.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @param <S>
 * <AUTHOR>
 */
public interface TemplatedReport<S extends BaseEntity> {

    static String format(Date d) {
        return DateUtil.formatFullDate(d);
    }

    static String format(Double salary) {
        return String.format("%.2f", salary == null ? 0.0 : salary);
    }

    List<S> getData();

    void refreshData();

    default String SendEmail() {
        throw new UnsupportedOperationException();
    }

    default String SendEmail(List<EmailRecipient> receps) {
        throw new UnsupportedOperationException();

    }

    default String getForQuery() {
        StringBuilder res = new StringBuilder("");

        res.append("(");
        if (!getData().isEmpty()) {
            for (int i = 0; i < getData().size() - 1; i++) {
                res.append(getData().get(i).getId()).append(", ");
            }

            res.append(getData().get(getData().size() - 1).getId());
        }
        res.append(")");
        res.append("#");
        res.append(getTitle());
        return res.toString();
    }

    String[] toStringArray(S obj);

    String getTitle();

    default List<String[]> getTable() {
        return getData().stream().map((c) -> {
            return toStringArray(c);
        }).collect(Collectors.toList());
    }

    String[] getHeaders();

    default boolean isEmpty() {
        return getData().isEmpty();
    }
}
