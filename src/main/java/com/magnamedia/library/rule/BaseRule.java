/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * Base Rule should implement applyGeneralRule and other meta-data methods
 * <AUTHOR>
 * @param <T>
 */
@Component
public interface BaseRule<T> {

    /**
     * 
     * @return Base Rule's name should be specified
     */
    String getName();

    /**
     * 
     * @return Base Rule's Description
     */
    String getDescription();

    /**
     *
     * @return Base Rule's Creator name
     */
    String getCreator();

    /**
     * Base Rule's code should be unique among rules of same annotation type
     * @return Base Rule's Code
     */
    String getCode();
    
    @JsonIgnore
    default Class getProjectionClass(){
        return null;
    }

    /**
     * General rule is the rule that gets a list of all the items that meet the Base Rule
     * Other methods should be present with the annotation @SubRule in order to filter the General Rule's result
     * @return a List of elements resulted after applying general rule
     */
    public List<T> applyGeneralRule();

    /**
     * A sub-rule is defined by a function with annotation type @SubRule
     * Any function with @SubRule annotation should return List of T and should accept 2 parameters: List of T input, Boolean withUpdate
     * @return a list of ordered sub-rules
     */
    public default List<SubRule> getOrderedSubRules() {
        List<SubRule> finalResult = new ArrayList<>();

        Class<?> obj = this.getClass();
        Map<Integer, Method> orderedMethods = new HashMap<>();
        for (Method method : obj.getDeclaredMethods()) {
            if (!method.isAnnotationPresent(SubRule.class)) {
                continue;
            }

            SubRule subRule = (SubRule) method.getAnnotation(SubRule.class);
            orderedMethods.put(subRule.priority(), method);
        }
        for (Integer i : orderedMethods.keySet()) {
            Method method = orderedMethods.get(i);
            SubRule subRule = (SubRule) method.getAnnotation(SubRule.class);
            finalResult.add(subRule);
        }
        return finalResult;
    }

    /**
     *
     * @param withUpdate: whether to update the results of sub-rules
     * @param codeFilter: only applies the sub-rules with specified code
     * @return a SubRulesResultSet that contains the result of applying sub-rules
     */
    public default SubRulesResultSet<T> applySubRules(Boolean withUpdate, String codeFilter) {
        List<SubRuleResult<T>> finalResult = new ArrayList<>();
        List<T> generalRuleResult = applyGeneralRule();
        if (generalRuleResult == null || generalRuleResult.isEmpty()) {
            return new SubRulesResultSet(finalResult, generalRuleResult);
        }

        Class<?> obj = this.getClass();
        Map<Integer, Method> orderedMethods = new HashMap<>();
        for (Method method : obj.getDeclaredMethods()) {
            if (!method.isAnnotationPresent(SubRule.class)) {
                continue;
            }
            
            SubRule subRule = (SubRule) method.getAnnotation(SubRule.class);
            if(codeFilter!=null && subRule.code()!=null && !subRule.code().equals(codeFilter))
                continue;
            orderedMethods.put(subRule.priority(), method);
        }
        for (Integer i : orderedMethods.keySet()) {
            Method method = orderedMethods.get(i);
            SubRule subRule = (SubRule) method.getAnnotation(SubRule.class);

            try {
                List<T> subRuleResult = (List<T>) method.invoke(this, generalRuleResult, withUpdate);
                if (subRuleResult == null) {
                    continue;
                }
                finalResult.add(new SubRuleResult<>(subRule, subRuleResult, withUpdate, getProjectionClass()));
                generalRuleResult.removeAll(subRuleResult);
            } catch (IllegalAccessException | IllegalArgumentException ex) {
                System.err.println("Exception in applySubRules");                
                System.err.println(ex);
            }catch(InvocationTargetException ex){
                System.err.println("InvocationTargetException Exception in applySubRules");                
                System.err.println(ex);
                if(ex.getTargetException() instanceof RuntimeException)
                    throw (RuntimeException)ex.getTargetException();
            }
        }

        return new SubRulesResultSet(finalResult, generalRuleResult);
    }

    /**
     * Applies all Subrules, without update and without codeFilter
     * @return a SubRulesResultSet that contains the result of applying sub-rules
     */
    public default SubRulesResultSet<T> applySubRules() {
        return applySubRules(false, null);
    }
}
