package com.magnamedia.businessrule;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.*;
import com.magnamedia.service.payment.PaymentDeletionRules;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.LocalDate;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.module.AccountingModule.PARAMETER_PRE_COLLECTED_SALARY_PAYMENT_REQUEST_PURPOSE;
import static com.magnamedia.module.AccountingModule.PARAMETER_PRE_COLLECTED_SALARY_PAYMENT_WITHOUT_VAT_REQUEST_PURPOSE;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jun 21, 2021
 *         Jirra ACC-3546
 */
// check if the payment replaced by another payment
// handle by switch bank account flow
// handle by OEC flow
// if contract cancelled within first x days -> set status deleted and cancel dds
// hide dd generation plan notification
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterCreate, BusinessEvent.AfterUpdate},
        fields = { "id", "amountOfPayment", "typeOfPayment.id", "dateOfPayment", "methodOfPayment",
                "directDebit.id", "status", "replaced", "isProRated", "contract.id", "trials",
                "sBABPBrChecked" })
public class BouncedPaymentBR implements BusinessAction<Payment> {

    private static final Logger logger = Logger.getLogger(BouncedPaymentBR.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.info("Validation");
        logger.info("payment id: " + (entity.isNewInstance() ?
                "new instance" : entity.getId()));

        if (entity.issBABPBrChecked()) return false;
        if (entity.isReplaced()) return false;

        boolean bouncedFirstTime = PaymentHelper.isChangedToBounced(entity);
        if(bouncedFirstTime) {
            logger.info("Bounced for the First Time");
            return true;
        }

        boolean bouncedAgain = PaymentHelper.isBouncedAgain(entity);
        logger.info("Bounced Again: " + bouncedAgain);

        return bouncedAgain;
    }

    @Override
    public Map execute(Payment entity, BusinessEvent even) {
        logger.info("Executing");
        Map map = new HashMap();

        // ACC-3968 if payment is bounced
        if(Setup.getApplicationContext().getBean(PaymentService.class)
                .replacementPaymentUpdated(entity)){

            map.put("replaced", true);
            entity.setReplaced(true);
            map.put("bouncedFlowPausedForReplacement", true);
        }
        
        // old logic before ACC-3968 
        map.put("sBABPBrChecked", true);

        checkSwitchingBankAccount(entity);
        checkOECFlow(entity);
        map.putAll(checkIfContractCancelledWithinFirstXDays(entity));

        Setup.getApplicationContext()
                .getBean(DirectDebitGenerationPlanService.class)
                .hideGenerationPlanPushNotification(entity);

        map.putAll(Setup.getApplicationContext()
                .getBean(PaymentDeletionRules.class)
                .handlePayment(entity));

        if (entity.hasBaseAdditionalInfo("waitingBankResponseForPreCollectedSalary")) {

            ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
            Contract c = contractRepository.findOne(entity.getContract().getId());
            ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();

            Setup.getApplicationContext()
                    .getBean(MaidVisaFailedMedicalCheckService.class)
                    .processUnFlagContractAsPreCollectedCurrentMonth(cpt,
                            new LocalDate(),
                            new LocalDate(Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class)
                                    .getDiscountStartDateInMillis(cpt)), null);
        }

        return map;
    }

    // ACC-3546
    public void checkSwitchingBankAccount(Payment entity) {
        Setup.getApplicationContext().getBean(SwitchingBankAccountService.class)
                .handleReceivingPaymentStatus(entity);
    }

    
    // ACC-3974
    public void checkOECFlow(Payment entity) {
        Setup.getApplicationContext().getBean(OecAmendDDsService.class)
                .handleReceivingOldPaymentStatus(entity);
    }

    // ACC-3555
    public Map checkIfContractCancelledWithinFirstXDays(Payment entity) {
        Map map = new HashMap();
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        logger.info("Contract Prospect Type: " + contract.getContractProspectType().getCode());
        if (contract.isCancelledWithinFirstXDays()) {
            logger.info("Contract is Cancelled within First X Days -> delete the Payment");
            map.put("status", PaymentStatus.DELETED.getValue());
            
            Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class)
                    .cancelAllContractDDs(contract);
        }

        return map;
    }
}