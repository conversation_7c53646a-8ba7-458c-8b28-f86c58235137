//package com.magnamedia.businessrule;
//
//import com.magnamedia.core.Setup;
//import com.magnamedia.core.annotation.BusinessRule;
//import com.magnamedia.core.helper.SelectQuery;
//import com.magnamedia.core.imc.BusinessAction;
//import com.magnamedia.core.imc.InterModuleConnector;
//import com.magnamedia.core.type.BusinessEvent;
//import com.magnamedia.entity.Contract;
//import com.magnamedia.entity.ContractPaymentTerm;
//import com.magnamedia.entity.DDMessaging;
//import com.magnamedia.entity.DirectDebit;
//import com.magnamedia.module.AccountingModule;
//import com.magnamedia.module.type.DDMessagingType;
//import com.magnamedia.module.type.DirectDebitDataEntryRejectCategory;
//import com.magnamedia.module.type.DirectDebitMessagingScheduleTermCategory;
//import com.magnamedia.repository.ContractRepository;
//import com.magnamedia.repository.DirectDebitRepository;
//import com.magnamedia.service.DDMessagingService;
//import org.joda.time.LocalDate;
//
//import java.util.*;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
///**
// * <AUTHOR> <<EMAIL>>
// *         Created on July 5, 2020
// *         Jirra ACC-2024
// */
//@BusinessRule(moduleCode = "accounting", entity = DirectDebit.class, events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
//        fields = {"id", "contractScheduleDateOfTermination", "contractPaymentTerm.id", "contractPaymentTerm.contract.id", "contractPaymentTerm.isEidRejected",
//                "contractPaymentTerm.isIBANRejected", "contractPaymentTerm.isAccountHolderRejected",
//                "rejectCategory",
//                "inCompleteDDTrials", "inCompleteDDReminder", "sendIncompleteDDInfoMessages", "ddBankInfoGroup"})
//public class ClientDidNotSignedMessagesBR implements BusinessAction<DirectDebit> {
//    private static final Logger logger = Logger.getLogger(ClientDidNotSignedMessagesBR.class.getName());
//
//    @Override
//    public boolean validate(DirectDebit entity, BusinessEvent event) {;
//        logger.info("validation DD id " + entity.getId() +
//                "; sendIncompleteDDInfoMessages " + entity.isSendIncompleteDDInfoMessages() +
//                "; DD trials " + entity.getInCompleteDDTrials() +
//                "; DD reminder " + entity.getInCompleteDDReminder());
//
//        return entity.isSendIncompleteDDInfoMessages();
//    }
//
//    @Override
//    public Map execute(DirectDebit entity, BusinessEvent event) {
//        logger.info("execute starts");
//
//        Contract contract = Setup.getRepository(ContractRepository.class)
//                .findOne(entity.getContractPaymentTerm().getContract().getId());
//
//        DirectDebitMessagingScheduleTermCategory scheduleTermCategory = null;
//        if (entity.getContractScheduleDateOfTermination() != null) {
//            Date scheduledDateOfTermination = entity.getContractScheduleDateOfTermination();
//            LocalDate localDate = new LocalDate(scheduledDateOfTermination);
//            if (localDate.getYear() == new LocalDate().getYear()
//                    && localDate.getDayOfYear() == new LocalDate().getDayOfYear()) {
//                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.EToday;
//            } else {
//                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.GToday;
//            }
//        } else {
//            scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.None;
//        }
//
//        logger.info("execute scheduleTermCategory selected: " + scheduleTermCategory);
//
//
//        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
//        query.filterBy("isActive", "=", true);
//        query.filterBy("event", "=", DDMessagingType.IncompleteDDRejectedByDataEntry);
//        query.filterBy("trials", "like", "%" + entity.getInCompleteDDTrials() + "%");
//        query.filterBy("reminders", "like", "%" + entity.getInCompleteDDReminder() + "%");
//        query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");
//
//        ContractPaymentTerm cpt = entity.getContractPaymentTerm();
//        DirectDebitDataEntryRejectCategory ddDataEntryRejection = null;
//
//        logger.info("getIsAccountHolderRejected: " + cpt.getIsAccountHolderRejected());
//        logger.info("getIsIBANRejected: " + cpt.getIsIBANRejected());
//        logger.info("getIsEidRejected: " + cpt.getIsEidRejected());
//
//        if (cpt.getIsAccountHolderRejected() && cpt.getIsIBANRejected() && cpt.getIsEidRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.AllDataIncorrect;
//        } else if (cpt.getIsIBANRejected() && cpt.getIsEidRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongEIDAndIBAN;
//        } else if (cpt.getIsEidRejected() && cpt.getIsAccountHolderRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongEIDAndAccountName;
//        } else if (cpt.getIsIBANRejected() && cpt.getIsAccountHolderRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongIBANAndAccountName;
//        } else if (cpt.getIsAccountHolderRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongAccountName;
//        } else if (cpt.getIsIBANRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongIBAN;
//        } else if (cpt.getIsEidRejected()) {
//            ddDataEntryRejection = DirectDebitDataEntryRejectCategory.WrongEID;
//        }
//
//        logger.info("execute dataEntryRejectCategory selected: " + ddDataEntryRejection);
//
//        if (ddDataEntryRejection == null) {
//            logger.info("execute dataEntryRejectCategory is null so we can't process");
//            return null;
//        }
//
//        query.filterBy("dataEntryRejectCategory", "=", ddDataEntryRejection);
//        query.setLimit(1);
//
//        DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory);
//
//        Integer inCompleteDDsInfoMaxReminder = Integer.valueOf(Setup.getParameter(
//                Setup.getCurrentModule(), AccountingModule.PARAMETER_IN_COMPLETE_DD_MAX_REMINDER));
//
//        if (ddMessaging != null) {
//            logger.info("execute message founded :" + ddMessaging.getId());
//            Setup.getApplicationContext().getBean(DDMessagingService.class)
//                    .applyDdMessagingBGT(contract, ddMessaging, entity, null);
//
//            List<DirectDebit> dds = entity.getDdBankInfoGroup() == null ?
//                    new ArrayList() {{ add(entity); }} :
//                    Setup.getRepository(DirectDebitRepository.class)
//                            .getByContractPaymentTermAndDdBankInfoGroup(cpt, entity.getDdBankInfoGroup());
//
//            for (int i = 0; i < dds.size(); i++) {
//                if (dds.get(i).getRejectCategory() != null) { // rejected dd that require user action is rejection flow not incomplete flow
//                    logger.info("execute dd is related to rejection flow");
//                    continue;
//                }
//
//                if (entity.getInCompleteDDReminder() >= inCompleteDDsInfoMaxReminder) {
//                    logger.info("execute dd reminder is equals or grater than max");
//                    continue;
//                }
//
//                Map body = new HashMap();
//                body.put("id", dds.get(i).getId());
//                body.put("inCompleteDDReminder", entity.getInCompleteDDReminder() + 1);
//
//                Setup.getApplicationContext().getBean(InterModuleConnector.class)
//                        .postJsonAsync("accounting/directDebit/update", body);
//            }
//        } else {
//            logger.log(Level.SEVERE, "execute no message found");
//        }
//
//        logger.log(Level.SEVERE, "execute execution ends");
//
//        Map<String, Object> map = new HashMap();
//        map.put("sendIncompleteDDInfoMessages", false);
//        return map;
//    }
//}