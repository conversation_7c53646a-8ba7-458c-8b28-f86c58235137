package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.PaymentService;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         Created on 13-4-2020
 *         Jirra ACC-1611
 */

@BusinessRule(moduleCode = "", entity = DirectDebitRejectionToDo.class,
        events = {BusinessEvent.BeforeUpdate, BusinessEvent.AfterCreate},
        fields = { "id", "trials", "dontSendDdMessage", "leadingRejectionFlow",
                "reminder", "manualDDBTrials", "autoDDBTrials", "ddCategory", "lastDirectDebit.id",
                "contractScheduleDateOfTermination", "reSignTrials", "sameSignTrials", "lastRejectCategory", "cancellationReasonCode"})
public class DirectDebitRejectionTodoBusinessRule implements BusinessAction<DirectDebitRejectionToDo> {
    private static final Logger logger = Logger.getLogger(DirectDebitRejectionTodoBusinessRule.class.getName());

    @Override
    public boolean validate(DirectDebitRejectionToDo entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "validation");

        DirectDebit lastDD = entity.getLastDirectDebit();
        if (lastDD == null) {
            logger.log(Level.SEVERE, "with no getLastDirectDebit");
            return false;
        }

        lastDD = Setup.getRepository(DirectDebitRepository.class).findOne(lastDD.getId());
        if (lastDD == null ||
                lastDD.getContractPaymentTerm().getContract().getStatus() == ContractStatus.CANCELLED ||
                lastDD.getContractPaymentTerm().getContract().getStatus() == ContractStatus.EXPIRED) {

            logger.log(Level.SEVERE, "last dd null or contract cancelled ");
            return false;
        }
        //ACC-4715
        if (lastDD.getContractPaymentTerm().getContract().isPayingViaCreditCard()
            && (entity.getLastRejectCategory() != null
            && entity.getLastRejectCategory().equals(DirectDebitRejectCategory.Signature)
            || (lastDD.getBouncingRejectCategory() != null && lastDD.getBouncingRejectCategory().equals(DirectDebitRejectCategory.Signature)))) {
            logger.log(Level.SEVERE, "DirectDebitRejectionTodoBusinessRule contract isPayingViaCreditCard");
            return false;
        }

        logger.log(Level.SEVERE, "id " + entity.getId() +
                "; event " + event.toString() +
                "; trials: " + entity.getTrials() +
                "; ReSignTrials: " + entity.getReSignTrials() +
                "; SameSignTrials: " + entity.getSameSignTrials() +
                "; reminders: " + entity.getReminder()+
                "; DD category: " + entity.getDdCategory() +
                "; DD reject category: " + lastDD.getRejectCategory() +
                "; todo reject category: " + entity.getLastRejectCategory() +
                "; todo schedule date: " + entity.getContractScheduleDateOfTermination() +
                "; last master dd: " + lastDD.getId());

        if (event == BusinessEvent.AfterCreate) return true;

        HistorySelectQuery<DirectDebitRejectionToDo> query = new HistorySelectQuery<>(DirectDebitRejectionToDo.class);
        query.filterBy("id", "=", entity.getId());
        query.sortBy("lastModificationDate", false);
        query.setLimit(1);

        List<DirectDebitRejectionToDo> dd = query.execute();
        if (dd.isEmpty()) return true;

        // if any number has increased then we should fire the event
        DirectDebitRejectionToDo old = dd.get(0);

        logger.log(Level.SEVERE, "old reminder " + old.getReminder() +
                "; new reminder " + entity.getReminder() +
                "; old trials " + old.getTrials() +
                "; new trials " + entity.getTrials() +
                "; ReSignTrials " + old.getReSignTrials() + "" +
                "; new ReSignTrials " + entity.getReSignTrials());

        return entity.getReminder() > old.getReminder() ||
                entity.getTrials() > old.getTrials() ||
                entity.getReSignTrials() > old.getReSignTrials();
    }

    @Override
    public Map<String, Object> execute(DirectDebitRejectionToDo entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "start execute");

        DirectDebit lastDD = entity.getLastDirectDebit();
        if (lastDD == null) {
            logger.log(Level.SEVERE, "execute with no getLastDirectDebit");
            return null;
        }

        return handleMessages(entity, Setup.getRepository(DirectDebitRepository.class).findOne(lastDD.getId()));
    }

    private Map<String, Object> handleMessages(
            DirectDebitRejectionToDo entity,
            DirectDebit lastDD) {
        Map<String, Object> r = new HashMap<>();
        logger.info("handleMessages");

        if (entity.getDontSendDdMessage()) {// if true then don't send msg else if false then send msg
            logger.log(Level.SEVERE, "dont send message is true");
            return r;
        }
        
        if (!entity.getLeadingRejectionFlow()) {
            DirectDebitRejectionToDo todo = Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                    .findOne(entity.getId());
            
            if (Setup.getApplicationContext()
                    .getBean(DirectDebitRejectionFlowService.class)
                    .isWaitingClientSignature(todo)) {
            
                logger.log(Level.SEVERE, "not leading signature rejection flow");
                return r;
            }
        }

        Contract contract = lastDD.getContractPaymentTerm().getContract();
        DirectDebitMessagingScheduleTermCategory scheduleTermCategory;
        
        if (entity.getContractScheduleDateOfTermination() != null) {
            Date scheduledDateOfTermination = entity.getContractScheduleDateOfTermination();
            LocalDate localDate = new LocalDate(scheduledDateOfTermination);
            if (localDate.getYear() == new LocalDate().getYear()
                    && localDate.getDayOfYear() == new LocalDate().getDayOfYear()) {
                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.EToday;
            } else {
                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.GToday;
            }
        } else {
            scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.None;
        }

        String contractType =  contract.getContractProspectType().getCode();
        boolean isMV_SDR = DDMessagingService.isContractMvSdr(contract);
        if (isMV_SDR) {
            contractType = DDMessagingContractType.MAID_VISA_SDR.getLabel();
        }
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("isActive", "=", true);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        query.filterBy("reminders", "like", "%" + entity.getReminder() + "%");
        query.filterBy("ddCategory", "like", "%" + lastDD.getCategory().toString() + "%");
        query.filterBy("rejectCategory", "=", entity.getLastRejectCategory());
        query.filterBy("contractProspectTypes", "like", "%" + contractType + "%");

        if (entity.getLastRejectCategory() == DirectDebitRejectCategory.Signature) {
            int reSignTrial = entity.getReSignTrials();
            Map<String, Object> m = checkSigningPaperMode(entity, lastDD);
            if (m.containsKey("paperModeThreshold")) {
                reSignTrial = Math.max(reSignTrial, (int) m.get("paperModeThreshold"));
                r.put("reSignTrials", reSignTrial);
            }

            query.filterBy("trials", "like", "%" + reSignTrial + "%");
        } else {
            query.filterBy("trials", "like", "%" + entity.getTrials() + "%");
        }

        // there is no termination message for MV required
        query.setLimit(1);

        DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory,
                DDMessagingType.DirectDebitRejected.isSendPayTabsWithTermination());

        if (ddMessaging == null) {
            logger.info("no dd message setup found");
            return r;
        }

        logger.info("dd message setup found with id: " + ddMessaging.getId());

        // ACC-8851 The termination message will be delayed until the PED date or on the third day after the flow is initiated.
        DDMessagingService ddMessagingService = Setup.getApplicationContext().getBean(DDMessagingService.class);
        if (ddMessagingService.shouldDelayTerminationMessageBeforePaidEndDate(
                DDMessagingType.DirectDebitRejected, ddMessaging, lastDD.getContractPaymentTerm(), isMV_SDR)) {
            logger.info("Flow delay sending the termination message before paid end date -> createToDo sms scheduled");
            Setup.getApplicationContext()
                    .getBean(DirectDebitRejectionFlowService.class)
                    .handleDelayTerminationMessageBeforePaidEndDate(ddMessaging, lastDD, entity);
            return r;
        }

        ddMessagingService.applyDdMessagingBGT(contract, ddMessaging, lastDD, entity);

        return r;
    }

    private Map<String, Object> checkSigningPaperMode(DirectDebitRejectionToDo entity, DirectDebit lastDD) {
        if (!DirectDebitRejectCategory.Signature.equals(entity.getLastRejectCategory())) return new HashMap<>();

        logger.info("checkSigningPaperMode");

        int paperModeThreshold = Integer.parseInt(
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL));

        Map<String, Object> m = new HashMap<>();
        m.put("paperModeThreshold", paperModeThreshold);
        if (lastDD.getContractPaymentTerm().getContract().isSigningPaperMode()) {
            return m;
        }

        if (entity.getReSignTrials() < paperModeThreshold) return new HashMap<>();

        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaperModeAsync(lastDD.getContractPaymentTerm(), true);

        return m;
    }
}