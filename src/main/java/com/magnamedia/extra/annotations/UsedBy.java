package com.magnamedia.extra.annotations;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.SOURCE;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @created 24/10/2024 - 6:13 PM
 */
@Retention(SOURCE)
@Target({METHOD, FIELD})
public @interface UsedBy {
    Modules[] modules() default Modules.None;

    Others others() default Others.None;

    enum Modules {
        None,
        Recruitment,
        Visa,
        Sales,
        Client_Management,
        Staff_Management,
        Payroll
    }

    enum Others {
        None,
        New_GPT,
        N8N,
        CC_App,
        Erp,
        Rpa
    }
}