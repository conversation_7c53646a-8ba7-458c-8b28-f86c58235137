package com.magnamedia.extra;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;

/**
 *
 * <AUTHOR>
 */
public class UserLibrary {

    public static Boolean userHasPositionCode(User user, String positionCode) {        
        return user!=null && user.hasPosition(positionCode);
    }

    public static Boolean currentUserHasPositionCode(String positionCode) {
        User loggeduser = CurrentRequest.getUser();        
        return userHasPositionCode(loggeduser, positionCode);
    }
}
