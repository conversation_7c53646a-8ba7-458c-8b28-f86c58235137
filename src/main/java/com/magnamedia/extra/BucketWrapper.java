package com.magnamedia.extra;

import com.magnamedia.module.type.BucketType;
import com.magnamedia.module.type.WealthBucketType;

/**
 * Created by <PERSON><PERSON>.Masod on 4/13/2021.
 */
public class BucketWrapper {
    private String nameOfBucket;
    private String code;
    private Long bucketHolderId;
    private String bucketType;
    private Double initialBalance;
    private String wealthBucketType;
    private Boolean activateAutoReplenishment;
    private Double replenishmentBalance;
    private Double balanceAfterReplenishment;
    private String refillFromWhichBucketCode;
    private Boolean useTransguardService;

    public String getNameOfBucket() {
        return nameOfBucket;
    }

    public void setNameOfBucket(String nameOfBucket) {
        this.nameOfBucket = nameOfBucket;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getBucketHolderId() {
        return bucketHolderId;
    }

    public void setBucketHolderId(Long bucketHolderId) {
        this.bucketHolderId = bucketHolderId;
    }

    public String getBucketType() {
        return bucketType;
    }

    public BucketType getBucketTypeAsEnum() {
        if (bucketType == null) return null;

        return BucketType.valueOf(bucketType);
    }

    public void setBucketType(String bucketType) {
        this.bucketType = bucketType;
    }

    public Double getInitialBalance() {
        return initialBalance;
    }

    public void setInitialBalance(Double initialBalance) {
        this.initialBalance = initialBalance;
    }

    public String getWealthBucketType() {
        return wealthBucketType;
    }


    public WealthBucketType getWealthBucketTypeAsEnum() {
        if (wealthBucketType == null) return null;

        return WealthBucketType.valueOf(wealthBucketType);
    }

    public void setWealthBucketType(String wealthBucketType) {
        this.wealthBucketType = wealthBucketType;
    }

    public Boolean getActivateAutoReplenishment() {
        return activateAutoReplenishment;
    }

    public void setActivateAutoReplenishment(Boolean activateAutoReplenishment) {
        this.activateAutoReplenishment = activateAutoReplenishment;
    }

    public Double getReplenishmentBalance() {
        return replenishmentBalance;
    }

    public void setReplenishmentBalance(Double replenishmentBalance) {
        this.replenishmentBalance = replenishmentBalance;
    }

    public Double getBalanceAfterReplenishment() {
        return balanceAfterReplenishment;
    }

    public void setBalanceAfterReplenishment(Double balanceAfterReplenishment) {
        this.balanceAfterReplenishment = balanceAfterReplenishment;
    }

    public String getRefillFromWhichBucketCode() {
        return refillFromWhichBucketCode;
    }

    public void setRefillFromWhichBucketCode(String refillFromWhichBucketCode) {
        this.refillFromWhichBucketCode = refillFromWhichBucketCode;
    }

    public Boolean getUseTransguardService() {
        return useTransguardService;
    }

    public void setUseTransguardService(Boolean useTransguardService) {
        this.useTransguardService = useTransguardService;
    }
}
