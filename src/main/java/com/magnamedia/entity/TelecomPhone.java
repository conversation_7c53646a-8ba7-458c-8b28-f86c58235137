package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;

import java.sql.Date;
import java.util.Comparator;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Transient;

import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 1, 2018
 */
@Entity
public class TelecomPhone extends BaseEntity {

    @Column(columnDefinition = "boolean default true")
    private Boolean active = true;

    @Column(columnDefinition = "boolean default false")
    private Boolean deleted = false;

    @Column
    private String name;

    @Label
    private String number;


    @ManyToOne
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Expense primaryExpense;

    @ManyToOne
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Expense secondryExpense;

    @Column
    private Date dueEvery;

//    @ManyToMany
//    @JoinTable(name = "PHONE_HOLDERS", 
//         joinColumns = { @JoinColumn(name = "PHONE_ID") }, 
//         inverseJoinColumns = { @JoinColumn(name = "PICKLISTITEM_ID") })
//    private Set<PicklistItem> holders;

    @Column
    private String holders;

    @Column
    private String usageText;

    @Lob
    private String notes;

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "phone", fetch = FetchType.LAZY)
    private List<TelecomPhoneBill> telecomPhoneBills;

    @Transient
    private TelecomPhoneBill lastTelecomPhoneBill;

    @ManyToOne
    private PicklistItem serviceType;

    @ManyToOne
    private PicklistItem paymentMethod;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Expense getPrimaryExpense() {
        return primaryExpense;
    }

    public void setPrimaryExpense(Expense primaryExpense) {
        this.primaryExpense = primaryExpense;
    }

    public Expense getSecondryExpense() {
        return secondryExpense;
    }

    public void setSecondryExpense(Expense secondryExpense) {
        this.secondryExpense = secondryExpense;
    }

    public Date getDueEvery() {
        return dueEvery;
    }

    public void setDueEvery(Date dueEvery) {
        this.dueEvery = dueEvery;
    }

    public String getHolders() {
        return holders;
    }

    public void setHolders(String holders) {
        this.holders = holders;
    }

//    public Set<PicklistItem> getHolders() {
//        return holders;
//    }
//    
//    public void setHolders(Set<PicklistItem> holders) {
//        this.holders = holders;
//    }

    public String getUsageText() {
        return usageText;
    }

    public void setUsageText(String usageText) {
        this.usageText = usageText;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<TelecomPhoneBill> getTelecomPhoneBills() {
        return telecomPhoneBills;
    }

    public void setTelecomPhoneBills(List<TelecomPhoneBill> telecomPhoneBills) {
        this.telecomPhoneBills = telecomPhoneBills;
    }

    public TelecomPhoneBill getLastTelecomPhoneBill() {
        if (this.lastTelecomPhoneBill != null)
            return this.lastTelecomPhoneBill;
        List<TelecomPhoneBill> bills = this.getTelecomPhoneBills();
        if ((bills != null) && (bills.size() > 1)) {
            return this.lastTelecomPhoneBill =
                    bills.stream()
                            .filter(bill -> bill.getBillDate() != null)
                            .max(Comparator.comparing(TelecomPhoneBill::getBillDate))
                            .get();
        } else
            return this.lastTelecomPhoneBill =
                    ((bills != null) && (bills.size() > 0)) ? bills.get(0)
                            : this.lastTelecomPhoneBill;
    }

    public void setLastTelecomPhoneBill(TelecomPhoneBill lastTelecomPhoneBill) {
        this.lastTelecomPhoneBill = lastTelecomPhoneBill;
    }

    public PicklistItem getServiceType() {
        return serviceType;
    }

    public void setServiceType(PicklistItem serviceType) {
        this.serviceType = serviceType;
    }

    public PicklistItem getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PicklistItem paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    //    public TelecomPhoneBill setLastTelecomPhoneBill(){
//        List<TelecomPhoneBill> bills = this.getTelecomPhoneBills();
//        if ((bills != null) && (bills.size()>1)){
//            return this.lastTelecomPhoneBill =
//                    bills.stream()
//                            .max(Comparator.comparing(TelecomPhoneBill::getBillDate))
//                            .get();
//        }
//        else
//            return this.lastTelecomPhoneBill =
//                    ((bills != null) && (bills.size()>0)) ? bills.get(0) : null;
//    }
//    
//    @BeforeInsert
//    @BeforeUpdate
//    public void updateHolders(){
//        PicklistItemRepository picklistItemRepository =
//                Setup.getRepository(PicklistItemRepository.class);
//        Set<PicklistItem> tempItems = new HashSet<PicklistItem>();
//        for (PicklistItem item : this.getHolders()){
//            item = picklistItemRepository.findOne(item.getId());
//            if (item!= null)
//                tempItems.add(item);
//        }
//        this.setHolders(tempItems);
//                
//    }
}
