package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.ManualDDFToSendStatus;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 13, 2020
 *         Jirra ACC-1598
 */

@Entity
public class ManualDDFToSend extends BaseEntity {

    @Column(nullable = false)
    private String fileName;

    @Column
    private Date generatingDate;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ManualDDFToSendStatus status = ManualDDFToSendStatus.NOT_SENT;

    @Column
    @Lob
    @JsonIgnore
    private String ids;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getGeneratingDate() {
        return generatingDate;
    }

    public void setGeneratingDate(Date generatingDate) {
        this.generatingDate = generatingDate;
    }

    public ManualDDFToSendStatus getStatus() {
        return status;
    }

    public void setStatus(ManualDDFToSendStatus status) {
        this.status = status;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }
}
