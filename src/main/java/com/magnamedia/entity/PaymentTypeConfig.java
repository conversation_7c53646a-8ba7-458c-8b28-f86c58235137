package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 */
@Entity
public class PaymentTypeConfig extends AbstractPaymentTypeConfig {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PaymentTermConfig paymentTermConfig;

    public PaymentTypeConfig() {
    }

    public PaymentTypeConfig(
            PicklistItem type, String description, double amount, Double discount, Integer discountEffectiveAfter,
            Integer startsOn, Integer recurrence, Integer endsAfter, Boolean affectsPaidEndDate,
            Boolean affectedByAdditionalDiscount, PaymentTermConfig paymentTermConfig, PicklistItem subType) {

        super(type, description, amount, discount, discountEffectiveAfter, startsOn, recurrence, endsAfter, affectsPaidEndDate, affectedByAdditionalDiscount, subType);
        this.paymentTermConfig = paymentTermConfig;
    }

    public PaymentTypeConfig(ContractPaymentType contractPaymentType) {
        this(contractPaymentType.getType(), contractPaymentType.getDescription(), contractPaymentType.getAmount(),
                contractPaymentType.getDiscount(), contractPaymentType.getDiscountEffectiveAfter(),
                contractPaymentType.getStartsOn(), contractPaymentType.getRecurrence(),
                contractPaymentType.getEndsAfter(), contractPaymentType.getAffectsPaidEndDate(), contractPaymentType.getAffectedByAdditionalDiscount(), null, contractPaymentType.getSubType());
    }

    public PaymentTermConfig getPaymentTermConfig() {
        return paymentTermConfig;
    }

    public void setPaymentTermConfig(PaymentTermConfig paymentTermConfig) {
        this.paymentTermConfig = paymentTermConfig;
    }

    @BeforeInsert
    @BeforeUpdate
    public void checkDiscountValue() {
        if (this.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE) &&
                this.getDiscount() != null && this.getDiscount().equals(0.0)) {
            this.setDiscountEffectiveAfter(0);
        }
    }
}