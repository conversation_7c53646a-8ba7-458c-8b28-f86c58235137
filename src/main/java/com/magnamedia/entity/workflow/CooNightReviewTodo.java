package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.PaymentTermConfig;
import com.magnamedia.entity.serializer.CustomIdLabelCodeSerializer;

import javax.persistence.*;
import java.sql.Date;
import java.util.List;

@Entity
public class CooNightReviewTodo extends WorkflowEntity {

    public enum CooNightReviewType {
        REVIEW_DISCOUNT, MAIDS_LOANS_WAIVER;
    }

    @Column
    @Enumerated(EnumType.STRING)
    private CooNightReviewType type = CooNightReviewType.MAIDS_LOANS_WAIVER;

    @Column
    private String maidName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem maidNationality;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem maidType;

    @Column
    private Date maidJoiningDate;

    @Column(columnDefinition = "double default 0")
    private Double maidSalary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double loanWaiverAmount = 0.0;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User requester;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User approvedBy;

    @Lob
    private String cooNightReviewNote;

    @Column
    boolean doneByCoo = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    private PicklistItem contractProspectType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PaymentTermConfig paymentPlan;

    @Column
    private Double discount;

    @Column
    private int discountMonths;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem discountReason;

    @Column
    @Lob
    private String discountDescription;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User discountUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem ptcReason;

    @Column
    @Lob
    private String ptcDescription;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User ptcUser;

    public String getMaidName() { return maidName; }

    public void setMaidName(String maidName) { this.maidName = maidName; }

    public PicklistItem getMaidNationality() { return maidNationality; }

    public void setMaidNationality(PicklistItem maidNationality) { this.maidNationality = maidNationality; }

    public PicklistItem getMaidType() { return maidType; }

    public void setMaidType(PicklistItem maidType) { this.maidType = maidType; }

    public Date getMaidJoiningDate() { return maidJoiningDate; }

    public void setMaidJoiningDate(Date maidJoiningDate) { this.maidJoiningDate = maidJoiningDate; }

    public Double getMaidSalary() { return maidSalary; }

    public void setMaidSalary(Double maidSalary) { this.maidSalary = maidSalary; }

    public Double getLoanWaiverAmount() { return loanWaiverAmount; }

    public void setLoanWaiverAmount(Double loanWaiverAmount) { this.loanWaiverAmount = loanWaiverAmount; }

    public User getRequester() { return requester; }

    public void setRequester(User requester) { this.requester = requester; }

    public User getApprovedBy() { return approvedBy;}

    public void setApprovedBy(User approvedBy) { this.approvedBy = approvedBy; }

    public String getCooNightReviewNote() { return cooNightReviewNote; }

    public void setCooNightReviewNote(String cooNightReviewNote) { this.cooNightReviewNote = cooNightReviewNote; }

    public boolean isDoneByCoo() { return doneByCoo; }

    public void setDoneByCoo(boolean doneByCoo) { this.doneByCoo = doneByCoo; }

    public CooNightReviewTodo(String startTaskName) {
        super(startTaskName);
    }

    public CooNightReviewTodo() {
        this("");
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

    public CooNightReviewType getType() { return type; }

    public void setType(CooNightReviewType type) { this.type = type; }

    public Client getClient() { return client; }

    public void setClient(Client client) { this.client = client; }

    public PicklistItem getContractProspectType() { return contractProspectType; }

    public void setContractProspectType(PicklistItem contractProspectType) {
        this.contractProspectType = contractProspectType;
    }

    public PaymentTermConfig getPaymentPlan() { return paymentPlan; }

    public void setPaymentPlan(PaymentTermConfig paymentPlan) { this.paymentPlan = paymentPlan; }

    public Double getDiscount() { return discount; }

    public void setDiscount(Double discount) { this.discount = discount; }

    public int getDiscountMonths() { return discountMonths; }

    public void setDiscountMonths(int discountMonths) { this.discountMonths = discountMonths; }

    public PicklistItem getDiscountReason() { return discountReason; }

    public void setDiscountReason(PicklistItem discountReason) { this.discountReason = discountReason; }

    public String getDiscountDescription() { return discountDescription; }

    public void setDiscountDescription(String discountDescription) { this.discountDescription = discountDescription; }

    public User getDiscountUser() { return discountUser; }

    public void setDiscountUser(User discountUser) { this.discountUser = discountUser; }

    public PicklistItem getPtcReason() { return ptcReason; }

    public void setPtcReason(PicklistItem ptcReason) { this.ptcReason = ptcReason; }

    public String getPtcDescription() { return ptcDescription; }

    public void setPtcDescription(String ptcDescription) { this.ptcDescription = ptcDescription; }

    public User getPtcUser() { return ptcUser; }

    public void setPtcUser(User ptcUser) { this.ptcUser = ptcUser; }
}
