package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.entity.serializer.TypeIdLabelSerializer;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@Entity
public class AdhocVariableBucket extends BasePLVariableBucket {

    @ManyToOne
    @JsonIgnore
    @JoinColumn(name = "P_LVARIABLE_ID")    
    private AdhocVariableNode PLVariable;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Revenue revenue;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    public BasePLVariableNode getpLVariable() {
        return PLVariable;
    }

    public void setpLVariable(BasePLVariableNode pLVariable) {
        this.PLVariable = (AdhocVariableNode) pLVariable;
    }

    @Override
    public Revenue getRevenue() {
        return revenue;
    }

    @Override
    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    @Override
    public Expense getExpense() {
        return expense;
    }

    @Override
    public void setExpense(Expense expense) {
        this.expense = expense;
    }
}

