package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelCodeSerializer;
import com.magnamedia.entity.serializer.HousemaidSerilizer;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.MVPackageType;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ComplaintRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> at Oct 19, 2017
 * 
 */
@Entity
@Table(
        indexes = {
                @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class Contract extends BaseEntityWithAdditionalInfo implements Serializable {
    protected static final Logger logger = Logger.getLogger(Contract.class.getName());

    public final static String DOWNGRADING_NATIONALITY_DATE = "downgrading_nationality_date";
    public final static String DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID = "downgrading_nationality_job_passed_while_has_no_maid";
    public final static String UPGRADING_NATIONALITY_DATE = "upgrading_nationality_date";
    public final static String OEC_AMEND_DDS = "oec_amend_dds";

    public final static String CHANGE_TO_PAYING_VIA_CREDIT_CARD_DATE = "change_to_paying_via_credit_card_date";

    public final static String SWITCHING_BANK_ACCOUNT_DATE = "switching_bank_account_date";
    public final static String SWITCHING_BANK_ACCOUNT_BOUNCED_PAYMENT_ID = "switching_bank_account_bounced_payment_id";
    public final static String TEMP_EID_ATTACHMENT_TAG = "bank_info_temp_eid";
    public final static String TEMP_IBAN_ATTACHMENT_TAG = "bank_info_temp_iban";
    public final static String TEMP_ACCOUNT_NAME_ATTACHMENT_TAG = "bank_info_temp_account_name";

    public final static String PAYMENT_RECEIVED_NOTIFICATION = "payment_received_notification";

    // Termination Reason
    public final static String INCOMPLETE_FLOW_MISSING_BANK_INFO_REASON = "signature_collection_flow_max_trials_reached";
    public final static String INCOMPLETE_FLOW_DATA_ENTRY_REJECTION_REASON = "client_did_not_sign_dd_after_x_days";
    public final static String DUE_PAYING_VIA_CC_FLOW_TERMINATION_REASON = "due_paying_via_cc_flow";
    public final static String DUE_IPAM_FLOW_TERMINATION_REASON  = "due_ipam_flow";
    public final static String DUE_EXTENSION_FLOW_TERMINATION_REASON  = "due_extension_flow";
    public final static String ONLINE_REMINDER_TERMINATION_REASON = "due_online_payment_reminder_flow";

    public final static String NO_MAID_AFTER_UNFREEZING = "no_maid_after_unfreezing";

    // ACC-8662
    public final static String PROPERTY_PREVENT_CREATE_OTHER_DDS = "prevent_create_other_dds";

    public final static String ADDITIONAL_INFO_IS_IPAM_RUNNING = "isIpamRunning";
    public final static String ADDITIONAL_INFO_EXTENSION_FLOW_DISABLED = "extensionFlowDisabled";
    public final static String SKIP_DDC_APPROVAL_FOR_EXPIRY = "skip_ddc_approval_for_expiry";

    // ACC-9011
    public final static String ADDITIONAL_INFO_NOTIFY_ABOUT_OVERDUE_PAYMENTS_AFTER_CANCELLATION = "notify_about_overdue_payments_after_cancellation";
    public final static String ADDITIONAL_INFO_NOTIFY_PENDING_DD_AFTER_CANCELLATION = "notify_pending_dd_after_cancellation";

    public enum ContractSource {
        IC, VISA_APP, CC_APP, CHAT_GPT
    }

    public enum ReceivePaymentNotificationsStatus {
        MANUALLY_ENABLED, AUTOMATICALLY_ENABLED, DISABLED
    }

    //Jirra ACC-659
    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "contract")
    private List<ContractPaymentTerm> contractPaymentTerms;

    @NotAudited
    @Formula("(FLOOR((DATEDIFF(ADJUSTED_END_DATE,CURDATE()))))")
    private Integer daysToAdjustedEnd;

    @NotAudited
    @Formula("(FLOOR((DATEDIFF(CURDATE(),START_OF_CONTRACT))))")
    private Integer daysSinceStartDate;

    public Integer getDaysSinceStartDate() {
        return daysSinceStartDate;
    }

    public void setDaysSinceStartDate(Integer daysSinceStartDate) {
        this.daysSinceStartDate = daysSinceStartDate;
    }

    //@JsonSerialize(using = IdLabelSerializer.class)
//    @JsonIgnore
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    //    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    @JsonSerialize(using = HousemaidSerilizer.class)
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @ManyToOne(fetch = FetchType.LAZY)
    private Housemaid housemaid;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "contract")
    private List<Complaint> complaints;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "contract")
    private Set<Payment> payments;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "contract")
    private List<LiveInOutLog> LiveInOutLogs;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "contract")
    @OrderBy("creationDate DESC")
    private List<Replacement> replacements;

    @JsonIgnore
    @NotAudited
    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne
    private InterviewVisit interview;

    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne
    private Contract renewalContract;

    @NotAudited
    @Formula("(SELECT COUNT(*) FROM PAYMENTS WHERE PAYMENTS.CONTRACT_ID = id)")
    private Integer paymentsCount;

    @NotAudited
    @Formula(
            "(SELECT COUNT(*) FROM CLIENTUNPAIDWARNINGMESSAGES WHERE CLIENTUNPAIDWARNINGMESSAGES.CONTRACT_ID = id)")
    private Integer warningMsgCount;

    @NotAudited
    @Formula(
            "(SELECT DATEDIFF(C.END_OF_CONTRACT,C.PAID_END_DATE) FROM CONTRACTS C WHERE C.ID=id)")
    private Integer daysBetweenEndAndPaid;

    @Column
    private Boolean isRenewal = false;

    @Column
    private Boolean vatRecordGenerated = false;

    @Column
    private Boolean done = false;

    @Column
    private Boolean isCurrentlyFrozen;

    @Column
    private Boolean allowRecurring = false;

    //    @Column
//    private Date terminationDate;
//    @Enumerated(EnumType.STRING)
//    private ContractTerminationReasons terminationReason;
    @Column
    @Temporal(TemporalType.DATE)
    private Date adjustedEndDate;


    @Column
    private Date initialAdjustedEndDate;

    @Column
    private Integer accruedVacationDays;

    @Column
    private Boolean isDoneWelcome;

    @Column
    private Date paidEndDate;
    @Column
    private Date startOfContract;

    @Column
    @Temporal(TemporalType.DATE)
    private Date endOfContract;

    @Column
    private Date leaveOfficeDate;
    @Column
    private Date arriveClientHomeDate;

    @Column
    private Date lastWarningDateClient;
    @Column
    private Date lastWarningDateHousemaid;
    @Column
    private String emiratesId;

    @Column
    private String utilityBillAmountNumber;

    @Lob
    @Column
    private String housemaidNote;

    @Lob
    @Column
    private String clientNote;

    @Column
    private Double firstMonthPayment;
    @Column
    private Double lastMonthPayment;
    @Column
    private Double numberOfPDCs;
    @Column
    private Double recruitmentFee;
    @Column
    private Double liveOutFee;
    @Column
    private Double valueOfEachPDC;

    @Enumerated(EnumType.STRING)
    private HousemaidArrival housemaidArrival;

    @Enumerated(EnumType.STRING)
    private HousemaidLiveplace living;

    @Enumerated(EnumType.STRING)
    private ContractType contractType;

    @Enumerated(EnumType.STRING)
    private ContractStatus status;

    //documents dates only to init the client documents
    // when creating new contract
    @Transient
    private Date passportExpirtyDate;
    @Transient
    private Date emiratesIdFronSideExpirtyDate;
    @Transient
    private Date emiratesIdBackSideExpirtyDate;
    @Transient
    private Date visaExpirtyDate;
    @Transient
    private Date ejariExpirtyDate;
    @Transient
    private Date tenancyContractExpirtyDate;
    @Transient
    private Date mariageCertificateExpirtyDate;
    @Transient
    private Date corporateCompanyWorkProofExpirtyDate;

    @Column
    private Boolean cancelledContract = false;

    @Column
    private Date cancelledDate;

    @Column
    private Date dateOfCancellation;

    @Column
    private Date dateOfTermination;

    @Column
    private Boolean ScheduledTerminationPendingApproval = false;

    @Column
    @Temporal(TemporalType.DATE)
    private Date scheduledDateOfTermination;

    //Jirra ACC-1135
    @Column(columnDefinition = "boolean default false")
    private Boolean isScheduledForTermination;

    @Enumerated(EnumType.STRING)
    private TerminationType terminationType;

    @Enumerated(EnumType.STRING)
    private WhoTerminated whoTerminated;

    @Column
    private Date NoticeDateOfTermination;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem reasonOfTerminationList;

    @Column
    private String reasonOfTermination;

    @NotAudited
    @OneToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Complaint terminationRelatedComplaint;

    @Column
    private Boolean emailSent;

    @Column
    private Boolean CancellationRefundAdded;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem hearedAboutUs;

    @Column
    private String hearedAboutUsOther;

    @Column
    private String googleSearchKeys;

    @Column
    private String googleSearchKeysOther;

    @Column
    private String remark;

    @Column(nullable = false)
    private boolean isLiveOut = false;

    @Column(columnDefinition = "boolean default false")
    private boolean excludeFromExpiryFlow = false;

    @Column
    private Double defaultST;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem nationality;

    //    @Column
//    private Boolean maidVisaAE = false;
    @Column
    private Double ethiopianLiveInMonthlyFee;

    @Column(nullable = false, columnDefinition = "varchar(255) default 'FRIDAY'")
    @Enumerated(EnumType.STRING)
    private Day weekend = Day.FRIDAY;

    //Added fields after moving "Add new contract" to sales
    //Jirra ACC-278
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelCodeSerializer.class)
    private PicklistItem contractProspectType;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem workerNationality;

    @Column(nullable = true)
    private String workerName;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem workerType;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem workerCurrentSituation;

    @Column(nullable = true)
    private Double depositFee;

    @Column(nullable = true)
    private Double agencyFee;

    @Column(nullable = true)
    private Double monthlyPayment;

    @Column(nullable = true)
    private Double workerSalary;

    @Column
    private String workerPassportNumber;

    @Column
    private Double overstayFee;

    @Column
    @Enumerated(EnumType.STRING)
    private ContractFeesType contractFeesType;

    @Column(columnDefinition = "varchar(255) default 'CONTRACTS_WITHOUT_VACATION'")
    @Enumerated(EnumType.STRING)
    private ContractMode contractMode;

    @Column(columnDefinition = "boolean default false")
    private Boolean isProRated;

    @Column(columnDefinition = "boolean default true")
    private boolean terminateContractDueRejection;

    //Jirra ACC-1092
    @Column(nullable = false, columnDefinition = "int(10) default 100")
    private Integer taxInvoiceCounter = 100;
    @Column(nullable = false, columnDefinition = "int(10) default 100")
    private Integer taxCreditNoteCounter = 100;
    @Column(nullable = false, columnDefinition = "int(10) default 100")
    private Integer paymentRequestTaxCreditNoteCounter = 100;

    //Jirra ACC-1241
    @Column
    @JsonIgnore
    private String discountCode;

    @Column(columnDefinition = "boolean default false")
    @JsonIgnore
    private boolean refundSentToExpensify;

    //Jirra ACC-1311
    @Column(columnDefinition = "boolean default false")
    @JsonIgnore
    private Boolean specialCase;

    //Jirra CM-1039
    @Column(columnDefinition = "boolean default false")
    private Boolean clientPaidVat = false;

    //Jirra ACC-1321
    @Column(columnDefinition = "boolean default false")
    private Boolean settled = false;

    //Jirra ACC-1625
    @Lob
    @Column
    private String expensifyNotes;

    //Jirra ACC-1435 from here
    @NotAudited
    @Formula("(EXISTS (select 1 from SALESENTITYPROPERTYS p where p._KEY = 'prorated_plus_month' and p.ORIGIN_TYPE = 'Contract' and p.ORIGIN_ID = ID))")
    private Boolean isProRatedPlusMonth;

    //SAL-2034
    @NotAudited
    @Formula("(EXISTS (select 1 from SALESENTITYPROPERTYS p where p._KEY = 'visa_service_application' and p.ORIGIN_TYPE = 'Contract' and p.ORIGIN_ID = ID))")
    private Boolean isMaidVisaServiceApplication;

    //ACC-2198
    @NotAudited
    @Formula("(EXISTS (select 1 from SALESENTITYPROPERTYS p where p._KEY = 'cc_service_application' and p.ORIGIN_TYPE = 'Contract' and p.ORIGIN_ID = ID))")
    private Boolean isMaidCCServiceApplication;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Double additionalDiscount;

    @Transient
    private Integer proRatedDays;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String additionalDiscountNotes;

    @Enumerated(EnumType.STRING)
    @Column
    private Gender workerGender;

    @Enumerated(EnumType.STRING)
    private WorkerPrevVisaType workerPrevVisaType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem workerInitialLocation;

    @Transient
    @Enumerated(EnumType.STRING)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private PickupType pickupType;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Boolean clientWantTalkMaid;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String pickupGoogleMapKeywords;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String pickupNotes;

    @Column
    private Date workerBirthDate;

    @Column
    private Date workerPassportExpiryDate;

    @Column(nullable = true)
    private String workerCarRegNumber;

    @Transient
    private String workerFirstName;

    @Transient
    private String workerMiddleName;

    @Transient
    private String workerLastName;

    @Column
    @Min(0)
    private Integer paymentsDuration;

    @Column
    private Boolean workerInHerCountry;

    @Column(nullable = true)
    private String workerMobileInCountry;

    @Column(nullable = true)
    @Lob
    private String workerAddressInCountry;

    @Column(nullable = true)
    private Date workerArriveDubaiDate;

    @Transient
    @Enumerated(EnumType.STRING)
    private ContractPackageType packageType;

    @Transient
    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PaymentTermConfig paymentTermConfig;

    @Enumerated(EnumType.STRING)
    private RVisaProcedureType workerRVisaProcedureType;

    @Column(nullable = true)
    private Boolean workerHasPrevSponsor;

    @Column(nullable = true)
    private Boolean workerPrevSponsorCancelled;
    //Jirra ACC-1435 to here

    //relation from new to old contract
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @OneToOne(fetch = FetchType.LAZY)
    private Contract oldRenewedContract;

    @Column
    private Boolean urgentMedical = false;

    //Jirra ACC-2115
    @Column(columnDefinition = "boolean default false")
    private boolean ddGenerationNextMonth = false;

    //Jirra ACC-2860
    @Column(columnDefinition = "boolean default false")
    private boolean signingPaperMode = false;

    //Jirra ACC-3555
    @Column(columnDefinition = "boolean default false")
    private boolean cancelledWithinFirstXDays = false;

    //Jirra ACC-3427
    @Transient
    private Boolean ccAppRetractedCancellation;
    
    /**ACC-3597*/
    @Column(nullable = true)
    private Double tempWorkerSalary;

    @Column(columnDefinition = "boolean default false")
    private boolean payingViaCreditCard = false;

    //ACC-4905
    @Column(columnDefinition = "boolean default false")
    private boolean isOneMonthAgreement = false;

    @Column
    @JsonIgnore
    @Enumerated(EnumType.STRING)
    private MVPackageType mvPackageType;

    @Column
    @JsonIgnore
    @Enumerated(EnumType.STRING)
    private ContractSource source;

    @Column
    private Date freezingDate;

    @Column(columnDefinition = "int default 0")
    private int waivedMonths = 0;

    @Column
    @Enumerated(EnumType.STRING)
    private ReceivePaymentNotificationsStatus receivePaymentNotifications;


    @Column(columnDefinition = "boolean default false")
    private Boolean specialProRated = false;

    public List<ContractPaymentTerm> getContractPaymentTerms() {
        return contractPaymentTerms;
    }

    public void setContractPaymentTerms(List<ContractPaymentTerm> contractPaymentTerms) {
        this.contractPaymentTerms = contractPaymentTerms;
    }

    public Date getCorporateCompanyWorkProofExpirtyDate() {
        return corporateCompanyWorkProofExpirtyDate;
    }

    public void setCorporateCompanyWorkProofExpirtyDate(
            Date corporateCompanyWorkProofExpirtyDate) {
        this.corporateCompanyWorkProofExpirtyDate = corporateCompanyWorkProofExpirtyDate;
    }

    public Integer getWarningMsgCount() {
        return warningMsgCount;
    }

    public void setWarningMsgCount(Integer warningMsgCount) {
        this.warningMsgCount = warningMsgCount;
    }

    public Integer getDaysBetweenEndAndPaid() {
        return daysBetweenEndAndPaid;
    }

    public void setDaysBetweenEndAndPaid(Integer daysBetweenEndAndPaid) {
        this.daysBetweenEndAndPaid = daysBetweenEndAndPaid;
    }

    public Boolean getAllowRecurring() { return allowRecurring != null && allowRecurring; }

    public void setAllowRecurring(Boolean allowRecurring) { this.allowRecurring = allowRecurring; }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public PicklistItem getHearedAboutUs() {
        return hearedAboutUs;
    }

    public void setHearedAboutUs(PicklistItem hearedAboutUs) {
        this.hearedAboutUs = hearedAboutUs;
    }

    public String getHearedAboutUsOther() {
        return hearedAboutUsOther;
    }

    public void setHearedAboutUsOther(String hearedAboutUsOther) {
        this.hearedAboutUsOther = hearedAboutUsOther;
    }

    public Date getInitialAdjustedEndDate() {
        return initialAdjustedEndDate;
    }

    public void setInitialAdjustedEndDate(Date initialAdjustedEndDate) {
        this.initialAdjustedEndDate = initialAdjustedEndDate;
    }

    public Integer getAccruedVacationDays() {
        return accruedVacationDays;
    }

    public void setAccruedVacationDays(Integer accruedVacationDays) {
        this.accruedVacationDays = accruedVacationDays;
    }

    public Date getPaidEndDate() {
        return paidEndDate;
    }

    public void setPaidEndDate(Date paidEndDate) {
        this.paidEndDate = paidEndDate;
    }

    public Boolean getIsCurrentlyFrozen() {
        return isCurrentlyFrozen;
    }

    public void setIsCurrentlyFrozen(Boolean isCurrentlyFrozen) {
        this.isCurrentlyFrozen = isCurrentlyFrozen;
    }

    public Date getLastWarningDateClient() {
        return lastWarningDateClient;
    }

    public void setLastWarningDateClient(Date lastWarningDateClient) {
        this.lastWarningDateClient = lastWarningDateClient;
    }

    public Date getLastWarningDateHousemaid() {
        return lastWarningDateHousemaid;
    }

    public void setLastWarningDateHousemaid(Date lastWarningDateHousemaid) {
        this.lastWarningDateHousemaid = lastWarningDateHousemaid;
    }

    public Integer getDaysToAdjustedEnd() {
        return daysToAdjustedEnd;
    }

    public void setDaysToAdjustedEnd(Integer daysToAdjustedEnd) {
        this.daysToAdjustedEnd = daysToAdjustedEnd;
    }

    public String getHousemaidNote() {
        return housemaidNote;
    }

    public void setHousemaidNote(String housemaidNote) {
        this.housemaidNote = housemaidNote;
    }

    public String getClientNote() {
        return clientNote;
    }

    public void setClientNote(String clientNote) {
        this.clientNote = clientNote;
    }

    public Boolean getIsRenewal() {
        return isRenewal;
    }

    public void setIsRenewal(Boolean isRenewal) {
        this.isRenewal = isRenewal;
    }

    public Boolean getVatRecordGenerated() {
        return vatRecordGenerated;
    }

    public void setVatRecordGenerated(Boolean vatRecordGenerated) {
        this.vatRecordGenerated = vatRecordGenerated;
    }

    public Boolean getDone() {
        return done;
    }

    public void setDone(Boolean done) {
        this.done = done;
    }

    public Contract getRenewalContract() {
        return renewalContract;
    }

    public void setRenewalContract(Contract renewalContract) {
        this.renewalContract = renewalContract;
    }

    public Set<Payment> getPayments() {
        return payments == null ? new HashSet<>() : payments;
    }

    public void setPayments(Set<Payment> payments) {
        this.payments = payments;
    }

    public Boolean getIsDoneWelcome() {
        return isDoneWelcome;
    }

    public void setIsDoneWelcome(Boolean isDoneWelcome) {
        this.isDoneWelcome = isDoneWelcome;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public InterviewVisit getInterview() {
        return interview;
    }

    public void setInterview(InterviewVisit interview) {
        this.interview = interview;
    }

    public String getStartOfContractCSV() {
        return DateUtil.formatDateDashed(startOfContract);
    }

    public Date getStartOfContract() {
        return startOfContract;
    }

    public void setStartOfContract(Date startOfContract) {
        this.startOfContract = startOfContract;
    }

    public Date getEndOfContract() {
        return endOfContract;
    }

    public String getEndOfContractCSV() {
        return DateUtil.formatDateDashedWithTimeV2(endOfContract);
    }

    public void setEndOfContract(Date endOfContract) {
        this.endOfContract = endOfContract;
    }

    public Date getLeaveOfficeDate() {
        return leaveOfficeDate;
    }

    public void setLeaveOfficeDate(Date leaveOfficeDate) {
        this.leaveOfficeDate = leaveOfficeDate;
    }

    public Date getArriveClientHomeDate() {
        return arriveClientHomeDate;
    }

    public void setArriveClientHomeDate(Date arriveClientHomeDate) {
        this.arriveClientHomeDate = arriveClientHomeDate;
    }

    public String getEmiratesId() {
        return emiratesId;
    }

    public void setEmiratesId(String emiratesId) {
        this.emiratesId = emiratesId;
    }

    public String getUtilityBillAmountNumber() {
        return utilityBillAmountNumber;
    }

    public void setUtilityBillAmountNumber(String utilityBillAmountNumber) {
        this.utilityBillAmountNumber = utilityBillAmountNumber;
    }

    public HousemaidArrival getHousemaidArrival() {
        return housemaidArrival;
    }

    public void setHousemaidArrival(HousemaidArrival housemaidArrival) {
        this.housemaidArrival = housemaidArrival;
    }

    public HousemaidLiveplace getLiving() {
        return living;
    }

    public void setLiving(HousemaidLiveplace living) {
        this.living = living;
    }

    public ContractType getContractType() {
//        if (getRecruitmentFee() == null) {
//         contractType   contractType = ContractType.SHORT_TERM;
//            return contractType;
//        }
//
//        if (getRecruitmentFee() > 0) {
//            contractType = ContractType.LONG_TERM;
//        } else {
//            contractType = ContractType.SHORT_TERM;
//        }
        return contractType;
    }

    public void setContractType(ContractType contractType) {
//        if (getRecruitmentFee() > 0) {
//            contractType = ContractType.LONG_TERM;
//        } else {
//            contractType = ContractType.SHORT_TERM;
//        }

        this.contractType = contractType;
    }

    public ContractStatus getStatus() {
        return status;
    }

    public void setStatus(ContractStatus status) {
        this.status = status;
    }

    public Date getPassportExpirtyDate() {
        return passportExpirtyDate;
    }

    public void setPassportExpirtyDate(Date passportExpirtyDate) {
        this.passportExpirtyDate = passportExpirtyDate;
    }

    public Date getEmiratesIdFronSideExpirtyDate() {
        return emiratesIdFronSideExpirtyDate;
    }

    public void setEmiratesIdFronSideExpirtyDate(
            Date emiratesIdFronSideExpirtyDate) {
        this.emiratesIdFronSideExpirtyDate = emiratesIdFronSideExpirtyDate;
    }

    public Date getEmiratesIdBackSideExpirtyDate() {
        return emiratesIdBackSideExpirtyDate;
    }

    public void setEmiratesIdBackSideExpirtyDate(
            Date emiratesIdBackSideExpirtyDate) {
        this.emiratesIdBackSideExpirtyDate = emiratesIdBackSideExpirtyDate;
    }

    public Date getVisaExpirtyDate() {
        return visaExpirtyDate;
    }

    public void setVisaExpirtyDate(Date visaExpirtyDate) {
        this.visaExpirtyDate = visaExpirtyDate;
    }

    public Date getEjariExpirtyDate() {
        return ejariExpirtyDate;
    }

    public void setEjariExpirtyDate(Date ejariExpirtyDate) {
        this.ejariExpirtyDate = ejariExpirtyDate;
    }

    public Date getTenancyContractExpirtyDate() {
        return tenancyContractExpirtyDate;
    }

    public void setTenancyContractExpirtyDate(Date tenancyContractExpirtyDate) {
        this.tenancyContractExpirtyDate = tenancyContractExpirtyDate;
    }

    public Date getMariageCertificateExpirtyDate() {
        return mariageCertificateExpirtyDate;
    }

    public void setMariageCertificateExpirtyDate(
            Date mariageCertificateExpirtyDate) {
        this.mariageCertificateExpirtyDate = mariageCertificateExpirtyDate;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getAdjustedEndDate() {
        return adjustedEndDate;
    }

    public String getAdjustedEndDateCSV() {
        return DateUtil.formatDateDashedWithTimeV2(adjustedEndDate);
    }

    public void setAdjustedEndDate(Date adjustedEndDate) {
        this.adjustedEndDate = adjustedEndDate;
    }

    public Integer getPaymentsCount() {
        return payments == null ? 0 : payments.size();
    }

    public void setPaymentsCount(Integer paymentsCount) {
        this.paymentsCount = paymentsCount;
    }

    public Date getCreationDate_() {
        return super.getCreationDate();
    }

    public List<Complaint> getComplaints() {
        return complaints;
    }

    public void setComplaints(List<Complaint> complaints) {
        this.complaints = complaints;
    }

    public Double getFirstMonthPayment() {
        return firstMonthPayment;
    }

    public void setFirstMonthPayment(Double firstMonthPayment) {
        this.firstMonthPayment = firstMonthPayment;
    }

    public Double getLastMonthPayment() {
        return lastMonthPayment;
    }

    public void setLastMonthPayment(Double lastMonthPayment) {
        this.lastMonthPayment = lastMonthPayment;
    }

    public Double getNumberOfPDCs() {
        return numberOfPDCs;
    }

    public void setNumberOfPDCs(Double numberOfPDCs) {
        this.numberOfPDCs = numberOfPDCs;
    }

    public Double getRecruitmentFee() {
        return recruitmentFee == null ? 0.0 : recruitmentFee;
    }

    public void setRecruitmentFee(Double recruitmentFee) {
        this.recruitmentFee = recruitmentFee;
    }

    public Double getLiveOutFee() {
        return liveOutFee;
    }

    public void setLiveOutFee(Double liveOutFee) {
        this.liveOutFee = liveOutFee;
    }

    public Double getValueOfEachPDC() {
        return valueOfEachPDC;
    }

    public void setValueOfEachPDC(Double valueOfEachPDC) {
        this.valueOfEachPDC = valueOfEachPDC;
    }

    public Boolean getCancelledContract() {
        return cancelledContract;
    }

    public void setCancelledContract(Boolean cancelledContract) {
        this.cancelledContract = cancelledContract;
    }

    public Date getCancelledDate() {
        return cancelledDate;
    }

    public void setCancelledDate(Date cancelledDate) {
        this.cancelledDate = cancelledDate;
    }

    public Date getDateOfCancellation() {
        return dateOfCancellation;
    }

    public void setDateOfCancellation(Date dateOfCancellation) {
        this.dateOfCancellation = dateOfCancellation;
    }

    public Date getDateOfTermination() {
        return dateOfTermination;
    }

    public void setDateOfTermination(Date dateOfTermination) {
        this.dateOfTermination = dateOfTermination;
    }

    public Boolean getScheduledTerminationPendingApproval() {
        return ScheduledTerminationPendingApproval;
    }

    public void setScheduledTerminationPendingApproval(
            Boolean ScheduledTerminationPendingApproval) {
        this.ScheduledTerminationPendingApproval = ScheduledTerminationPendingApproval;
    }

    public Date getScheduledDateOfTermination() {
        return scheduledDateOfTermination;
    }

    public String getScheduledDateOfTerminationCSV() {
        return DateUtil.formatDateDashedWithTimeV2(scheduledDateOfTermination);
    }

    public void setScheduledDateOfTermination(Date scheduledDateOfTermination) {
        this.scheduledDateOfTermination = scheduledDateOfTermination;
    }

    public Boolean getIsScheduledForTermination() {
        return isScheduledForTermination != null && isScheduledForTermination;
    }

    public void setIsScheduledForTermination(Boolean isScheduledForTermination) {
        this.isScheduledForTermination = isScheduledForTermination;
    }

    public TerminationType getTerminationType() {
        return terminationType;
    }

    public void setTerminationType(TerminationType terminationType) {
        this.terminationType = terminationType;
    }

    public WhoTerminated getWhoTerminated() {
        return whoTerminated;
    }

    public void setWhoTerminated(WhoTerminated whoTerminated) {
        this.whoTerminated = whoTerminated;
    }

    public Date getNoticeDateOfTermination() {
        return NoticeDateOfTermination;
    }

    public void setNoticeDateOfTermination(Date NoticeDateOfTermination) {
        this.NoticeDateOfTermination = NoticeDateOfTermination;
    }

    public String getReasonOfTermination() {
        return reasonOfTermination;
    }

    public void setReasonOfTermination(String reasonOfTermination) {
        this.reasonOfTermination = reasonOfTermination;
    }

    public Complaint getTerminationRelatedComplaint() {
        return terminationRelatedComplaint;
    }

    public void setTerminationRelatedComplaint(
            Complaint terminationRelatedComplaint) {
        this.terminationRelatedComplaint = terminationRelatedComplaint;
    }

    public Boolean getEmailSent() {
        return emailSent;
    }

    public void setEmailSent(Boolean emailSent) {
        this.emailSent = emailSent;
    }

    public Boolean getCancellationRefundAdded() {
        return CancellationRefundAdded == null ? false : CancellationRefundAdded;
    }

    public void setCancellationRefundAdded(Boolean CancellationRefundAdded) {
        this.CancellationRefundAdded = CancellationRefundAdded;
    }

    public List<LiveInOutLog> getLiveInOutLogs() {
        return LiveInOutLogs;
    }

    public void setLiveInOutLogs(List<LiveInOutLog> LiveInOutLogs) {
        this.LiveInOutLogs = LiveInOutLogs;
    }

    public List<Replacement> getReplacements() {
        return replacements == null ? new ArrayList<>() : replacements;
    }

    public void setReplacements(List<Replacement> replacements) {
        this.replacements = replacements;
    }

    public PicklistItem getReasonOfTerminationList() {
        return reasonOfTerminationList;
    }

    public void setReasonOfTerminationList(PicklistItem reasonOfTerminationList) {
        this.reasonOfTerminationList = reasonOfTerminationList;
    }

    public ContractStatus getRenewalContractStatus() {
        return renewalContract == null ? ContractStatus.UNKNOWN : renewalContract.getStatus();
    }

    public String getGoogleSearchKeys() {
        return googleSearchKeys;
    }

    public void setGoogleSearchKeys(String googleSearchKeys) {
        this.googleSearchKeys = googleSearchKeys;
    }

    public String getGoogleSearchKeysOther() {
        return googleSearchKeysOther;
    }

    public void setGoogleSearchKeysOther(String googleSearchKeysOther) {
        this.googleSearchKeysOther = googleSearchKeysOther;
    }

    public Double getDefaultST() {
        return defaultST;
    }

    public void setDefaultST(Double defaultST) {
        this.defaultST = defaultST;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    //    public Boolean getMaidVisaAE() {
//        return maidVisaAE;
//    }
//
//    public void setMaidVisaAE(Boolean maidVisaAE) {
//        this.maidVisaAE = maidVisaAE;
//    }
    public Double getEthiopianLiveInMonthlyFee() {
        return ethiopianLiveInMonthlyFee;
    }

    public void setEthiopianLiveInMonthlyFee(Double ethiopianLiveInMonthlyFee) {
        this.ethiopianLiveInMonthlyFee = ethiopianLiveInMonthlyFee;
    }

    public Day getWeekend() {
        return weekend;
    }

    public void setWeekend(Day weekend) {
        this.weekend = weekend;
    }

    public PicklistItem getContractProspectType() {
        return contractProspectType;
    }

    //Jirra ACC-278
    public void setContractProspectType(PicklistItem contractProspectType) {
        this.contractProspectType = contractProspectType;
    }

    public PicklistItem getWorkerNationality() {
        return workerNationality;
    }

    public void setWorkerNationality(PicklistItem workerNationality) {
        this.workerNationality = workerNationality;
    }

    public String getWorkerName() {
        return workerName;
    }

    public void setWorkerName(String workerName) {
        this.workerName = workerName;
    }

    public PicklistItem getWorkerType() {
        return workerType;
    }

    public void setWorkerType(PicklistItem workerType) {
        this.workerType = workerType;
    }

    public PicklistItem getWorkerCurrentSituation() {
        return workerCurrentSituation;
    }

    public void setWorkerCurrentSituation(PicklistItem workerCurrentSituation) {
        this.workerCurrentSituation = workerCurrentSituation;
    }

    public Double getDepositFee() {
        return depositFee;
    }

    public void setDepositFee(Double depositFee) {
        this.depositFee = depositFee;
    }

    public Double getAgencyFee() {
        return agencyFee;
    }

    public void setAgencyFee(Double agencyFee) {
        this.agencyFee = agencyFee;
    }

    public Double getMonthlyPayment() {
        return monthlyPayment;
    }

    public void setMonthlyPayment(Double monthlyPayment) {
        this.monthlyPayment = monthlyPayment;
    }

    public Double getWorkerSalary() {
        return workerSalary;
    }

    public Double getWorkerSalaryWithoutVat() {
        return workerSalary;
    }

    public Double getWorkerSalaryWithVat() {

        if (getWorkerSalary() == null || getWorkerSalary() == 0D) return 0D;
        return Math.ceil(DiscountsWithVatHelper.getAmountPlusVat(getWorkerSalary()));
    }

    public Double getWorkerSalaryNew() {
        return isWorkerSalaryVatted() ? getWorkerSalaryWithVat() : getWorkerSalaryWithoutVat();
    }

    public void setWorkerSalary(Double workerSalary) {
        this.workerSalary = workerSalary;
    }

    public String getWorkerPassportNumber() {
        return workerPassportNumber;
    }

    public void setWorkerPassportNumber(String workerPassportNumber) {
        this.workerPassportNumber = workerPassportNumber;
    }

    public Double getOverstayFee() {
        return overstayFee;
    }

    public void setOverstayFee(Double overstayFee) {
        this.overstayFee = overstayFee;
    }

    public ContractFeesType getContractFeesType() {
        return contractFeesType;
    }

    public void setContractFeesType(ContractFeesType contractFeesType) {
        this.contractFeesType = contractFeesType;
    }

    @Transient
    private Integer clientComplaints;

    @Transient
    private Integer maidComplaints;

    @NotAudited
    @Formula(
            "(SELECT COUNT( * ) FROM   REPLACEMENTS R INNER JOIN CONTRACTS C ON R.CONTRACT_ID=C.ID   WHERE C.CLIENT_ID= CLIENT_ID )")

    private Integer clientReplacments;

    public Integer getClientComplaints() {
        if (clientComplaints == null) {
            List<Complaint> cms = Setup.getRepository(ComplaintRepository.class).findByClient(client);
            return cms == null ? 0 : cms.size();
        }

        return clientComplaints;
    }

    public Integer getMaidComplaints() {
        if (maidComplaints == null) {
            List<Complaint> cms = Setup.getRepository(ComplaintRepository.class).findByHousemaid(housemaid);
            return cms == null ? 0 : cms.size();
        }

        return maidComplaints;
    }

    public Integer getClientReplacments() {
        return clientReplacments == null ? 0 : clientReplacments;
    }

    public void setClientComplaints(Integer clientComplaints) {
        this.clientComplaints = clientComplaints;
    }

    public void setMaidComplaints(Integer maidComplaints) {
        this.maidComplaints = maidComplaints;
    }

    public void setClientReplacments(Integer clientReplacments) {
        this.clientReplacments = clientReplacments;
    }

    public void setContractMode(ContractMode contractMode) {
        this.contractMode = contractMode;
    }

    public ContractMode getContractMode() {
        return contractMode;
    }

    public void setProRated(Boolean proRated) { isProRated = proRated; }

    public Boolean getIsProRated() {
        return isProRated != null && isProRated;
    }

    public boolean isTerminateContractDueRejection() {
        return terminateContractDueRejection;
    }
    
    public void setTerminateContractDueRejection(boolean terminateContractDueRejection) {
        this.terminateContractDueRejection = terminateContractDueRejection;
    }
    
    public Integer getTaxInvoiceCounter() {
        return taxInvoiceCounter;
    }

    public void setTaxInvoiceCounter(Integer taxInvoiceCounter) {
        this.taxInvoiceCounter = taxInvoiceCounter;
    }

    public void increaseTaxInvoiceCounter() {
        this.taxInvoiceCounter += 1;
    }

    public void increaseTaxCreditNoteCounter() {
        this.taxCreditNoteCounter += 1;
    }

    public void increasePaymentRequestTaxCreditNoteCounter() {
        this.paymentRequestTaxCreditNoteCounter += 1;
    }

    public Integer getTaxCreditNoteCounter() {
        return taxCreditNoteCounter;
    }

//    public Double getAmountToRefundOnCancellation() {
//        return amountToRefundOnCancellation;
//    }
//
//    public void setAmountToRefundOnCancellation(Double amountToRefundOnCancellation) {
//        this.amountToRefundOnCancellation = amountToRefundOnCancellation;
//    }

    public void setTaxCreditNoteCounter(Integer taxCreditNoteCounter) {
        this.taxCreditNoteCounter = taxCreditNoteCounter;
    }

    public Integer getPaymentRequestTaxCreditNoteCounter() {
        return paymentRequestTaxCreditNoteCounter;
    }

    public void setPaymentRequestTaxCreditNoteCounter(Integer paymentRequestTaxCreditNoteCounter) {
        this.paymentRequestTaxCreditNoteCounter = paymentRequestTaxCreditNoteCounter;
    }

    public String getDiscountCode() {
        return discountCode;
    }

    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode;
    }

    public boolean getRefundSentToExpensify() {
        return refundSentToExpensify;
    }

    public void setRefundSentToExpensify(boolean refundSentToExpensify) {
        this.refundSentToExpensify = refundSentToExpensify;
    }

    public Boolean getSpecialCase() {
        return specialCase == null ? false : specialCase;
    }

    public void setSpecialCase(Boolean specialCase) {
        this.specialCase = specialCase;
    }

    public Boolean getClientPaidVat() {
        return clientPaidVat != null && clientPaidVat;
    }

    public void setClientPaidVat(Boolean clientPaidVat) {
        this.clientPaidVat = clientPaidVat;
    }

    public Boolean getSettled() {
        return settled != null && settled;
    }

    public void setSettled(Boolean settled) {
        this.settled = settled;
    }

    public String getExpensifyNotes() {
        return expensifyNotes;
    }

    public void setExpensifyNotes(String expensifyNotes) {
        this.expensifyNotes = expensifyNotes;
    }

    public Integer getPaymentsDuration() {
        return paymentsDuration;
    }

    public void setPaymentsDuration(Integer paymentsDuration) {
        this.paymentsDuration = paymentsDuration;
    }

    // ACC-1435 from here
    public Boolean getProRatedPlusMonth() {
        return isProRatedPlusMonth != null && isProRatedPlusMonth;
    }

    public void setProRatedPlusMonth(Boolean proRatedPlusMonth) {
        isProRatedPlusMonth = proRatedPlusMonth;
    }

    public Boolean getIsMaidVisaServiceApplication() {
        return isMaidVisaServiceApplication;
    }

    public void setIsMaidVisaServiceApplication(Boolean maidVisaServiceApplication) {
        isMaidVisaServiceApplication = maidVisaServiceApplication;
    }

    public Boolean getMaidCCServiceApplication() {
        return isMaidCCServiceApplication;
    }

    public void setMaidCCServiceApplication(Boolean maidCCServiceApplication) {
        isMaidCCServiceApplication = maidCCServiceApplication;
    }

    public Double getAdditionalDiscount() {
        return additionalDiscount;
    }

    public Integer getProRatedDays() {
        return proRatedDays;
    }

    public String getAdditionalDiscountNotes() {
        return additionalDiscountNotes;
    }

    public Gender getWorkerGender() {
        return workerGender;
    }

    public WorkerPrevVisaType getWorkerPrevVisaType() {
        return workerPrevVisaType;
    }

    public PicklistItem getWorkerInitialLocation() {
        return workerInitialLocation;
    }

    public PickupType getPickupType() {
        return pickupType;
    }

    public Boolean getClientWantTalkMaid() {
        return clientWantTalkMaid;
    }

    public String getPickupGoogleMapKeywords() {
        return pickupGoogleMapKeywords;
    }

    public String getPickupNotes() {
        return pickupNotes;
    }

    public Date getWorkerBirthDate() {
        return workerBirthDate;
    }

    public Date getWorkerPassportExpiryDate() {
        return workerPassportExpiryDate;
    }

    public String getWorkerCarRegNumber() {
        return workerCarRegNumber;
    }

    public String getWorkerFirstName() {
        return workerFirstName;
    }

    public String getWorkerMiddleName() {
        return workerMiddleName;
    }

    public String getWorkerLastName() {
        return workerLastName;
    }

    public Boolean getWorkerInHerCountry() {
        return workerInHerCountry;
    }

    public String getWorkerMobileInCountry() {
        return workerMobileInCountry;
    }

    public String getWorkerAddressInCountry() {
        return workerAddressInCountry;
    }

    public Date getWorkerArriveDubaiDate() {
        return workerArriveDubaiDate;
    }

    public Contract getOldRenewedContract() {
        return oldRenewedContract;
    }

    public Boolean getUrgentMedical() {
        return urgentMedical;
    }

    public boolean isDdGenerationNextMonth() {
        return ddGenerationNextMonth;
    }

    public void setDdGenerationNextMonth(boolean ddGenerationNextMonth) {
        this.ddGenerationNextMonth = ddGenerationNextMonth;
    }

    public boolean isSigningPaperMode() {
        return signingPaperMode;
    }

    public void setSigningPaperMode(boolean signingPaperMode) {
        this.signingPaperMode = signingPaperMode;
    }

    public boolean isCancelledWithinFirstXDays() {
        return cancelledWithinFirstXDays;
    }

    public void setCancelledWithinFirstXDays(boolean cancelledWithinFirstXDays) {
        this.cancelledWithinFirstXDays = cancelledWithinFirstXDays;
    }

    public Boolean getCcAppRetractedCancellation() {
        return ccAppRetractedCancellation;
    }

    public void setCcAppRetractedCancellation(Boolean ccAppRetractedCancellation) {
        this.ccAppRetractedCancellation = ccAppRetractedCancellation;
    }

    @JsonIgnore
    public Date getUpgradingNationalityDate() {
        AccountingEntityProperty upgradingNationalityDate = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndOriginAndDeletedFalse(Contract.UPGRADING_NATIONALITY_DATE, this);
        if (upgradingNationalityDate == null) return null;

        try {
            return DateUtil.parseDateDashed(upgradingNationalityDate.getValue());
        } catch (Exception e) {
            logger.severe("Exception while parsing " + upgradingNationalityDate.getValue() + ", to java.util.Date");
            logger.severe("Exception: " + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    @JsonIgnore
    public Date getDowngradingNationalityDate() {
        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        AccountingEntityProperty downgradingNationalityDate = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.DOWNGRADING_NATIONALITY_DATE, this);
        if (downgradingNationalityDate == null) return null;
        try {
            return DateUtil.parseDateDashed(downgradingNationalityDate.getValue());
        } catch (Exception e) {
            logger.severe("Exception while parsing " + downgradingNationalityDate.getValue() + ", to java.util.Date");
            logger.severe("Exception: " + ExceptionUtils.getStackTrace(e));
            logger.severe("break cde redundancy alert");
            return null;
        }
    }

    public ContractPackageType getPackageType() {
        return packageType;
    }

    public PaymentTermConfig getPaymentTermConfig() {
        return paymentTermConfig;
    }

    public RVisaProcedureType getWorkerRVisaProcedureType() {
        return workerRVisaProcedureType;
    }

    public Boolean getWorkerHasPrevSponsor() {
        return workerHasPrevSponsor;
    }

    public Boolean getWorkerPrevSponsorCancelled() {
        return workerPrevSponsorCancelled;
    }

    public void setWorkerHasPrevSponsor(Boolean workerHasPrevSponsor) {
        this.workerHasPrevSponsor = workerHasPrevSponsor;
    }

    public void setWorkerPrevSponsorCancelled(Boolean workerPrevSponsorCancelled) {
        this.workerPrevSponsorCancelled = workerPrevSponsorCancelled;
    }

    //Jirra ACC-1435 to here

    @BeforeInsert
    public void fixContractId() {
        this.setContractFeesType(contractFeesType.TYPE2);
        Long maxId = Setup.getRepository(ContractRepository.class).getMaxId();

        this.setId(maxId == null ? Long.valueOf(1) : ++maxId);
    }

    @JsonIgnore
    public ContractPaymentTerm getActiveContractPaymentTerm() {
        List<Map> cpts = Setup.getRepository(ContractPaymentTermRepository.class)
                .findActiveTermsByContract(this);
        
        if (cpts.isEmpty()) {
            throw new BusinessException("Contract_" + this.getId() + " has no active Contract Payment Term");
        }
        
        if (cpts.size() > 1) {
            throw new BusinessException("Contract_" + this.getId() + " has more than one active Contract Payment Term");
        }
        
        return (ContractPaymentTerm) cpts.get(0).get("contractPaymentTerm");
    }

    public ContractPaymentTerm hasBeenUpgradedNationalityBefore(ContractPaymentTerm cpt) {
        SelectQuery cptsQuery = new SelectQuery(ContractPaymentTerm.class);
        cptsQuery.filterBy("contract", "=", this);
        cptsQuery.filterBy("reason", "=", ContractPaymentTermReason.SWITCHING);
        cptsQuery.filterBy("switchingToPhilipino", "=", Boolean.TRUE);
        cptsQuery.filterBy("id", "<=", cpt.getId());
        cptsQuery.sortBy("creationDate", false);
        cptsQuery.setLimit(1);

        List<ContractPaymentTerm> cpts = cptsQuery.execute();

        if (cpts == null || cpts.isEmpty()) return null;

        return cpts.get(0);
    }

    /**ACC-3597*/
    public Double getTempWorkerSalary() {
        return tempWorkerSalary;
    }

    public void setTempWorkerSalary(Double tempWorkerSalary) {
        this.tempWorkerSalary = tempWorkerSalary;
    }
    
    public boolean deleteAttachmentByTag(String tag) {
        Attachment attachment = getAttachment(tag);
        if (attachment != null) {
            return Storage.deleteAttachment(attachment);

        }
        return false;
    }
    
    @JsonIgnore
    public boolean isActive() {
        return getStatus().equals(ContractStatus.ACTIVE);
    }

    @JsonIgnore
    public boolean isEnded() {
        return getStatus().equals(ContractStatus.CANCELLED) ||
                getStatus().equals(ContractStatus.EXPIRED);
    }

    @JsonIgnore
    public boolean isMaidCc() {
        return this.getContractProspectType().getCode()
                .equals(AccountingModule.MAID_CC_PROSPECT_TYPE_CODE);
    }

    public boolean isPayingViaCreditCard() { return payingViaCreditCard; }

    public void setPayingViaCreditCard(boolean payingViaCreditCard) { this.payingViaCreditCard = payingViaCreditCard; }

    @JsonIgnore
    public boolean isMaidVisa() { return !isMaidCc(); }

    public boolean isOneMonthAgreement() { return isOneMonthAgreement; }

    public void setOneMonthAgreement(boolean oneMonthAgreement) { isOneMonthAgreement = oneMonthAgreement; }

    public boolean getIsOneMonthAgreement() { return isOneMonthAgreement; }

    public void setIsOneMonthAgreement(boolean oneMonthAgreement) { this.isOneMonthAgreement = oneMonthAgreement; }
    public MVPackageType getMvContractPackage() { return mvPackageType; }

    public void setMvContractPackage(MVPackageType mvPackageType) { this.mvPackageType = mvPackageType; }

    public ContractSource getSource() { return source; }

    public void setSource(ContractSource source) {
        this.source = source;
    }

    public boolean getLiveOut() { return isLiveOut; }

    public void setLiveOut(boolean liveOut) { isLiveOut = liveOut; }

    public int getWaivedMonths() { return waivedMonths; }

    public void setWaivedMonths(int waivedMonths) { this.waivedMonths = waivedMonths; }

    public Date getFreezingDate() { return freezingDate; }

    public void setFreezingDate(Date freezingDate) { this.freezingDate = freezingDate; }


    public boolean isExcludeFromExpiryFlow() { return excludeFromExpiryFlow; }

    public void setExcludeFromExpiryFlow(boolean excludeFromExpiryFlow) {
        this.excludeFromExpiryFlow = excludeFromExpiryFlow;
    }

    public ReceivePaymentNotificationsStatus getReceivePaymentNotifications() { return receivePaymentNotifications; }

    public void setReceivePaymentNotifications(ReceivePaymentNotificationsStatus receivePaymentNotifications) {
        this.receivePaymentNotifications = receivePaymentNotifications;
    }


    public Boolean getSpecialProRated() { return specialProRated; }

    public void setSpecialProRated(Boolean specialProRated) { this.specialProRated = specialProRated; }

    public boolean getDdGenerationNextMonth() {
        String vATProjectDateStr = Setup.getParameter(Setup.getModule("clientmgmt"),"CM_1501_DEPLOYMENT_DATE");        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date deploymentDate = null;
        try {
            deploymentDate = dateFormat.parse(vATProjectDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }

        if(deploymentDate == null){
            Logger.getLogger(Contract.class.getName()).log(Level.SEVERE, null, "");
            logger.log(Level.SEVERE, "Parameter CM_1501_DEPLOYMENT_DATE " + " is null");
            return false;

        }
        return (getContractProspectType()!=null && getContractProspectType().getCode().equals("maidvisa.ae_prospect") &&
                getStartOfContract()!=null &&
                DateUtil.checkIfInSameDay(getStartOfContract() , DateUtil.getMonthEnd(getStartOfContract())) &&
                getCreationDate()!=null && getCreationDate().getTime() > deploymentDate.getTime()
        );
    }

    public void setIsProRated(Boolean isProRated) {
        this.isProRated = isProRated;
    }

    @NotAudited
    @Formula("(EXISTS(SELECT 1 FROM BASEADDITIONALINFOS B " +
             "WHERE B.OWNER_ID = ID AND B.OWNER_TYPE = 'Contract' AND " +
                "B.INFO_KEY = 'withVatOnSalary' AND B.INFO_VALUE = 'true'))")
    @Basic(fetch = FetchType.LAZY)
    @JsonIgnore
    private boolean workerSalaryVatted;

    public void setWorkerSalaryVatted(boolean workerSalaryVatted) {
        this.workerSalaryVatted = workerSalaryVatted;
    }

    public boolean isWorkerSalaryVatted() {
        return workerSalaryVatted;
    }

//    @BeforeUpdate
//    public void fixAdjEndDate() throws Exception{
//        Boolean notFixAdjustedEndDate = (Boolean) CurrentRequest.getPropertyFromCurrentOperation("notFixAdjustedEndDate", Boolean.class);
//        if(notFixAdjustedEndDate!=null && notFixAdjustedEndDate)
//            return;
//        if (this.getAdjustedEndDate() == null && !this.getPayments().isEmpty()) {
//            Setup.getApplicationContext()
//                    .getBean(ContractService.class)
//                    .fixAdjustedEndDate(this, false);
//        }
//        if (this.getAdjustedEndDate() == null && !this.getPayments().isEmpty())
//            throw new BusinessException("Adjusted end date can't be null");
//    }
}
