package com.magnamedia.entity.dto;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.service.ConsumptionRateService;

import java.math.BigDecimal;

/**
 * <PERSON> (Feb 02, 2021)
 */
public class PurchaseItemDtoForConfirmRequest {

    public PurchaseItemDtoForConfirmRequest(PurchaseItem purchaseItem) {
        this.id = purchaseItem.getId();
        this.itemId = purchaseItem.getItem().getId();
        this.name = purchaseItem.getItem().getName();
        this.unitOfMeasure = purchaseItem.getItem().getUnitOfMeasureShortName();
        this.quantity = purchaseItem.getQuantity();
        this.actualConsumption = purchaseItem.getActualConsumption();
        this.theoreticalConsumption = purchaseItem.getTheoreticalConsumption();
        this.initialCycleInventory = purchaseItem.getItem().getInitialCycleInventory();
        this.firstInitialCycleInventory = purchaseItem.getInitialCycleInventory();
        this.currentInventoryLevel = purchaseItem.getItem().getQuantity();
        if (actualConsumption != null && theoreticalConsumption != null)
            this.wasItOverConsumed = actualConsumption.compareTo(theoreticalConsumption.multiply(new BigDecimal("1.2"))) == 1;
        this.consumptionRate = purchaseItem.getConsumptionRate();
        this.updatedConsumptionRate = purchaseItem.getUpdatedConsumptionRate() != null ? purchaseItem.getUpdatedConsumptionRate() : this.consumptionRate;
    }

    private Long id;
    private Long itemId;
    private String name;
    private BigDecimal quantity;
    private String unitOfMeasure;
    private BigDecimal actualConsumption;
    private BigDecimal theoreticalConsumption;
    private BigDecimal initialCycleInventory;
    private BigDecimal currentInventoryLevel;
    private boolean wasItOverConsumed;
    private BigDecimal firstInitialCycleInventory;
    private BigDecimal consumptionRate;
    private BigDecimal updatedConsumptionRate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public BigDecimal getActualConsumption() {
        return actualConsumption;
    }

    public void setActualConsumption(BigDecimal actualConsumption) {
        this.actualConsumption = actualConsumption;
    }

    public BigDecimal getTheoreticalConsumption() {
        return theoreticalConsumption;
    }

    public void setTheoreticalConsumption(BigDecimal theoreticalConsumption) {
        this.theoreticalConsumption = theoreticalConsumption;
    }

    public BigDecimal getInitialCycleInventory() {
        return initialCycleInventory;
    }

    public void setInitialCycleInventory(BigDecimal initialCycleInventory) {
        this.initialCycleInventory = initialCycleInventory;
    }

    public BigDecimal getCurrentInventoryLevel() {
        return currentInventoryLevel;
    }

    public void setCurrentInventoryLevel(BigDecimal currentInventoryLevel) {
        this.currentInventoryLevel = currentInventoryLevel;
    }

    public boolean isWasItOverConsumed() {
        return wasItOverConsumed;
    }

    public BigDecimal getFirstInitialCycleInventory() {
        return firstInitialCycleInventory;
    }

    public void setFirstInitialCycleInventory(BigDecimal firstInitialCycleInventory) {
        this.firstInitialCycleInventory = firstInitialCycleInventory;
    }

    public void setWasItOverConsumed(boolean wasItOverConsumed) {
        this.wasItOverConsumed = wasItOverConsumed;
    }
    
    public BigDecimal getConsumptionRate() {
        return consumptionRate;
    }

    public void setConsumptionRate(BigDecimal consumptionRate) {
        this.consumptionRate = consumptionRate;
    }

    public BigDecimal getUpdatedConsumptionRate() {
        return updatedConsumptionRate;
    }

    public void setUpdatedConsumptionRate(BigDecimal updatedConsumptionRate) {
        this.updatedConsumptionRate = updatedConsumptionRate;
    }
}
