package com.magnamedia.entity.dto;

import com.magnamedia.entity.ExpenseRelatedTo;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class ExpenseRequestRefundSearchDto {
    private Long refundId;
    private Long expenseId;

    private Boolean refundConfirmed;

    private String expenseAmountOperator;
    private Double expenseAmount;

    private String refundAmountOperator;
    private Double refundAmount;

    private Long supplierId;

    private ExpenseRelatedTo.ExpenseRelatedToType relatedToType;
    private Long relatedToId;

    private String expenseDateOperator;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expenseDate1;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expenseDate2;

    private String refundDateOperator;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date refundDate1;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date refundDate2;

    private Long expenseTransaction;
    private String expenseName;

    public Long getRefundId() {
        return refundId;
    }

    public void setRefundId(Long refundId) {
        this.refundId = refundId;
    }

    public Long getExpenseId() {
        return expenseId;
    }

    public void setExpenseId(Long expenseId) {
        this.expenseId = expenseId;
    }

    public String getExpenseAmountOperator() {
        return expenseAmountOperator;
    }

    public void setExpenseAmountOperator(String expenseAmountOperator) {
        this.expenseAmountOperator = expenseAmountOperator;
    }

    public Double getExpenseAmount() {
        return expenseAmount;
    }

    public void setExpenseAmount(Double expenseAmount) {
        this.expenseAmount = expenseAmount;
    }

    public String getRefundAmountOperator() {
        return refundAmountOperator;
    }

    public void setRefundAmountOperator(String refundAmountOperator) {
        this.refundAmountOperator = refundAmountOperator;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public ExpenseRelatedTo.ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public String getExpenseDateOperator() {
        return expenseDateOperator;
    }

    public void setExpenseDateOperator(String expenseDateOperator) {
        this.expenseDateOperator = expenseDateOperator;
    }

    public Date getExpenseDate1() {
        return expenseDate1;
    }

    public void setExpenseDate1(Date expenseDate1) {
        this.expenseDate1 = expenseDate1;
    }

    public Date getExpenseDate2() {
        return expenseDate2;
    }

    public void setExpenseDate2(Date expenseDate2) {
        this.expenseDate2 = expenseDate2;
    }

    public String getRefundDateOperator() {
        return refundDateOperator;
    }

    public void setRefundDateOperator(String refundDateOperator) {
        this.refundDateOperator = refundDateOperator;
    }

    public Date getRefundDate1() {
        return refundDate1;
    }

    public void setRefundDate1(Date refundDate1) {
        this.refundDate1 = refundDate1;
    }

    public Date getRefundDate2() {
        return refundDate2;
    }

    public void setRefundDate2(Date refundDate2) {
        this.refundDate2 = refundDate2;
    }

    public Boolean getRefundConfirmed() {
        return refundConfirmed;
    }

    public void setRefundConfirmed(Boolean refundConfirmed) {
        this.refundConfirmed = refundConfirmed;
    }

    public Long getExpenseTransaction() {
        return expenseTransaction;
    }

    public void setExpenseTransaction(Long expenseTransaction) {
        this.expenseTransaction = expenseTransaction;
    }

    public String getExpenseName() {
        return expenseName;
    }

    public void setExpenseName(String expenseName) {
        this.expenseName = expenseName;
    }
}
