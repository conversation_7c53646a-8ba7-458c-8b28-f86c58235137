package com.magnamedia.entity.dto;

import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class DDFSendDDToBankDto {
    private Long id;
    private Map<String, Object> directDebit;
    private String applicationId;
    private String ddfNotes;
    private Date startDate;
    private Date creationDate;
    private Double amount;
    private Long paymentsCount;
    private String bankName;
    private String notes;
    private String type;
    private List<Map> attachments;

    public DDFSendDDToBankDto(Map ddf) {
        this.id = (Long) ddf.get("ddfId");
        this.ddfNotes = (String) ddf.get("ddfNotes");
        this.directDebit = new HashMap<String, Object>() {{
            put("id", ddf.get("ddId"));
            put("contractPaymentTerm", new HashMap<String, Object>() {{
                put("id", ddf.get("ddCptId"));
                put("isActive", ddf.get("ddCptIsActive"));
                put("contract", new HashMap<String, Object>() {{
                    put("id", ddf.get("ddContractId"));
                    put("client", new HashMap<String, Object>() {{
                        put("id", ddf.get("ddClientId"));
                        put("name", ddf.get("ddClientName"));
                    }});
                }});
            }});
        }};
        this.applicationId = (String) ddf.get("applicationId");
        this.startDate = (Date) ddf.get("startDate");
        this.creationDate = (Date) ddf.get("creationDate");
        this.amount = (Double) ddf.get("amount");

        this.type = (ddf.get("ddMethod") != null ? ((DirectDebitMethod) ddf.get("ddMethod")).getLabel() : "") + " " +
                (ddf.get("ddFrequency") != null ? ((DirectDebitType) ddf.get("ddFrequency")).getLabel() : "");
        this.bankName = (String) ddf.get("bankName");
        this.notes = (String) ddf.get("notes");
    }
}