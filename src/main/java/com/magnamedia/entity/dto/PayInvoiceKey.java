package com.magnamedia.entity.dto;

import com.magnamedia.entity.Expense;

import java.util.Objects;

/**
 * <PERSON> (Feb 13, 2021)
 */
public class PayInvoiceKey {
    public Long beneficiaryId;
    public Expense expense;

    public PayInvoiceKey(Long beneficiaryId, Expense expense) {
        this.beneficiaryId = beneficiaryId;
        this.expense = expense;
    }

    @Override
    public int hashCode() {
        return Objects.hash(beneficiaryId, expense);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PayInvoiceKey that = (PayInvoiceKey) o;
        return Objects.equals(beneficiaryId, that.beneficiaryId) &&
                Objects.equals(expense, that.expense);
    }
}

