package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Expense;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 12, 2020
 */
public class ExpenseIdLabelCodeCaptionSerializer extends JsonSerializer<Expense> {

    @Override
    public void serialize(Expense value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException, JsonProcessingException {
            if (value == null) {
                    gen.writeNull();
                    return;
            }
            gen.writeStartObject();
            gen.writeNumberField("id", value.getId());
            gen.writeStringField("label", value.getName());
            gen.writeStringField("name", value.getName());
            gen.writeStringField("code", value.getCode());
            gen.writeStringField("caption", value.getCaption());
        // todo add serializor for ApprovalMethod
            gen.writeEndObject();
    }
}
