package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on May 13, 2019
 * Jirra ACC-674
 */
@Entity
@Table(
        indexes = {
            @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class RecalculateSalaryRequest extends BaseEntity  {

    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    
    @Column
    private Date refreshDate;
    
    @Column
    private String reason;
    
    @Column(columnDefinition = "boolean default false")
    private boolean done;
    
    //Jirra ACC-1007
    @Column(columnDefinition = "boolean default false")
    private boolean withReturnToPayroll;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Date getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(Date refreshDate) {
        this.refreshDate = refreshDate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public boolean isDone() {
        return done;
    }

    public void setDone(boolean done) {
        this.done = done;
    }

    public boolean isWithReturnToPayroll() {
        return withReturnToPayroll;
    }

    public void setWithReturnToPayroll(boolean withReturnToPayroll) {
        this.withReturnToPayroll = withReturnToPayroll;
    }
    
}
