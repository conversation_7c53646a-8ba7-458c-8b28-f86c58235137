package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.Entity;

@Entity
public class UnpaidLeaveExpense extends VisaExpense<UnpaidLeaveRequest> {

    public UnpaidLeaveExpense() {
        super(null, null);
    }

    public UnpaidLeaveExpense(UnpaidLeaveRequest request, ExpensePurpose purpose) {
        super(request, purpose);
    }

    @Override
    public String getVisaExpenseType() {
        return "UnpaidLeaveExpense";
    }
}