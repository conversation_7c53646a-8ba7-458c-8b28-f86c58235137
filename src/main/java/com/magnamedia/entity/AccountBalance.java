package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.extra.FilterItem;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

@Entity
public class AccountBalance extends BaseEntity {

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @ManyToOne
    private Bucket bucket;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Revenue revenue;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Expense expense;

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date toDate;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fromDate;

    @Transient
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private List<FilterItem> selectFilters;

    @Column
    private String filterItemsAsString;

    @Column
    private Double actualValue;

    public Bucket getBucket() {
        return bucket;
    }

    public void setBucket(Bucket bucket) {
        this.bucket = bucket;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public List<FilterItem> getSelectFilters() {
        if (selectFilters == null) return new ArrayList<>();

        return selectFilters;
    }

    public void setSelectFilters(List<FilterItem> selectFilters) {
        this.selectFilters = selectFilters;
    }

    public String getFilterItemsAsString() {
        return filterItemsAsString;
    }

    public void setFilterItemsAsString(String filterItemsAsString) {
        this.filterItemsAsString = filterItemsAsString;
    }

    public Double getActualValue() {
        return actualValue;
    }

    public void setActualValue(Double actualValue) {
        this.actualValue = actualValue;
    }
}
