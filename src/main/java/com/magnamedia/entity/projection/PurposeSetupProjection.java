package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.ClientRefundSetup;
import com.magnamedia.entity.ComplaintType;
import com.magnamedia.entity.serializer.IdLabelListSerializer;

import java.util.List;

/**
 *
 * <AUTHOR> k<PERSON>raw<PERSON>
 */
public interface PurposeSetupProjection {

    Long getId();

    @JsonSerialize(using = IdLabelListSerializer.class)
    List<User> getRequestedBy();

    @JsonSerialize(using = IdLabelSerializer.class)
    User getApprovedBy();

    Boolean getLinkComplaint();

    @JsonSerialize(using = IdLabelListSerializer.class)
    List<ComplaintType> getComplaintTypes();

    Boolean getValidateClientBank();

    List<PicklistItem> getBanks();

    Boolean getAutoApproved();

    Boolean getAllowMonthlyRefunds();

    Boolean getRequireAttachment();

    Integer getLimitForCeoApproval();

}
