package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.serializer.ContractJsonSerializer;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/26/2020
 **/
public interface ExpenseAndBucketTransactionProjection {

    public Long getId();

    public Date getDate();

    public Double getTransactionAmount();

    @Value("#{target.getVatAmount() != null ? target.getVatAmount().longValue() : 0 }")
    public Double getVatAmount();

    public VatType getVatType();

    public String getExpenseCode();

    public String getDescription();

    public String getReason();

    public String getForEntity();

    public String getToBucket();

    public String getFromBucket();

    public String getPaymentDetail();

    public boolean isResolved();

    @JsonSerialize(using = ContractJsonSerializer.class)
    public Contract getContract();

    @JsonSerialize(using = IdSerializer.class)
    public Transaction getTransaction();

    @JsonSerialize(using = IdSerializer.class)
    public Payment getPayment();

    String getNote();

    String getErpObjectId();

    String getForName();

}
