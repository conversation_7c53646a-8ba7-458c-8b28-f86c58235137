package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.PaymentMethod;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 09, 2020
 *         Jirra ACC-1435
 */
public interface ContractPaymentSalesProjection {

    Long getId();

    List<Attachment> getAttachments();

    boolean isIsTrasient();

    Double getVatPercent();

    Double getVat();

    Double getPaymentWithoutVAT();

    @JsonSerialize(using = IdLabelSerializer.class)
    ContractPaymentTerm getContractPaymentTerm();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getPaymentType();

    PaymentMethod getPaymentMethod();

    Double getAmount();

    Date getDate();

    String getDescription();

    String getDescriptionForSigningScreen();

    Boolean getConfirmed();

    Double getDiscountAmount();

    Boolean getIsCalculated();

    Boolean getIsProRated();

    @Value("#{target.getDirectDebit() != null ? "
            + "{id: target.getDirectDebit().getId(), "
            + "label: target.getDirectDebit().getLabel(), "
            + "status: target.getDirectDebit().getStatus(), "
            + "nonCompletedInfo: target.getDirectDebit().getNonCompletedInfo()}"
            + ": null}")
    Map<?, ?> getDirectDebit();

    @JsonSerialize(using = IdLabelSerializer.class)
    Payment getReplaceOf();

    Double getAdditionalDiscountAmount();

    Boolean getIsInitial();

    Double getVisaFees();

    Double getVisaFeesWithoutVAT();

    Boolean getAddedByAccountant();

    Boolean getIncludeWorkerSalary();

    boolean isOneTime();

    String getUuid();

    long getVersion();

    Date getLastModificationDate();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();

}
