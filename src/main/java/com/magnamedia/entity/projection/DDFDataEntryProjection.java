package com.magnamedia.entity.projection;


import com.magnamedia.module.type.DirectDebitRejectCategory;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 17, 2020
 *         Jirra ACC-2330
 */

public interface DDFDataEntryProjection {

    Long getId();

    String getNotes();

    Date getCreationDate();

    @Value("#{target.getDirectDebit() != null ? "
            + "{id: target.getDirectDebit().getId(), "
            + "dataEntryNotes: target.getDirectDebit().getDataEntryNotes(), "
            + "contractPaymentTerm: target.getDirectDebit().getContractPaymentTerm() != null ? "
            + "{id: target.getDirectDebit().getContractPaymentTerm().getId(), "
            + "isActive:target.getDirectDebit().getContractPaymentTerm().isIsActive(), "
            + "contract: target.getDirectDebit().getContractPaymentTerm().getContract() != null ? "
            + "{id: target.getDirectDebit().getContractPaymentTerm().getContract().getId(), "
            + "contractProspectType: target.getDirectDebit().getContractPaymentTerm().getContract().getContractProspectType(), "
            + "client: target.getDirectDebit().getContractPaymentTerm().getContract().getClient() != null ? "
            + "{id: target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getId(), "
            + "name: target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()} : null} : null} "
            + ": null} "
            + ": null}")
    Map<?, ?> getDirectDebit();

    Boolean getNeedAccountantReConfirmation();

    DirectDebitRejectCategory getRejectCategory();

    String getRejectionReason();
}
