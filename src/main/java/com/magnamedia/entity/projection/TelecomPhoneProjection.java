package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.TelecomPhoneBill;
import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 1, 2018
 */
public interface TelecomPhoneProjection {
    
    Long getId();
    
    String getName();
    
    String getNumber();
    
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    Expense getPrimaryExpense();
    
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    Expense getSecondryExpense();
    
    Date getDueEvery();
    
    String getHolders() ;
    
    String getUsageText();
    
    PicklistItem getServiceType();
    
    PicklistItem getPaymentMethod();
    
    //List<TelecomPhoneBill> getTelecomPhoneBills();
    
    @Value("#{(target.getLastTelecomPhoneBill() != null)?"
            + "({id:target.getLastTelecomPhoneBill().getId(),"
            + "name:target.getLastTelecomPhoneBill().getName(),"
            + "amount:target.getLastTelecomPhoneBill().getAmount(),"
            + "billDate:target.getLastTelecomPhoneBill().getBillDate(),"
            + "billMonth:(target.getLastTelecomPhoneBill().getBillDate() != null ? (new org.joda.time.LocalDate(target.getLastTelecomPhoneBill().getBillDate())).monthOfYear().getAsText() : null)})"
            + ":({id:(\"\")})}")
    Map<?, ?> getLastTelecomPhoneBill();
    
    String getNotes();

    public Boolean getActive();

    public Boolean getDeleted();
}
