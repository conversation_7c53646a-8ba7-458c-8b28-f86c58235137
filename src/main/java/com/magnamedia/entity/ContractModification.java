package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;
import java.util.List;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR>
 */
@Entity
public class ContractModification extends VisaRequest<ContractModification, ContractModificationNote, ContractModificationExpense> {

    public enum ContractModificationCreationBy{
        INIT_OEC_REQUEST,
        AFTER_FINISH_GET_PASSPORT_PROCESS,
        AFTER_FINISH_ANEND_DDS_PROCESS,
        OTHER
    }

    public ContractModification(String startTaskName) {
        super(startTaskName);
    }

    public ContractModification() {
        super("");
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

}
