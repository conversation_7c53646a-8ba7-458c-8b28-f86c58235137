package com.magnamedia.entity.interfaces;

import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.module.type.PLNodeType;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.RevenueRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

public interface BasePLVariableNode<T extends BasePLVariableBucket> {

    Long getId();

    BaseReportCompany getPLCompany();

    String getName();

    PLNodeType getpLNodeType();

    BasePLNode getParent();

    void setFormula(String formula);

    List<T> getpLVariableBuckets();

    void setpLVariableBuckets(List<T> pLVariableBuckets);

    BaseCompanyReportController.SearchCriteria getSearchCriteria();

    default String getFormula() {
        String formula = "";
        for (T pLVariableBucket : getpLVariableBuckets()) {
            if (pLVariableBucket.getExpense() != null) {
                formula += (pLVariableBucket.getWieght() >= 0 ? "+ " : "- ") + Math.abs(pLVariableBucket.getWieght())
                        + " " + pLVariableBucket.getExpense().getCode() + " ";
            } else if (pLVariableBucket.getRevenue() != null) {
                formula += (pLVariableBucket.getWieght() >= 0 ? "+ " : "- ") + Math.abs(pLVariableBucket.getWieght())
                        + " " + pLVariableBucket.getRevenue().getCode() + " ";
            }
        }
        return formula;
    }

    default Double calculateAndSetValue(Date fromDate, Date toDate) {

        Double result = 0.0;
        for (T plVariableBucket : getpLVariableBuckets()) {
            Double amountToAdd = 0D;
            //Jirra ACC-306
            if (plVariableBucket.getExpense() != null) {
                amountToAdd = calculateExpensesBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

                Double accrualValue = calculateAccrualExpensesBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

                if (amountToAdd == null)
                    amountToAdd = 0.0D;
                if (accrualValue != null)
                    amountToAdd += accrualValue;
            } else if (plVariableBucket.getRevenue() != null) {
                amountToAdd = calculateRevenuesBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);

                Double accrualValue = calculateAccrualRevenuesBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);

                if (amountToAdd == null)
                    amountToAdd = 0D;
                if (accrualValue != null)
                    amountToAdd += accrualValue;
            }
            if (amountToAdd != null)
                result += plVariableBucket.getWieght() * amountToAdd;
        }

        return result;
    }

    default Double calculateExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateExpensesBetweenTwoPnlDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateExpensesBetweenTwoTransactionDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateAccrualExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateAccrualExpensesBetweenTwoDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateAccrualExpensesBetweenTwoDates(expense, transactionDate1, transactionDate2);
        }


        return 0D;
    }

    default Double calculateRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateRevenuesBetweenTwoPnlDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateRevenuesBetweenTwoTransactionDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateAccrualRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateAccrualRevenuesBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateAccrualRevenuesBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    //Jirra ACC-2500
    default Double calculateVatAmount(Date fromDate, Date toDate) {

        Double result = 0.0;
        for (T plVariableBucket : getpLVariableBuckets()) {
            Double vatAmountToAdd = 0D;
            if (plVariableBucket.getExpense() != null) {
                vatAmountToAdd = calculateExpensesVatAmountBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

            } else if (plVariableBucket.getRevenue() != null) {
                vatAmountToAdd = calculateRevenuesVatAmountBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);
            }
            if (vatAmountToAdd != null)
                result += plVariableBucket.getWieght() * vatAmountToAdd;
        }

        return result;
    }

    default Double calculateExpensesVatAmountBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateExpensesVatAmountBetweenTwoPnlDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateExpensesVatAmountBetweenTwoTransactionDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateRevenuesVatAmountBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateRevenuesVatAmountBetweenTwoPnlDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateRevenuesVatAmountBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateProfitAdjustmentValue(Date fromDate, Date toDate) {
        Double result = 0.0;

        for (T plVariableBucket : getpLVariableBuckets()) {
            Double profitAdjustment = 0.0;
            if (plVariableBucket.getExpense() != null) {
                Double temp = calculateExpensesProfitAdjustmentBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

                if (temp != null)
                    profitAdjustment += temp;

            } else if (plVariableBucket.getRevenue() != null) {
                Double temp = calculateRevenuesProfitAdjustmentBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);

                if (temp != null)
                    profitAdjustment += temp;
            }

            result += plVariableBucket.getWieght() * profitAdjustment;
        }

        return result;
    }

    default Double calculateExpensesProfitAdjustmentBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateExpensesProfitAdjustmentBetweenTwoDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateExpensesProfitAdjustmentBetweenTwoDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateRevenuesProfitAdjustmentBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateRevenuesProfitAdjustmentBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateRevenuesProfitAdjustmentBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateAverageValue(Date fromDate, Date toDate) {
        Double result = 0.0;

        for (T plVariableBucket : getpLVariableBuckets()) {
            Double average = 0.0;
            if (plVariableBucket.getExpense() != null) {
                Double temp = calculateExpensesAverageBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

                if (temp != null)
                    average += temp;

            } else if (plVariableBucket.getRevenue() != null) {
                Double temp = calculateRevenuesAverageBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);

                if (temp != null)
                    average += temp;
            }
            result += plVariableBucket.getWieght() * average;
        }

        return result;
    }

    default Double calculateExpensesAverageBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateExpensesAverageBetweenTwoDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateExpensesAverageBetweenTwoDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateRevenuesAverageBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateRevenuesAverageBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateRevenuesAverageBetweenTwoDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    //Jirra ACC-804
    default Double calculateOutputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;
        for (T plVariableBucket : getpLVariableBuckets()) {
            Double amountToAdd = 0.0D;
            Double weight = 1D;
            if (plVariableBucket.getRevenue() != null) {
                amountToAdd = calculateOutputVatRevenuesBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);
                weight = plVariableBucket.getWieght();
            }
            //Jirra ACC-947
            if (plVariableBucket.getExpense() != null) {
                amountToAdd = calculateOutputVATExpensesBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);

                weight = plVariableBucket.getWieght();
            }

            if (amountToAdd != null)
                result += weight * amountToAdd;
        }
        return result;
    }

    default Double calculateOutputVatRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateOutputVatRevenuesBetweenTwoPnlDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateOutputVatRevenuesBetweenTwoTransactionDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateOutputVATExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateOutputVATExpensesBetweenTwoPnlDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateOutputVATExpensesBetweenTwoTransactionDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    //Jirra ACC-804
    default Double calculateInputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;

        for (T plVariableBucket : getpLVariableBuckets()) {
            Double amountToAdd = 0.0D;
            //Jirra ACC-947
            if (plVariableBucket.getRevenue() != null) {
                amountToAdd = calculateInputVatRevenuesBetweenTwoPnlDates(
                        plVariableBucket.getRevenue(),
                        fromDate, toDate);
            }
            if (plVariableBucket.getExpense() != null) {
                amountToAdd = calculateInputVATExpensesBetweenTwoPnlDates(
                        plVariableBucket.getExpense(),
                        fromDate, toDate);
            }
            if (amountToAdd != null)
                result += plVariableBucket.getWieght() * amountToAdd;
        }
        return result;
    }

    default Double calculateInputVatRevenuesBetweenTwoPnlDates(Revenue revenue, Date transactionDate1, Date transactionDate2) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.calculateInputVatRevenuesBetweenTwoPnlDates(revenue, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.calculateInputVatRevenuesBetweenTwoTransactionDates(revenue, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    default Double calculateInputVATExpensesBetweenTwoPnlDates(Expense expense, Date transactionDate1, Date transactionDate2) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.calculateInputVATExpensesBetweenTwoPnlDates(expense, transactionDate1, transactionDate2);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.calculateInputVATExpensesBetweenTwoTransactionDates(expense, transactionDate1, transactionDate2);
        }

        return 0D;
    }

    // ACC-496 2) Get all transactions behind a row in P&Ls page | Majd Bousaad
    default List<Transaction> getTransactionsBehindNode(Date fromDate, Date toDate) {

        List<Transaction> transactions = new ArrayList();

        getpLVariableBuckets().stream().forEach((pLVariableBucket) -> {
            if (pLVariableBucket.getExpense() != null) {
                transactions.addAll(getExpenseTransactionsBetweenTwoPnlDates(pLVariableBucket.getExpense(), fromDate, toDate));
            } else if (pLVariableBucket.getRevenue() != null) {
                transactions.addAll(getRevenueTransactionsBetweenTwoPnlDates(pLVariableBucket.getRevenue(), fromDate, toDate));
            }
        });

        return transactions;
    }

    default List<Transaction> getExpenseTransactionsBetweenTwoPnlDates(Expense expense, Date fromDate, Date toDate) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.getExpenseTransactionsBetweenTwoPnlDates(expense, fromDate, toDate);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.getExpenseTransactionsBetweenTwoTransactionDates(expense, fromDate, toDate);
        }

        return new ArrayList();
    }

    default List<Transaction> getRevenueTransactionsBetweenTwoPnlDates(Revenue revenue, Date fromDate, Date toDate) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.getRevenueTransactionsBetweenTwoPnlDates(revenue, fromDate, toDate);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.getRevenueTransactionsBetweenTwoTransactionDates(revenue, fromDate, toDate);
        }

        return new ArrayList();
    }

    default List<TransactionDetails> getTransactionDetailsBehindNode(Date fromDate, Date toDate) {

        List<TransactionDetails> transactionDetails = new ArrayList<>();

        getpLVariableBuckets().stream().forEach((pLVariableBucket) -> {
            if (pLVariableBucket.getExpense() != null) {
                transactionDetails.addAll(getExpenseTransactionDetailsBetweenTwoDates(pLVariableBucket.getExpense(), fromDate, toDate));
            } else if (pLVariableBucket.getRevenue() != null) {
                transactionDetails.addAll(getRevenueTransactionDetailsBetweenTwoDates(pLVariableBucket.getRevenue(), fromDate, toDate));
            }
        });

        return transactionDetails;
    }

    default List<TransactionDetails> getExpenseTransactionDetailsBetweenTwoDates(Expense expense, Date fromDate, Date toDate) {
        ExpenseRepository expenseRepository = Setup.getApplicationContext().getBean(ExpenseRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return expenseRepository.getExpenseTransactionDetailsBetweenTwoDates(expense, fromDate, toDate);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return expenseRepository.getExpenseTransactionDetailsBetweenTwoDates(expense, fromDate, toDate);
        }

        return new ArrayList();
    }

    default List<TransactionDetails> getRevenueTransactionDetailsBetweenTwoDates(Revenue revenue, Date fromDate, Date toDate) {
        RevenueRepository revenueRepository = Setup.getApplicationContext().getBean(RevenueRepository.class);

        if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.PNL_DATE)) {
            return revenueRepository.getRevenueTransactionDetailsBetweenTwoDates(revenue, fromDate, toDate);
        } else if (getSearchCriteria().equals(BaseCompanyReportController.SearchCriteria.ACTUAL_DATE)) {
            return revenueRepository.getRevenueTransactionDetailsBetweenTwoDates(revenue, fromDate, toDate);
        }

        return new ArrayList();
    }

    default void validate() {

        if (getParent() == null)// || getParent().getId() == null)
            throw new RuntimeException("parent should not be empty.");

        if (getpLVariableBuckets().stream().anyMatch(x -> x.getWieght() < -1 || x.getWieght() > 1))
            throw new RuntimeException("All Buckets weights should be between -1 and +1.");
    }
}
