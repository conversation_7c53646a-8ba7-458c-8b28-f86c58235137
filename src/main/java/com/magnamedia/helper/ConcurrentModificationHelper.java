package com.magnamedia.helper;

import org.joda.time.DateTime;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Oct 14, 2020
 *         Jirra ACC-2522
 */

public class ConcurrentModificationHelper {
    protected static final Logger logger = Logger.getLogger(ConcurrentModificationHelper.class.getName());

    private static Map<String, List<Long>> sharedData = new HashMap();
    private static ConcurrentHashMap<String, Object> globalSharedData = new ConcurrentHashMap<>();

    public static void lock(String key, Long id) {
        logger.info("Locking Key: " + key + ", Value: " + id);
        List<Long> values;
        if (sharedData.containsKey(key)) {
            values = sharedData.get(key);
        } else {
            values = new ArrayList();
        }

        values.add(id);
        sharedData.put(key, values);
    }

    public static void unLock(String key, Long id) {
        if (sharedData.contains<PERSON><PERSON>(key)) {
            List<Long> values = sharedData.get(key);
            values.remove(id);
        }
    }

    public static boolean isLocked(String key, Long id) {
        return sharedData.containsKey(key) && sharedData.get(key).contains(id);
    }

    public static void lockOrThrowException(String key, Long id) {

        if (ConcurrentModificationHelper.isLocked(key, id)) {
            throw new ConcurrentModificationException("Key: " + key + ", Value: " + id);
        }

        ConcurrentModificationHelper.lock(key, id);
    }

    //global shared data
    public static boolean isExistInGlobalData(String key) { return globalSharedData.containsKey(key); }

    public static void lockGlobalDataNewKey(String key, Object value) { globalSharedData.put(key,value); }

    public static void unLockGlobalDataKey(String key) { globalSharedData.remove(key); }

    public static Object getValueFromGlobalData(String key) { return globalSharedData.get(key); }

    public static void emptyExpiredKeys() {
        List<String> keysToRemove = new ArrayList<>();
        for (Map.Entry<String, Object> entry : globalSharedData.entrySet()) {
            if (entry.getValue() instanceof Map && ((Map<?, ?>) entry.getValue()).containsKey("deletedDate")) {
                Map<String, Object> valueMap = (Map<String, Object>) entry.getValue();
                if (new DateTime().isAfter((DateTime) valueMap.get("deletedDate"))) {
                    keysToRemove.add(entry.getKey());
                }
            }
        }

        for (String key : keysToRemove) {
            unLockGlobalDataKey(key);
        }
    }
}
