package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.OneMonthAgreementFlowService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static java.util.Calendar.YEAR;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 29, 2017
 */
public class DateUtil {

    private static final Logger logger = Logger.getLogger(DateUtil.class.getName());

    public static boolean isThisDateValid(Object dateToValidate, String dateFromat) {

        if ((dateToValidate == null) || (!(dateToValidate instanceof String))) {
            return false;
        }

        String dateToValidateStr = (String) dateToValidate;
        SimpleDateFormat sdf = new SimpleDateFormat(dateFromat);
        sdf.setLenient(false);

        try {
            //if not valid, it will throw ParseException
            Date d = sdf.parse(dateToValidateStr);
            System.out.println(d);

        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static String getDateFormatDDMM(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd MMMM");
        return sdf.format(date);
    }

    public static String getTimeFormatHHMMA(Date date) {
        return simpleTimeFormatAMPM.format(date);
    }

    public static Date getStartDayValue(Date dateValue) {
        DateTime dateTimeValue = new DateTime(dateValue);
        return new Date(dateTimeValue.withTimeAtStartOfDay().getMillis());
    }

    public static Date getEndDayValue(Date dateValue) {
        DateTime dateTimeValue = new DateTime(dateValue).plusDays(1);
        return new Date(dateTimeValue.withTimeAtStartOfDay().getMillis() - 1);
    }

    public static Date getFirstOfMonthDate() {
        return new DateTime().withDayOfMonth(1).withTimeAtStartOfDay().toDate();
    }

    public static Date getEndOfMonthDate() {
        return getEndDayValue(new DateTime().dayOfMonth().withMaximumValue().toDate());
    }

    private static final DateFormat fullDateFormat = new SimpleDateFormat(
            "dd MMMM, yyyy");

    private static final DateFormat clientFullDateFormat = new SimpleDateFormat(
            "EEEE, MMM dd, yyyy");

    public static final DateFormat bucketEmailDateFormat = new SimpleDateFormat(
            "EEEE, dd MMMM, yyyy");

    private static final DateFormat fullDateFormatWithTime = new SimpleDateFormat(
            "dd MMMM, yyyy HH:mm:ss.SSS");

    private static final DateFormat clientFullDateFormatWithTime = new SimpleDateFormat(
            "EEEE, MMM dd, yyyy HH:mm:ss.SSS");

    private static final DateFormat dashedDateFormatWithTime = new SimpleDateFormat(
            "yyyy-MM-dd hh:mm:ss");

    private static final DateFormat dashedDateFormatWithTimeV2 = new SimpleDateFormat(
            "yyyy-MM-dd hh:mm:ss");

    private static final DateFormat dashedDateFormatWithTimeV3 = new SimpleDateFormat(
            "yyyy-MM-dd HH:mm:ss");

    private static final DateFormat simpleMonthYearFormat = new SimpleDateFormat(
            "MMM yyyy");

    private static final SimpleDateFormat monthDayYearFormat = new SimpleDateFormat(
            "MMM dd YYYY");

    private static final DateFormat dayOfWeekFormat = new SimpleDateFormat(
            "EEEE");

    private static final DateFormat dayMonthDayYearFormat = new SimpleDateFormat(
            "EEE, MMMMM dd,yyyy");

    private static final DateFormat monthFormat = new SimpleDateFormat(
            "MMMM");
    private static final DateFormat monthFormatWithTime = new SimpleDateFormat(
            "MMMM hh:mm:ss");
    private static final DateFormat monthFormatWithSimpleTime = new SimpleDateFormat(
            "MMMM mm:ss");
    private static final DateFormat simpleMonthFormat = new SimpleDateFormat(
            "MMM");

    private static final DateFormat dashedDateFormat = new SimpleDateFormat(
            "yyyy-MM-dd");
    private static final DateFormat dashedDateFormatV2 = new SimpleDateFormat(
            "dd-MMM-yyyy");

    private static final DateFormat slashedDateFormat = new SimpleDateFormat(
            "dd/MM/yyyy");
    private static final DateFormat slashedDateFormatV2 = new SimpleDateFormat(
            "M/d/yyyy");
    private static final DateFormat simpleTimeFormat = new SimpleDateFormat(
            "HH:mm");
    private static final DateFormat timeFormat = new SimpleDateFormat(
            "HH:mm:ss");

    private static final DateFormat simpleTimeFormatAMPM = new SimpleDateFormat(
            "HH:mm a");

    private static final DateFormat dayNumberFormat = new SimpleDateFormat("dd");

    private static final DateFormat monthNumberFormat = new SimpleDateFormat("MM");

    private static final DateFormat yearNumberFormat = new SimpleDateFormat("yy");

    private static final DateFormat yearFormat = new SimpleDateFormat("yyyy");

    private static final DateFormat notDashedFullDateFormat = new SimpleDateFormat("dd MMM yyyy");

    private static final DateFormat yearMonthFormat = new SimpleDateFormat("yyyy/MM");

    private static final DateFormat noqodiDateFormatV2 = new SimpleDateFormat(
            "MM/dd/yy hh:mm");

    private static final DateFormat amwalDateFormatV2 = new SimpleDateFormat(
            "dd/MM/yyyy hh:mm");

    public static Integer secondsBetween(Date d1, Date d2) {
        long seconds = Math.abs(d1.getTime() - d2.getTime()) / 1000;
        return (int) seconds;
    }

    public static String getMonthForInt(int num) {
        String month = "wrong";
        DateFormatSymbols dfs = new DateFormatSymbols();
        String[] months = dfs.getMonths();
        if (num >= 0 && num <= 11) {
            month = months[num];
        }
        return month;
    }

    public static Date getTodayDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date now() {
        Calendar cal = Calendar.getInstance();
        return cal.getTime();
    }

    public static Date getDateFromDateTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Integer getDaysBetween(Date date1, Date date2) {
        try {
            Integer diff = (int) TimeUnit.DAYS.convert(
                    date2.getTime() - date1
                            .getTime(), TimeUnit.MILLISECONDS);
            return diff;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static int getDaysCountOfMonth(Date date) {
        return getDaysCountOfMonth(new DateTime(date));
    }

    public static int getDaysCountOfMonth(DateTime date) {
        return YearMonth.of(date.getYear(), date.getMonthOfYear()).lengthOfMonth();
    }

    public static Date getPreviousDays(Date d, Integer days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DATE,
                -days);
        return cal.getTime();
    }

    public static Date getPreviousMonths(Date d, Integer months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.MONTH,
                -months);
        return cal.getTime();
    }

    public static  Date getFirstDateEver() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateInString = "1970-01-01";
        Date dateFrom = new Date();
        try {
            dateFrom = formatter.parse(dateInString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateFrom;
    }

    public static Date addMinutes(Date d, Integer minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.MINUTE,
                minutes);
        return cal.getTime();
    }

    public static Date addMinutesAtZeroSecond(Date d, Integer minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.MINUTE,
                minutes);
        cal.set(Calendar.SECOND,
                0);
        cal.set(Calendar.MILLISECOND,
                0);
        return cal.getTime();
    }

    public static Date addDays(Date d, Integer days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.DATE,
                days);
        return cal.getTime();
    }

    public static Date addMonths(Date d, Integer months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.MONTH,
                months);
        return cal.getTime();
    }

    public static Date addYears(Date d, Integer years) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        cal.add(Calendar.YEAR,
                years);
        return cal.getTime();
    }

    public static Date getDate(int year, int month, int day, int hour, int minute, int seconds) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, day, hour, minute, seconds);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getDate(int year, int month, int day) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, day);
        return cal.getTime();
    }

    public static String getOrdinalSuffix(Integer day) {
        if (day >= 11 && day <= 13) {
            return "th";
        }
        switch (day % 10) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    }

    //Jirra ACC-1092
    public static Date getDayStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date getDayEnd(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.HOUR_OF_DAY, c.getActualMaximum(Calendar.HOUR_OF_DAY));
        c.set(Calendar.MINUTE, c.getActualMaximum(Calendar.MINUTE));
        c.set(Calendar.SECOND, c.getActualMaximum(Calendar.SECOND));
        c.set(Calendar.MILLISECOND, c.getActualMaximum(Calendar.MILLISECOND));
        return c.getTime();
    }

    //ACC-2080
    public static boolean isInLastCoupleDaysOfMonth(Date date) {
        Integer numOfDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_NUMBER_OF_DAYS_TO_CREATE_HIDDEN_DDS));
        return isInLastXDaysOfMonth(date, numOfDays);
    }

    public static boolean isInLastXDaysOfMonth(Date date, int x) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //end of month
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endOfMonth = calendar.getTime();
        //2 days ago
        calendar.add(Calendar.DATE, -x);
        Date beforeXDays = calendar.getTime();

        return date.after(beforeXDays) && date.before(endOfMonth);
    }

    public static boolean isAfterOrEqual(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        //
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        boolean isYearAfter = calendar1.get(Calendar.YEAR) > calendar2.get(Calendar.YEAR);
        boolean isMonthAfter = calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) && calendar1.get(Calendar.MONTH) > calendar2.get(Calendar.MONTH);
        boolean isDayAfter = calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) && calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH) &&
                calendar1.get(Calendar.DAY_OF_MONTH) >= calendar2.get(Calendar.DAY_OF_MONTH);

        return isYearAfter || isMonthAfter || isDayAfter;
    }

    public static boolean isAfterOrEqualByMonth(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        //
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        boolean isYearAfter = calendar1.get(Calendar.YEAR) > calendar2.get(Calendar.YEAR);
        boolean isMonthAfter = calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) && calendar1.get(Calendar.MONTH) >= calendar2.get(Calendar.MONTH);

        return isYearAfter || isMonthAfter;
    }
    
    public static Date getDateAtHour(Date date, Integer hour) {
        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date targetDate = calendar.getTime();

        return targetDate;
    }

    public static Date getLastOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //Jirra ACC-1182

    static {
        dashedDateFormat.setLenient(false);
        dashedDateFormatWithTime.setLenient(false);
    }

    public static String formatFullDate(Object d) {
        return fullDateFormat.format(d);
    }

    public static String formatClientFullDate(Object d) {
        return clientFullDateFormat.format(d);
    }

    public static String formatFullDateWithTime(Object d) {
        return fullDateFormatWithTime.format(d);
    }

    public static String formatClientFullDateWithTime(Object d) {
        return clientFullDateFormatWithTime.format(d);
    }

    public static String formatDateDashed(Object date) {
        if (date == null) return null;
        return dashedDateFormat.format(date);
    }

    public static String formatDateDashedV2(Date date) {
        if (date == null) return null;
        return dashedDateFormatV2.format(date);
    }

    public static String formatDateDashedWithTime(Date date) {
        if (date == null) return null;
        return dashedDateFormatWithTime.format(date);
    }

    public static String formatDateDashedWithTimeV2(Date date) {
        if (date == null) return null;
        return dashedDateFormatWithTimeV2.format(date);
    }

    public static String formatDateDashedWithTimeV3(Date date) {
        if (date == null) return null;
        return dashedDateFormatWithTimeV3.format(date);
    }

    public static String formatMonth(Date date) {
        if (date == null) return null;
        return monthFormat.format(date);
    }

    public static String formatMonthWithTime(Date date) {
        if (date == null) return null;
        return monthFormatWithTime.format(date);
    }

    public static String formatMonthWithSimpleTime(Date date) {
        if (date == null) return null;
        return monthFormatWithSimpleTime.format(date);
    }

    public static String formatSimpleMonth(Date date) {
        if (date == null) return null;
        return simpleMonthFormat.format(date);
    }

    public static String formatSimpleMonthYear(Date date) {
        if (date == null) return null;
        return simpleMonthYearFormat.format(date);
    }

    public static String formatMonthDayYear(Date date) {
        if (date == null) return null;
        return monthDayYearFormat.format(date);
    }

    public static String formatDayOfWeek(Date date) {
        if (date == null) return null;
        return dayOfWeekFormat.format(date);
    }

    public static String formatDayMonthDayYear(Date date) {
        if (date == null) return null;
        return dayMonthDayYearFormat.format(date);
    }

    public static String formatDateSlashed(Date date) {
        if (date == null) return null;
        return slashedDateFormat.format(date);
    }

    public static String formatSimpleTime(Date date) {
        if (date == null) return null;
        return simpleTimeFormat.format(date);
    }

    public static String formatTime(Date date) {
        if (date == null) return null;
        return timeFormat.format(date);
    }

    public static String formatDayNumber(Date date) {
        if (date == null) return null;
        return dayNumberFormat.format(date);
    }

    public static String formatMonthNumber(Date date) {
        if (date == null) return null;
        return monthNumberFormat.format(date);
    }

    public static String formatYearNumber(Date date) {
        if (date == null) return null;
        return yearNumberFormat.format(date);
    }

    public static String formatYear(Date date) {
        if (date == null) return null;
        return yearFormat.format(date);
    }

    public static String formatNotDashedFullDate(Date date) {
        if (date == null) return null;
        return notDashedFullDateFormat.format(date);
    }

    public static String formatYearMonth(Date date) {
        if (date == null) return null;
        return yearMonthFormat.format(date);
    }

    public static String formatMonthDayOfMonthWithSuffix(Date date) {
        if (date == null) return null;

        return formatMonth(date) + " " + formatDayNumber(date).replaceFirst("^0+(?!$)", "")
                + getOrdinalSuffix(new DateTime(date).getDayOfMonth());
    }

    public static Date parseFullDate(String date) throws ParseException {
        if (date == null) return null;
        return fullDateFormat.parse(date);
    }

    public static Date parseClientFullDate(String date) throws ParseException {
        if (date == null) return null;
        return clientFullDateFormat.parse(date);
    }

    public static Date parseFullDateWithTime(String date) throws ParseException {
        if (date == null) return null;
        return fullDateFormatWithTime.parse(date);
    }

    public static Date parseClientFullDateWithTime(String date) throws ParseException {
        if (date == null) return null;
        return clientFullDateFormatWithTime.parse(date);
    }

    public static Date parseDateDashed(String date) throws ParseException {
        if (date == null) return null;
        return dashedDateFormat.parse(date);
    }

    public static Date parseDateSlashed(String date) throws ParseException {
        if (date == null) return null;
        return slashedDateFormat.parse(date);
    }

    public static Date parseDateDashedV2(String date) throws ParseException {
        if (date == null) return null;
        return dashedDateFormatV2.parse(date);
    }

    public static Date parseDateTimeDashed(String date) throws ParseException {
        if (date == null) return null;
        return dashedDateFormatWithTime.parse(date);
    }

    public static Date parseDashedDateFormatWithTimeV3(String date) throws ParseException {
        if (date == null) return null;
        return dashedDateFormatWithTimeV3.parse(date);
    }

    public static Date parseDateSlashedV2(String date) throws ParseException {
        if (date == null) return null;
        return slashedDateFormatV2.parse(date);
    }

    public static String formatCCAPPDate(Date date) {
        LocalDate date1 = new LocalDate(date);
        String ordinalSuffix = getOrdinalSuffix(date1.getDayOfMonth());
        return date1.getDayOfMonth() + ordinalSuffix + " " + simpleMonthYearFormat.format(date);
    }

    public static DateFormat getDashedDateFormat() {
        return dashedDateFormat;
    }

    public static DateFormat getDashedDateFormatWithTime() {
        return dashedDateFormatWithTime;
    }

    public static DateFormat getDashedDateFormatWithTimeV2() {
        return dashedDateFormatWithTimeV2;
    }

    public static int hoursDifference(Date date1, Date date2) {
        final int MILLI_TO_HOUR = 1000 * 60 * 60;
        return (int) ((date1.getTime() - date2.getTime()) / MILLI_TO_HOUR);
    }

    public static boolean isSameDate(Date d1, Date d2) {
        return isSameMonth(d1, d2) &&
                new DateTime(d1).getDayOfMonth() == new DateTime(d2).getDayOfMonth();
    }

    public static boolean isSameMonth(Date d1, Date d2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(d1);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(d2);

        return calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
                calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH);
    }
    
    public static Date getFirstDateOfMonth(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }
    
    public static Date getStartOfCurrentWeek() {
        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        Picklist days = picklistRepository.findByCode("days");
        PicklistItem startOfWeekPickListItem = picklistItemRepository.findByListOrderByNameAsc(days).stream().filter(item -> item.hasTag(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_WEEK_START_TAG))).collect(Collectors.toList()).get(0);
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.clear(Calendar.MINUTE);
        cal.clear(Calendar.SECOND);
        cal.clear(Calendar.MILLISECOND);
        int currentDayOfWeek = (cal.get(Calendar.DAY_OF_WEEK) + 7 - CalenderDaysOfWeek.valueOf(startOfWeekPickListItem.getName()).getValue()) % 7;
        cal.add(Calendar.DAY_OF_YEAR, -currentDayOfWeek);


        return cal.getTime();
    }

    public static Date getStartOfCurrentYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.clear(Calendar.MINUTE);
        cal.clear(Calendar.SECOND);
        cal.clear(Calendar.MILLISECOND);

        cal.set(Calendar.DAY_OF_YEAR, 1);
        return cal.getTime();
    }

    public static Date parseNoqodiDate(String date) throws ParseException {
        if (date == null) return null;
        return noqodiDateFormatV2.parse(date);
    }

    public static Date parseAmwalDate(String date) throws ParseException {
        if (date == null) return null;
        return amwalDateFormatV2.parse(date);
    }

    public enum CalenderDaysOfWeek {
        Sunday(1), Monday(2), Tuesday(3), Wednesday(4), Thursday(5), Friday(6), Saturday(7);

        private final int value;

        private CalenderDaysOfWeek(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public static Date resolveDateFromCsvFile(String d) {

        List<String> formatList = Arrays.asList(
                "M/d/yyyy", "MM/dd/yyyy", "MM/d/yyyy",
                "M/dd/yyyy", "yyyy-MM-dd", "yyyy-MM-dd hh:mm:ss", "YYYY-MM-DDTHH:MM:SS.sss");
        for (String f : formatList) {
            try {
                return new SimpleDateFormat(f).parse(d);
            } catch(Exception e) {
                logger.info("The format (" + f + ") did not match");
            }
        }

        return null;
    }

    public static String formatTo_MM_DD_YYYY(LocalDate date) {
        int day = date.getDayOfMonth();
        return date.toString("MMM") + " " + day + getOrdinalSuffix(day) + " " + date.toString("yyyy");
    }

    public static Date getMonthEnd(Date d) {
        LocalDate c =  new LocalDate(d);;
        return c.dayOfMonth().withMaximumValue().toDate();
    }

    public static Date getMonthStart(Date d) {
        LocalDate c =  new LocalDate(d);;
        return c.dayOfMonth().withMinimumValue().toDate();
    }

    public static boolean checkIfInSameDay(Date d1,Date d2){
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(d1);
        cal2.setTime(d2);
        boolean sameDay = cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR) &&
                cal1.get(YEAR) == cal2.get(YEAR);
        return sameDay;
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat("dd MM yyyy").format(date);
    }

    public static LocalDate getCurrentMonthDateViaContract(Contract contract) {
        return contract.isOneMonthAgreement() ?
                Setup.getApplicationContext()
                        .getBean(OneMonthAgreementFlowService.class)
                        .getCurrentPaymentDate(contract).toLocalDate() :
                new LocalDate().dayOfMonth().withMinimumValue();
    }

    public static LocalDate getStartDayPaymentDateOfMonth(Contract contract, Date paymentDate) {
        return contract.isOneMonthAgreement() ?
                Setup.getApplicationContext()
                        .getBean(OneMonthAgreementFlowService.class)
                        .getPaymentDate(contract, new DateTime(paymentDate)).toLocalDate() :
                new LocalDate(paymentDate).dayOfMonth().withMinimumValue();
    }
}