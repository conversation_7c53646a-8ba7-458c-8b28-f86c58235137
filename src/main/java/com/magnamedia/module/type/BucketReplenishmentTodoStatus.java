package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * @date 1/21/2021
 */
public enum BucketReplenishmentTodoStatus  implements LabelValueEnum {

    PENDING("Pending"),
    APPROVED("Approved"),
    REJECTED("Rejected");

    private final String label;

    BucketReplenishmentTodoStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
