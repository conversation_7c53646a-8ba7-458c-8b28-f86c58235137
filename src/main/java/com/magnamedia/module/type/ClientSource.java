package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 08, 2020
 *         Jirra ACC-1435
 */
public enum ClientSource implements LabelValueEnum {
    WHATSAPP("Whatsapp"),
    FACEBOOK("Facebook"),
    INBOUND("Inbound"),
    WEBSITE("Website"),
    IC("IC"),
    EVENT("Event"),
    SOCIAL_MEDIA("Social Media"),
    CHAT_GPT("ChatGPT");

    private final String label;

    ClientSource(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}