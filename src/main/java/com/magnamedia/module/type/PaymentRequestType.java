package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 21, 2019
 *         Jirra ACC-1241
 */
public enum PaymentRequestType implements LabelValueEnum {
    ERP("ERP"),
    CONTRACT_CANCELLATION_REFUND("Contract Cancelation Refund"),
    DISCOUNT_REFUND("Discount Refund"),
    DUPLICATED_PAYMENT("Duplicated Payment");

    private final String label;

    private PaymentRequestType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
