package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 22, 2019 Jirra ACC-608
 */
public enum HousemaidType implements LabelValueEnum {

    FREEDOM_OPERATOR("Freedom Operator"),
    MAID_VISA("MaidVisa"),
    Normal("Normal"),
    WALKIN("Walk-in");

    private final String label;

    HousemaidType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
