package com.magnamedia.module.type;

import com.fasterxml.jackson.annotation.JsonValue;
import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * MC-28
 * */
public enum ContractSummaryPaymentType implements LabelValueEnum {
    MONTHLY_PREVIOUS_MONTH("monthlyPaymentPreviousMonth", "Monthly (Previous Month)"),
    MONTHLY_CURRENT_MONTH("monthlyPaymentCurrentMonth", "Monthly (Current Month)"),
    MONTHLY_RECURRING("monthlyPaymentRecurring", "Monthly (Recurring)"),
    FUTURE_MONTHLY_NOT_RECURRING("monthlyPaymentFutureNotRecurring", "Future Monthly (Not Recurring)"),
    SDR("sdr", "SDR"),
    SDR_INSTALLMENT("sdr@param@", "SDR Installment @param@"),
    UPCOMING_SDR_RENEWAL("upcomingSdrRenewal","Upcoming SDR Renewal"),
    UPCOMING_SDR_RENEWAL_INSTALLMENT("upcomingSdrRenewalInstallment@param@","Upcoming SDR Renewal Installment @param@"),
    SDR_RENEWAL("sdrRenewal", "SDR Renewal"),
    INSURANCE("insurance", "Insurance"),
    UPCOMING_INSURANCE_RENEWAL("upcomingInsuranceRenewal", "Upcoming Insurance Renewal"),
    NON_MONTHLY("@param@", "@param@");

    private final String label;
    private final String code;

    ContractSummaryPaymentType(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @Override
    @JsonValue
    public String getLabel() {
        return label;
    }

    public String getCode() {
        return code;
    }
}
