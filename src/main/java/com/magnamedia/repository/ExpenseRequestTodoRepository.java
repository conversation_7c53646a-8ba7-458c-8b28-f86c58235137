package com.magnamedia.repository;

import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.InvoiceStatementTransactionType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 *         Created on Jan 18, 2021
 *         Jirra ACC-2913
 */
@Repository
public interface ExpenseRequestTodoRepository extends WorkflowRepository<ExpenseRequestTodo> {


    List<ExpenseRequestTodo> findByBeneficiaryIdAndPaymentMethodAndStatus(Long beneficiaryId, ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    List<ExpenseRequestTodo> findByExpenseOrderByCreationDateAsc(Expense expense);
    List<ExpenseRequestTodo> findByExpensePayment(ExpensePayment expensePayment);

    List<ExpenseRequestTodo> findByExpenseAndCreationDateBetweenAndIdNotInOrderByCreationDateAsc(Expense expense, Date fromDate, Date toDate, List<Long> ids);
    List<ExpenseRequestTodo> findByExpenseAndCreationDateBetweenOrderByCreationDateAsc(Expense expense, Date fromDate, Date toDate);

    List<ExpenseRequestTodo> findByExpenseAndInvoiceNumberOrderByCreationDateAsc(Expense expense, String invoiceNumber);

    Page<ExpenseRequestTodo> findByTaskNameAndRelatedToType(String taskName, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, Pageable pageable);

    Page<ExpenseRequestTodo> findByTaskNameAndRelatedToTypeAndExpense_RequestedFrom_CodeIn(String taskName, ExpenseRelatedTo.ExpenseRelatedToType relatedToType, List<String> codes, Pageable pageable);

    @Query("SELECT exR FROM ExpenseRequestTodo exR "
            + "JOIN exR.expense ex "
            + "LEFT JOIN exR.expense.requestedFrom rf "
            + "where exR.taskName = :taskName and (exR.relatedToType <> 'MAID' "
            + "or exR.expense.requestedFrom is null "
            + "or exR.expense.requestedFrom.code not in :codes) "
            + "and exR.linkedToFopRequest = false")
    Page<ExpenseRequestTodo> findByTaskNameAndExpenseAndNotRelatedToMaidOrRelatedToMaidAnd_RequestedFrom_CodeNotIn(@Param("taskName") String taskName, @Param("codes") List<String> codes, Pageable pageable);

    @Query("select todo.expense.caption, todo.creationDate, todo.amount, todo.loanAmount, todo.paymentMethod, todo.status, todo.notes " +
            "from ExpenseRequestTodo todo " +
            "where ((todo.relatedToId = ?1 and todo.relatedToType = 'MAID') or " +
                "(todo.beneficiaryId = ?1 and todo.beneficiaryType = 'MAID'))")
    List<Object[]> findByRelatedToIdAndRelatedToType(Long relatedToId);

    Page<ExpenseRequestTodo> findByTaskName(String taskName, Pageable pageable);

    Page<ExpenseRequestTodo> findByTaskNameAndApproveHolder(String taskName, User user, Pageable pageable);

    List<ExpenseRequestTodo> findByBeneficiaryIdAndStatusIn(Long beneficiaryId, List<ExpenseRequestStatus> statuses);

    List<ExpenseRequestTodo> findByRelatedToIdAndStatusIn(Long relatedToId, List<ExpenseRequestStatus> statuses);

    List<ExpenseRequestTodo> findByPaymentMethodAndStatus(ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    List<ExpenseRequestTodo> findByPaymentMethodAndStatusAndExpensePaymentIsNull(ExpensePaymentMethod invoiced, ExpenseRequestStatus pendingPayment);

    @Query("SELECT todo FROM ExpenseRequestTodo todo " +
           "LEFT JOIN Supplier s ON s.id = todo.beneficiaryId " +
           "WHERE todo.paymentMethod = 'INVOICED' " +
           "AND todo.status = 'PENDING_PAYMENT' " +
           "AND todo.expensePayment IS NULL " +
           "AND todo.beneficiaryType = 'SUPPLIER' " +
           "AND (s.name IS NULL OR s.name != 'Capital Medical Centre for Health' )")
    List<ExpenseRequestTodo> findByPaymentMethodAndStatusAndExpensePaymentIsNullExcludingSupplier();

    @Query("SELECT todo FROM ExpenseRequestTodo todo " +
            "INNER JOIN InvoiceStatementRecord r ON r.statement.id = :statementId AND r.housemaid.id = todo.relatedToId " +
            "WHERE todo.paymentMethod = 'INVOICED' AND todo.status = 'PENDING_PAYMENT' " +
                "AND todo.relatedToType = 'MAID' " +
                "AND todo.beneficiaryType = 'SUPPLIER' AND todo.beneficiaryId = :beneficiaryId " +
                "AND (:types IS NULL OR " +
                    "NOT EXISTS (SELECT t.id FROM InvoiceStatementTransaction t " +
                                "WHERE t.statement.id = :statementId " +
                                    "AND t.type IN :types AND t.type != 'DELETED' " +
                                    "AND t.expenseRequestTodo = todo " +
                                    "AND (t.confirmed IS NULL OR t.confirmed = false)))")
    List<ExpenseRequestTodo> findPendingInvoicedExpenseRequests(
            @Param("beneficiaryId") Long beneficiaryId,
            @Param("statementId") Long statementId,
            @Param("types") List<InvoiceStatementTransactionType> usedTypes);

    @Query("SELECT todo FROM ExpenseRequestTodo todo " +
           "WHERE todo.paymentMethod = 'INVOICED' AND todo.status = 'PENDING_PAYMENT' " +
                "AND todo.relatedToType = 'MAID' AND todo.relatedToId = :relatedToId " +
                "AND todo.beneficiaryType = 'SUPPLIER' AND todo.beneficiaryId = :beneficiaryId " +
                "AND (:statementId IS NULL OR " +
                    "NOT EXISTS (SELECT t.id FROM InvoiceStatementTransaction t " +
                                "WHERE t.statement.id = :statementId " +
                                    "AND t.type = 'Matched' AND t.expenseRequestTodo = todo " +
                                    "AND (t.confirmed IS NULL OR t.confirmed = false)))")
    List<ExpenseRequestTodo> findPendingInvoicedExpenseRequestsForMaid(
            @Param("relatedToId") Long relatedToId,
            @Param("beneficiaryId") Long beneficiaryId,
            @Param("statementId") Long statementId);

    List<ExpenseRequestTodo> findByCovidLaserTestTaxiOrderAndExpenseRequestType(LogisticsWorkOrder logisticsWorkOrder, ExpenseRequestType expenseRequestType);

    @Query("select todo from ExpenseRequestTodo todo " +
            "join fetch todo.expense e "+
            "where ((todo.beneficiaryId = ?1 and todo.beneficiaryType = 'MAID') or " +
                "(todo.relatedToId = ?1 and todo.relatedToType = 'MAID')) and e.code = ?2")
    List<ExpenseRequestTodo> findByBeneficiaryIdAndBeneficiaryTypeAndExpense_Code(
            Long beneficiaryId, String expenseCode);


    @Query("SELECT todo FROM ExpenseRequestTodo todo " +
           "JOIN Housemaid h ON todo.relatedToType = 'MAID' and todo.relatedToId = h.id " +
           "WHERE todo.paymentMethod = 'INVOICED' AND todo.status = 'PENDING_PAYMENT' AND todo.status != 'CANCELED' " +
                "AND todo.beneficiaryId = :beneficiaryId AND todo.beneficiaryType = 'SUPPLIER' " +
                "AND (((:maidName IS NULL OR :maidName = '') AND (:passportNumber IS NULL OR :passportNumber = '')) " +
                    "OR (:maidName IS NOT NULL AND :maidName != '' AND LOWER(h.name) LIKE LOWER(CONCAT('%', :maidName, '%'))) " +
                    "OR (:passportNumber IS NOT NULL AND :passportNumber != '' " +
                        "AND LOWER(h.passportNumber) LIKE LOWER(CONCAT('%', :passportNumber, '%')))) " +
                "AND NOT EXISTS (SELECT t.id FROM InvoiceStatementTransaction t " +
                                "WHERE t.statement.id = :statementId " +
                                "AND t.type IN :types AND t.type != 'DELETED' " +
                                "AND t.expenseRequestTodo = todo " +
                                "AND (t.confirmed IS NULL OR t.confirmed = false))")
    Page<ExpenseRequestTodo> findPendingMatchedTransactions(
            @Param("statementId") Long statementId,
            @Param("types") List<InvoiceStatementTransactionType> usedTypes,
            @Param("beneficiaryId") Long beneficiaryId,
            @Param("maidName") String maidName,
            @Param("passportNumber") String passportNumber,
            Pageable pageable);

    @Query("select todo.contractId from ExpenseRequestTodo todo " +
            "join todo.expensePayment e "+
            "where e.id = ?1 and todo.relatedToId = ?2 and todo.relatedToType = 'MAID'")
    List<Long> findByExpensePaymentAndRelatedToIdAndRelatedToType(
            Long expensePaymentId, Long relatedToId);
}
