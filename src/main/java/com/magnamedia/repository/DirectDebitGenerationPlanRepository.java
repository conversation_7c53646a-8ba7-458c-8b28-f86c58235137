package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebitGenerationPlan;
import com.magnamedia.entity.DirectDebitGenerationPlan.DdGenerationPlanStatus;
import com.magnamedia.module.type.ContractStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

@Repository
public interface DirectDebitGenerationPlanRepository extends BaseRepository<DirectDebitGenerationPlan> {

    @Query("SELECT d FROM DirectDebitGenerationPlan d "
            + "where d.ddGenerationPlanStatus = 'PENDING' AND d.ddGenerationDate < ?1 ")
    List<DirectDebitGenerationPlan> findByStatusPendingAndDDGenerationDateLessThan(
            Date ddGenerationDate);

    @Query("SELECT d FROM DirectDebitGenerationPlan d "
            + "where d.ddGenerationPlanStatus = 'PENDING' AND d.ddGenerationDate = ?1 AND d.id not in ?2 order by d.creationDate")
    Page<DirectDebitGenerationPlan> findByStatusPendingAndDDGenerationDateEquals(
            Date ddGenerationDate, List<Long> ids, Pageable pageable);

    @Query("select d from DirectDebitGenerationPlan d " +
            "where d.id > ?1 and d.ddGenerationPlanStatus = 'PENDING' and " +
            "d.contractPaymentType.type.code = 'insurance' and d.ddGenerationDate = ?2")
    Page<DirectDebitGenerationPlan> findByStatusPendingAndTypeInsuranceAndDDGenerationDateEquals(
            Long lastId, Date ddGenerationDate, Pageable pageable);

    List<DirectDebitGenerationPlan> findByContractAndDdGenerationPlanStatusIn(
            Contract contract, List<DirectDebitGenerationPlan.DdGenerationPlanStatus> s);

    @Query("SELECT d FROM DirectDebitGenerationPlan d "
        + "where d.ddGenerationPlanStatus = 'PENDING' AND d.contract = ?1 And d.ddCloned is not null")
    List<DirectDebitGenerationPlan> findByStatusPendingAndContractAndDDClonedIsNotNull(Contract contract);

    @Query("SELECT d FROM DirectDebitGenerationPlan d "
            + "where d.ddID = ?1 AND d.messageSent = true ")
    DirectDebitGenerationPlan findByDirectDebitIDAndMessageSentTrue(Long ddID);

    List<DirectDebitGenerationPlan> findAllPlansByContractAndDdSendDate(Contract contract, Date cpDate);

    DirectDebitGenerationPlan findFirstByContractAndOneTimeFalseAndContractPaymentType_Type_CodeAndDdExpiryDateIsGreaterThan(
            Contract contract, String paymentCode, Date d);

    @Query("select count(p.id) > 0 from DirectDebitGenerationPlan p " +
            "where p.contract = ?1 and p.contractPaymentType.type = ?2 and " +
            "p.ddGenerationPlanStatus <> 'DELETED'")
    Boolean existsByContractAndContractPaymentType_TypeAndDdGenerationPlanStatus(Contract contract, PicklistItem type);

    @Query("select distinct cpt from DirectDebitGenerationPlan d " +
            "join d.contractPaymentType.contractPaymentTerm cpt " +
            "where cpt.id > ?1 and cpt.isActive = false and " +
            "d.ddGenerationPlanStatus in ?2 and cpt.contract.status in ?3 " +
            "order by cpt.id")
    Page<ContractPaymentTerm> findInactiveCptHasActivePlan(
            Long lastId, List<DirectDebitGenerationPlan.DdGenerationPlanStatus> pendingStatus, List<ContractStatus> l, Pageable pageable);

    @Query("select distinct cpt from ContractPaymentType t " +
            "join t.contractPaymentTerm cpt " +
            "join cpt.contract c " +
            "join t.type typeItem " +
            "where cpt.id > ?1 and c.status = 'ACTIVE' and c.startOfContract <= ?3 and cpt.isActive = true and " +
                "typeItem.code not in ?2 and t.postponedDdGenerated = false and " +
                "not exists(select 1 from DirectDebitGenerationPlan p " +
                    "where p.contract = c and p.contractPaymentType.type = typeItem and " +
                        "p.ddGenerationDate >= ?3 and p.ddGenerationPlanStatus in ?4)")
    Page<ContractPaymentTerm> findActiveCptHasNotActivePlan(
            Long lastId, List<String> monthlyTypes, Date today, List<DdGenerationPlanStatus> statuses, Pageable pageable);

    List<DirectDebitGenerationPlan> findByDdGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus s);

    @Query("select d from DirectDebitGenerationPlan d " +
            "where d.id > ?1 and d.ddGenerationPlanStatus in ('PENDING', 'PENDING_PAYING_VIA_CREDIT_CARD') and " +
            "d.ddGenerationDate >= ?2 ")
    Page<DirectDebitGenerationPlan> findDirectDebitGenerationPlansByDDGenerationDateAfter(
            Long id, Date ddGenerationDate, Pageable pageable);

    @Query("select p from DirectDebitGenerationPlan p " +
            "where p.contract = ?1 and p.contractPaymentType.type.code <> 'monthly_payment' and " +
            "p.ddGenerationPlanStatus = 'PENDING' and p.ddSendDate >= ?2 " +
            "order by p.ddGenerationDate asc")
    List<DirectDebitGenerationPlan> findPlansForPaymentExpiryFlow(Contract contract, Date date);


    @Query("select d from DirectDebitGenerationPlan d " +
            "where d.contract = ?1 and d.contractPaymentType.type.code = 'insurance'")
    List<DirectDebitGenerationPlan> getDirectDebitGenerationPlanWithTypeInsurance(Contract c);

    Boolean existsByContractAndDdGenerationPlanStatusIn(Contract contract, List<DirectDebitGenerationPlan.DdGenerationPlanStatus> s);

    @Query("select d from DirectDebitGenerationPlan d " +
            "where d.id > ?1 and d.contract.payingViaCreditCard = true and " +
            "d.ddGenerationPlanStatus in ?2 and d.contractPaymentType.type.code = 'monthly_payment'")
    Page<DirectDebitGenerationPlan> findMonthlyDdGenerationPlanByContractAndDdGenerationPlanStatusIn(
            Long id, List<DirectDebitGenerationPlan.DdGenerationPlanStatus> s, Pageable pageable);

    List<DirectDebitGenerationPlan> findByContractAndDdGenerationPlanStatusAndContractPaymentType_Type_CodeOrderByDdSendDateAsc(
            Contract c, DdGenerationPlanStatus s, String type);

    @Query("SELECT p FROM DirectDebitGenerationPlan p " +
            "WHERE p.contract = ?1 AND p.contractPaymentType.type.code NOT IN ('insurance', 'same_day_recruitment_fee', 'monthly_payment') " +
            "AND p.ddGenerationPlanStatus = 'PENDING' " +
            "Order by p.ddSendDate asc")
    List<DirectDebitGenerationPlan> findByNonMonthlyPlansByContractOrderByDdGenerationDateAsc(Contract c);
}