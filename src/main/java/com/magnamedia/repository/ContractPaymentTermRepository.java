package com.magnamedia.repository;

import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.module.type.ContractPaymentTermReason;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 30, 2018
 */
@Repository
public interface ContractPaymentTermRepository extends BaseRepository<ContractPaymentTerm> {

    @Query("select cpt from ContractPaymentTerm cpt " +
            "where (cpt.ibanNumber <> '' and cpt.eid <> '' and cpt.accountName <> '') " +
                "and cpt.contract.client = ?1 and cpt.isActive = true and cpt.id <> ?2 " +
            "order by cpt.contract.startOfContract DESC")
    List<ContractPaymentTerm> getLastOldContractPaymentTermActive(Client client, Long id);

    boolean existsByContract_IdAndIsActiveTrue(Long contractId);

    @Query("Select new Map(CPT.contract as contract, CPT as contractPaymentTerm, C.client as client) " +
            "from ContractPaymentTerm CPT left join CPT.contract C " +
            "where CPT.contract = ?1 and CPT.isActive = 1")
    List<Map> findActiveTermsByContract(Contract contract);

    @Query("select cpt from ContractPaymentTerm cpt where cpt.contract = ?1 and cpt.isActive = false")
    List<ContractPaymentTerm> findNotActiveTermsByContract(Contract contract);

    //jirra ACC-633
    List<ContractPaymentTerm> findByContractAndIsActive(Contract contract, boolean isActive);

    List<ContractPaymentTerm> findByCreationDateBetween(Date fromDate, Date toDate);

    List<ContractPaymentTerm> findByContractAndIsActiveOrderByCreationDateDesc(Contract contract, boolean isActive);

    //Jirra ACC-1435
    Boolean existsByContract(Contract contract);

    ContractPaymentTerm findByContract(Contract contract);

    ContractPaymentTerm findFirstByContractAndIdGreaterThanOrderById(Contract contract, Long id);
    ContractPaymentTerm findFirstByContractAndIdLessThanOrderByIdDesc(Contract contract, Long id);

    ContractPaymentTerm findFirstByContractAndReasonOrderByCreationDateDesc(
            Contract contract, ContractPaymentTermReason reason);

    @Query("select c from ContractPaymentTerm c " +
            "where c.id > ?1 and c.isActive = true and c.reason = ?2 and c.creationDate > (" +
                "select max(l.creationDate) from CollectionFlowLog l " +
                    "where l.flowType.code = 'switching_bank_account_flow')")
    Page<ContractPaymentTerm> findByReasonAndCreationDate(Long lastId, ContractPaymentTermReason reason, Pageable pageable);

    ContractPaymentTerm findTopByContractAndIsActiveTrueAndReasonInOrderByIdDesc(Contract contract, List<ContractPaymentTermReason> reasons);

    @Query("select cpt from ContractPaymentTerm cpt where cpt.contract = ?1 and cpt.isActive = false and cpt.switchedOrReplacedNationality = true order by cpt.id desc")
    List<ContractPaymentTerm> findOldCPTForSwitching(Contract contract);

    ContractPaymentTerm findFirstByContractOrderByCreationDateDesc(Contract contract);

    ContractPaymentTerm findFirstByContract_IdOrderByIdDesc(Long contractId);

    @Query("select new map(c.creationDate as creationDate, c.switchedBankAccount as switchedBankAccount) from " +
            "ContractPaymentTerm c where c.contract = ?1 order by c.creationDate desc")
    List<?> findCptAfterSwitchingBankAccount(Contract contract, Pageable pageable);

    @Query("select c from ContractPaymentTerm c " +
            "join c.contract co " +
            "where c.contract.payingViaCreditCard = true and c.isActive = true and co.paidEndDate <= ?1 and " +
                "co.status not in ('CANCELLED', 'EXPIRED') and co.scheduledDateOfTermination is null and " +
                "not exists (select 1 from FlowProcessorEntity f " +
                            "where f.contractPaymentTerm.contract = co and " +
                                "f.flowEventConfig.name = 'EXTENSION_FLOW' and f.stopped = false and f.completed = false) and " +
                "not exists (select 1 from FlowProcessorEntity f " +
                            "where f.contractPaymentTerm.contract = co and " +
                                "f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and " +
                                "f.currentSubEvent.name <> 'DD_SIGNING_OFFER' and " +
                                "f.stopped = false and f.completed = false)")
    List<ContractPaymentTerm> findByCptAndFlowProcessEntityAndPayingViaCreditCard(Date d);

    @Query("select c.id from ContractPaymentTerm c " +
            "join c.contract co " +
            "where c.contract.payingViaCreditCard = true and c.isActive = true and co.paidEndDate <= ?1 and " +
            "co.status not in ('CANCELLED', 'EXPIRED') and co.scheduledDateOfTermination is null and " +
            "not exists (select 1 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = co and " +
            "f.flowEventConfig.name = 'EXTENSION_FLOW' and f.stopped = false and f.completed = false) and " +
            "not exists (select 1 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = co and " +
            "f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and " +
            "f.currentSubEvent.name <> 'DD_SIGNING_OFFER' and " +
            "f.stopped = false and f.completed = false)")
    List<Long> findIdByCptAndFlowProcessEntityAndPayingViaCreditCard(Date d);

    @Query("select cpt from ContractPaymentTerm cpt " +
            "where cpt.id > ?1 and cpt.isActive = false and cpt.sourceId is not null" )
    Page<ContractPaymentTerm> findContractPaymentTermWithNotNullSourceIDAndIsActiveFalse(Long cptId, Pageable pageable);

    @Query("select cpt from ContractPaymentTerm cpt " +
            "where cpt.contract = ?1 and cpt.isActive = false and cpt.sourceId is not null" )
    List<ContractPaymentTerm> findContractPaymentTermWithNotNullSourceIDAndIsActiveFalseByContract(Contract contract);

    // ACC-4905
    @Query("select c from ContractPaymentTerm c  " +
            "join c.contract as co " +
            "where co.isOneMonthAgreement = true and co.payingViaCreditCard = false and c.isActive = true and " +
                "co.status not in ('CANCELLED', 'EXPIRED') and " +
                "exists(select 1 from Payment p where p.methodOfPayment <> 'DIRECT_DEBIT' " +
                            "and p.typeOfPayment.code = 'monthly_payment' and p.status = 'RECEIVED') and " +
                "not exists(select 1 from DirectDebitSignature d where d.contractPaymentTerm = c) and " +
                "not exists(select 1 from DirectDebit d where d.contractPaymentTerm = c and d.addedManuallyFromClientProfile = true)")
    List<ContractPaymentTerm> findOneMonthAgreementPayingViaCreditCard();

    // ACC-5712
    @Query("select distinct c from DirectDebit d " +
            "join d.contractPaymentTerm c " +
            "where c.contract.isOneMonthAgreement = true and c.isActive = true and " +
            "d.prorated = true and d.startDate <= ?1 and " +
            "exists (select 1 from DirectDebit d1 where d1.contractPaymentTerm = c and " +
            "(d1.status = 'CONFIRMED' or d1.MStatus = 'CONFIRMED'))")
    List<ContractPaymentTerm> findOneMonthAgreementPayingViaDd(Date date);

    @Query(nativeQuery = true,
            value = "SELECT cpt.ID, cfg.WEEKLY_AMOUNT, cfg.DAILY_RATE_AMOUNT " +
                    "FROM CONTRACTPAYMENTTERMS cpt " +
                    "INNER JOIN CONTRACTS ct ON ct.ID = cpt.CONTRACT_ID " +
                    "INNER JOIN PAYMENTTERMCONFIGS_REVISIONS cfg ON cfg.REVISION = " +
                    "(SELECT MAX(R.REVISION) FROM PAYMENTTERMCONFIGS_REVISIONS R " +
                    "WHERE R.ID = cpt.PAYMENT_TERM_CONFIG_ID AND " +
                    "R.LAST_MODIFICATION_DATE <= cpt.CREATION_DATE) " +
                    "WHERE ct.STATUS = 'ACTIVE' AND cpt.IS_ACTIVE = 1 AND cpt.ID > ?1 AND " +
                    "(((cpt.WEEKLY_AMOUNT IS NULL OR cpt.WEEKLY_AMOUNT = 0) AND " +
                    "(cfg.WEEKLY_AMOUNT IS NOT NULL AND cfg.WEEKLY_AMOUNT > 0)) OR " +
                    "((cpt.WEEKLY_AMOUNT IS NOT NULL AND cpt.WEEKLY_AMOUNT > 0) AND " +
                    "(cfg.WEEKLY_AMOUNT IS NULL OR cfg.WEEKLY_AMOUNT = 0))) AND " +
                    "(cpt.REASON IN ('SWITCHING', 'REPLACEMENT', 'REPLACEMENT_WITH_VAT')) " +
                    "ORDER BY cpt.ID ASC LIMIT 50")
    List<Object[]> findWithWeeklyPackage(Long id);

    @Query(nativeQuery = true,
            value = "select cli.id as cliId, max(a.id) as aId from CLIENTS cli " +
                    "inner join CONTRACTS c on cli.ID = c.CLIENT_ID " +
                    "inner join CONTRACTPAYMENTTERMS cpt on c.ID = cpt.CONTRACT_ID " +
                    "inner join ATTACHMENTS a on a.OWNER_ID = cpt.id and a.OWNER_TYPE = 'ContractPaymentTerm' and a.TAG = 'bank_info_eid' " +
                    "where cli.id > ?1 and (cli.NATIONALITY_ID is null or cli.TITLE_ID is null) " +
                    "group by cli.id " +
                    "order by cli.id asc, a.id desc " +
                    "limit 30")
    List<Object[]> findClientForAcc6214(Long id);

    ContractPaymentTerm findFirstByContractAndCreationDateLessThanOrderByCreationDateDesc(Contract c, Date d);

    @Query(value = "SELECT COUNT(CPT.ID) from CONTRACTPAYMENTTERMS CPT " +
                    "WHERE CPT.CONTRACT_ID = ?1 AND CPT.REASON = 'SWITCHING' AND CPT.CREATION_DATE > ?2 AND " +
                        "CPT.CREATION_DATE > (SELECT CR.LAST_MODIFICATION_DATE FROM CONTRACTS_REVISIONS CR " +
                                                "WHERE CR.ID = ?1 AND CR.PAYING_VIA_CREDIT_CARD = 1 AND CR.PAYING_VIA_CREDIT_CARD_MODIFIED = 1 " +
                                                "ORDER BY CR.LAST_MODIFICATION_DATE LIMIT 1)",
            nativeQuery = true)
    int countSwitchingNationalitiesPayingViaCcPerCurrentMonth(Long contractId, Date d);

    @Query("select cpt.id from ContractPaymentTerm cpt " +
            "where cpt.contract = ?1 and cpt.isActive = false " +
            "order by cpt.creationDate desc")
    List<Long> findLastInActiveCPTForContract(Contract contract);

    @Query("select cpt from ContractPaymentTerm cpt " +
            "where cpt.id > ?1 and " +
            "cpt.isActive = true and cpt.sourceId is not null ")
    Page<ContractPaymentTerm> findActiveCPTsWithNotNullSourceId(Long lastId, Pageable pageable);

    @Query("select cpt from ContractPaymentTerm cpt " +
            "where cpt.id > ?1 and " +
            "cpt.isActive = true and cpt.sourceId is not null and cpt.provider <> ?2 ")
    Page<ContractPaymentTerm> findActiveCPTsWithNotNullSourceIdAndProviderAndProviderNotEqual(Long lastId, EPaymentProvider provider, Pageable pageable);

    @Query(value = "select count(cpt.id) > 1 from ContractPaymentTerm cpt " +
            "where cpt.contract.id = ?1 and cpt.reason = 'SWITCHING' and cpt.creationDate > ?2")
    boolean existsSwitchingNationalitiesPayingViaCcPerCurrentMonth(Long contractId, Date d);

    @Query("select cpt from ContractPaymentTerm cpt " +
            "join cpt.contract c " +
            "where cpt.id > ?1 and c.status = 'CANCELLED' and c.dateOfTermination <= ?2 and " +
            "exists (select 1 from Payment p " +
                    "where p.contract = c and p.recurring = true and p.status not in ('RECEIVED', 'DELETED')) " +
            "order by cpt.id")
    Page<ContractPaymentTerm> findScheduledTokenDeletionsForToday(Long lastId, Date today, Pageable pageable);
}