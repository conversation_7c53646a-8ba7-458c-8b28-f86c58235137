package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Iterables;
import com.google.common.collect.Table;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.utils.PdfMerger;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.*;
import com.magnamedia.extra.DDFAsCsvHelper;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.PdfHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.DirectDebitService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.QueryService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Level;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 * 
 */

@RestController
@RequestMapping("/directdebitfile")
public class DirectDebitFileController extends BaseRepositoryController<DirectDebitFile> {

    @Autowired
    DirectDebitFileRepository directDebitFileRep;
    @Autowired
    ClientRepository clientRepository;
    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private DDFExportingConfigRepository ddfExportingConfigRepository;

    @Autowired
    DirectDebitRepository directDebitRepository;

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;

    @Autowired
    private DDFBatchForRPARepository ddfBatchForRPARepository;

    @Autowired
    private DirectDebitController directDebitCtrl;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Autowired
    private DDSignatureTempRepository ddSignatureTempRepository;

    @Autowired
    private DirectDebitService directDebitService;

    @Autowired
    private QueryService queryService;

    public static final String MANUAL_DD_BATCH_FILE = "manual_dd_batch_file";
    //Jirra ACC-2744
    public static final String DD_FORM_DOWNLOAD_POSITION = "dd_form_download_position";
    public static final String DDF_BATCH_FILE_RAR = "ddf_batch_file_rar";
    public static final String DDF_BATCH_FILE_CSV = "ddf_batch_file_dds";

    @Override
    protected ResponseEntity<?> updateEntity(DirectDebitFile entity) {
        entity.setCancellationTrigger(DirectDebitFileCancellationTrigger.MANUAL);
        return super.updateEntity(entity);
    }

    // ACC-1588
    @PreAuthorize("hasPermission('directdebitfile','advancesearch')")
    @PostMapping(value = "/advancesearch")
    @ResponseBody
    public ResponseEntity<?> advanceSearch(
            @RequestParam(name = "ddcontractpayments", required = false) Boolean ddContractPayments,
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId,
            @RequestParam(value = "contractId", required = false) Long contractId,
            Pageable pageable) {

        if (ddContractPayments == null) ddContractPayments = false;
        if (clientName == null) clientName = "";
        if (bankName != null && bankName.isEmpty()) bankName = null;

        //ACC-9316
        //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

        return new ResponseEntity(ddContractPayments ?
                (PageImpl) directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContract(
                        clientName, contractId, bankName, paymentTypeId,
                                new LocalDate().toDate(), pageable)
                        .map(obj -> projectionFactory.createProjection(DDFSearchProjection.class, obj)) :
                (PageImpl) directDebitFileRep.getPendingDDFsGroupByDDBankInfoGroup(pageable)
                        .map(obj -> projectionFactory.createProjection(DDFDataEntryProjection.class, obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','advanceSearchForSendDDToBankAcc8544')")
    @GetMapping(value = "/advanceSearchForSendDDToBankAcc8544")
    public ResponseEntity<?> advanceSearchForSendDDToBankAcc8544(
            @RequestParam(value = "clientName", required = false, defaultValue = "") String clientName,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId,
            @RequestParam(value = "contractId", required = false) Long contractId,
            Pageable pageable) {

        if (bankName != null && bankName.isEmpty()) bankName = null;

        return ResponseEntity.ok(queryService.advanceSearchForSendDDToBankAcc8544(clientName, contractId, bankName, paymentTypeId,
                pageable));
    }

    @PreAuthorize("hasPermission('directdebitfile','advanceSearchForDDDataEntryAcc8544')")
    @GetMapping(value = "/advanceSearchForDDDataEntryAcc8544")
    public ResponseEntity<?> advanceSearchForDDDataEntryAcc8544(Pageable pageable) {
        return ResponseEntity.ok(directDebitFileRep.getPendingDDFsGroupByDDBankInfoGroupAcc8544(
                /*new DateTime(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE)).toDate(),*/
                pageable));
    }

    //Jirra ACC-1588
    @PreAuthorize("hasPermission('directdebitfile','advancesearchcsv')")
    @RequestMapping(value = "/advancesearch/csv",
            method = RequestMethod.POST)
    @ResponseBody
    public void advanceSearchCSV(
            HttpServletResponse response,
            Sort sort,
            @RequestParam(name = "ddcontractpayments",
                    required = false) Boolean ddContractPayments,
            @RequestParam(name = "limit",
                    required = false) Integer limit,
            @RequestBody List<FilterItem> filters) throws Exception {

        if (ddContractPayments == null)
            ddContractPayments = false;

        SelectQuery<DirectDebitFile> query = getSearchQuery(filters, ddContractPayments, sort);

        if (limit == null)
            limit = 2000;
        InputStream is = null;
        
        try {
            if (ddContractPayments) {
                String[] namesOrdared =
                        {"client", "referenceNumber",
                                "housemaid", "contractID", "contractType",
                                "nationality", "paymentDate", "paymentType", "paymentMethod",
                                "creationDate", "amount", "numberOfPayments", "additionalDiscount",
                                "additionalDiscountNotes", "isActive", "type"};
                is = generateCsv(query, DDFContractPaymentSearchProjectionCsv2.class, namesOrdared, limit);
            } else {
                String[] namesOrdered =
                        {"client", "contractID", "referenceNumber",
                                "bankName", "startDate", "amount",
                                "expiryDate", "status", "uploaded", "signingDate", "resultDate", "notes",
                                "additionalDiscount", "additionalDiscountNotes", "isActive", "type"};
                is = generateCsv(query, DDFCsvProjection.class, namesOrdered, limit);
            }

            createDownloadResponse(response, "DDs.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-1588
    @PreAuthorize("hasPermission('directdebitfile','advancesearchcsv/sublist')")
    @RequestMapping(value = "/advancesearch/csv/sublist",
            method = RequestMethod.POST)
    public void advanceSearchCSVSubList(HttpServletResponse response, @RequestParam(name = "limit", required = false) Integer limit,
                                        @RequestBody List<Long> ids) throws Exception {

        if (ids == null || ids.isEmpty()) {
            response.setStatus(400);
            response.getWriter().write("you must select at least one direct debit file");
            response.getWriter().flush();
            response.getWriter().close();
            return;
        }

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.leftJoinFetch("directDebit", "directDebit");
        query.nestedLeftJoinFetch("directDebit", "contractPaymentTerm", "contractPaymentTerm");
        query.nestedLeftJoinFetch("contractPaymentTerm", "contract", "contract");
        query.nestedLeftJoinFetch("contract", "client");
        query.setAllowNestedJoinsWithPageable(true);

        query.filterBy("id", "IN", ids);
        if (limit == null)
            limit = 2000;
        String[] namesOrdered =
                {"client", "referenceNumber",
                        "housemaid", "contractID", "contractType",
                        "nationality", "paymentDate", "paymentType", "paymentMethod",
                        "creationDate", "amount", "numberOfPayments", "bankName", "additionalDiscount",
                        "additionalDiscountNotes", "isActive", "type"};
        InputStream is = generateCsv(query, DDFContractPaymentSearchProjectionCsv2.class, namesOrdered, limit);

        createDownloadResponse(response,
                "DDs.csv",
                is);
    }

    //Jirra ACC-2272
    @PreAuthorize("hasPermission('directdebitfile','advancesearchcsv/alllist/send')")
    @RequestMapping(value = "/advancesearch/csv/alllist/send", method = RequestMethod.POST)
    public ResponseEntity advanceSearchCSVAllList_Send(
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "contractId", required = false) Long contractId,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId) throws IOException {

        taskExecutor.execute(new Runnable() {
            String emails = CurrentRequest.getUser().getEmail();
            String threadClientName = clientName != null ? clientName : "";
            String threadBankName = bankName != null && bankName.isEmpty() ? null : bankName;

            @Override
            public void run() {

                //ACC-9316
                //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

                List<DirectDebitFile> directDebitFiles;
                directDebitFiles = directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContract(
                        threadClientName, contractId, threadBankName,
                        paymentTypeId, new LocalDate().toDate(), null).getContent();
                try {
                    String[] namesOrdered =
                            {"client", "referenceNumber",
                                    "housemaid", "contractID", "contractType",
                                    "nationality", "paymentDate", "paymentType", "paymentMethod",
                                    "creationDate", "amount", "numberOfPayments", "bankName", "additionalDiscount",
                                    "additionalDiscountNotes", "isActive", "type"};

                    generateCSVFileAndSendViaMail(emails, namesOrdered, namesOrdered, DDFContractPaymentSearchProjectionCsv2.class, "DDs", directDebitFiles);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
                }
            }
        });

        return ResponseEntity.ok("The file is too big and needs time to generate. We will send it to your email once done.!");
    }

    //Jirra ACC-2272
    @PreAuthorize("hasPermission('directdebitfile','advancesearchcsv/alllist/download')")
    @RequestMapping(value = "/advancesearch/csv/alllist/download", method = RequestMethod.POST)
    public void advanceSearchCSVAllList_Download(
            HttpServletResponse response,
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "contractId", required = false) Long contractId,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId) throws IOException {

        if (clientName == null)
            clientName = "";
        if (bankName != null && bankName.isEmpty())
            bankName = null;

        List<DirectDebitFile> directDebitFiles;

        //ACC-9316
        //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

        directDebitFiles = directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContract(
                clientName, contractId, bankName, paymentTypeId,
                new LocalDate().toDate(), null).getContent();

        String[] namesOrdered =
                {"client", "referenceNumber",
                        "housemaid", "contractID", "contractType",
                        "nationality", "paymentDate", "paymentType", "paymentMethod",
                        "creationDate", "amount", "numberOfPayments", "bankName", "additionalDiscount",
                        "additionalDiscountNotes", "isActive", "type"};
        InputStream is = null;
        
        try {
            is = new FileInputStream(CsvHelper.generateCsv(directDebitFiles, DDFContractPaymentSearchProjectionCsv2.class, namesOrdered, namesOrdered, "DDs", "csv"));

            createDownloadResponse(response, "DDs.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-1588
    @PreAuthorize("hasPermission('directdebitfile','advancesearchmail')")
    @RequestMapping(value = "/advancesearch/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity advanceSearchMail(Sort sort,
                                            @RequestParam(name = "ddcontractpayments",
                                                    required = false) Boolean fromContractPaymentsPage,
                                            @RequestBody List<FilterItem> filters) {

        SelectQuery<DirectDebitFile> query = getSearchQuery(filters, fromContractPaymentsPage, sort);

        Class projection;
        String[] namesOrdered;
        if (fromContractPaymentsPage) {
            namesOrdered = new String[]
                    {"client", "referenceNumber",
                            "housemaid", "contractID", "contractType",
                            "nationality", "paymentDate", "paymentType", "paymentMethod",
                            "creationDate", "amount", "numberOfPayments", "bankName", "additionalDiscount",
                            "additionalDiscountNotes", "isActive", "type"};
            projection = DDFContractPaymentSearchProjectionCsv2.class;
        } else {
            namesOrdered = new String[]
                    {"client", "contractID", "referenceNumber",
                            "bankName", "startDate", "amount",
                            "expiryDate", "status", "uploaded", "signingDate", "resultDate", "notes",
                            "additionalDiscount", "additionalDiscountNotes", "isActive", "type"};
            projection = DDFCsvProjection.class;
        }

        taskExecutor.execute(new Runnable() {
            String emails = CurrentRequest.getUser().getEmail();
            Class threadProjection = projection;
            String[] threadNamesOrdered = namesOrdered;

            @Override
            public void run() {
                try {
                    List<DirectDebitFile> dDFs = query.execute();
                    generateCSVFileAndSendViaMail(emails, threadNamesOrdered, threadNamesOrdered, threadProjection, "DDs", dDFs);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
                }
            }
        });

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    public void generateCSVFileAndSendViaMail(String emails, String[] headers, String[] names, Class projection, String fileName, List<DirectDebitFile> directDebitFiles) throws IOException {

        File file = CsvHelper.generateCsv(directDebitFiles, projection, headers, names, fileName, ".csv");

        sendFileByMail(emails, file);
    }

    @PreAuthorize("hasPermission('directdebitfile','advancesearch')")
    @RequestMapping(value = "/advancesearch2/page",
            method = RequestMethod.GET)
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.client.name",
            label = "Family Name",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.client.mobileNumber",
            label = "Client Mobile",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.id",
            label = "Contract ID",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.bankName",
            label = "Bank Name",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "applicationId",
            label = "DD ID",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "rejectionReason",
            label = "Rejection Reason",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "ddStatus",
            label = "Status",
            entity = DirectDebitFile.class,
            valuesApi = "/accounting/directDebit/getalldirectdebitstatus",
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "startDate",
            label = "Start Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "creationDate",
            label = "Signing Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "expiryDate",
            label = "Expiry Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "notes",
            label = "Notes",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    //Jirra ACC-762
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    @Searchable(fieldName = "directDebit.additionalDiscount",
            label = "DD Discount Amount",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management")
    public ResponseEntity<?> advanceSearch2(
            Pageable pageable) {

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.leftJoinFetch("directDebit", "directDebit");
        query.nestedLeftJoinFetch("directDebit", "contractPaymentTerm", "contractPaymentTerm");
        query.nestedLeftJoinFetch("contractPaymentTerm", "contract", "contract");
        query.nestedLeftJoinFetch("contract", "client");
        query.setAllowNestedJoinsWithPageable(true);

        query.filterBy(CurrentRequest.getSearchFilter());

        return new ResponseEntity<>(
                query.execute(pageable).map(
                        obj -> projectionFactory.createProjection(
                                DDFSearchProjection.class, obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','advancesearchcsv')")
    @RequestMapping(value = "/advancesearch2/csv",
            method = RequestMethod.GET)
    @ResponseBody
    public void advanceSearchCSV2(
            HttpServletResponse response,
            Sort sort,
            @RequestParam(name = "limit",
                    required = false) Integer limit) throws Exception {

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.leftJoinFetch("directDebit", "directDebit");
        query.nestedLeftJoinFetch("directDebit", "contractPaymentTerm", "contractPaymentTerm");
        query.nestedLeftJoinFetch("contractPaymentTerm", "contract", "contract");
        query.nestedLeftJoinFetch("contract", "client");
        query.setAllowNestedJoinsWithPageable(true);

        //Process Filters
        query.filterBy(CurrentRequest.getSearchFilter());
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", true, false);
        }

        String[] namesOrdered = {
                "client", "contractID", "referenceNumber",
                "bankName", "ddaRefNo", "startDate", "amount",
                "expiryDate", "status", "uploaded", "signingDate", "resultDate", "notes",
                "additionalDiscount", "additionalDiscountNotes", "isActive", "type"};

        String[] headers = {
                "Client", "Contract ID", "Reference Number",
                "Bank Name", "Bank Reference Number", "Start Date", "Amount",
                "Expiry Date", "Status", "Uploaded", "Signing Date", "Result Date", "Notes",
                "Additional Discount", "Additional Discount Notes", "Is Active", "Type"};
        if (limit == null)
            limit = 2000;
        for (DirectDebitFile ddf : query.execute()) {
            logger.log(Level.SEVERE, "MMM DDF ID: " + ddf.getId());
            logger.log(Level.SEVERE, "MMM DDF Additional discount notes: "
                    + project(Arrays.asList(ddf), DDFCsvProjection.class).get(0).getAdditionalDiscountNotes());
        }

        File csvFile = CsvHelper.generateCsv(query.execute(), DDFCsvProjection.class, headers, namesOrdered, "DDs");
        createDownloadResponse(response,
                "DDs.csv",
                new FileInputStream(csvFile));
    }

    @PreAuthorize("hasPermission('directdebitfile','advancesearchmail')")
    @RequestMapping(value = "/advancesearch2/mail",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity advanceSearchMail2(Sort sort) {

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        query.leftJoinFetch("directDebit", "directDebit");
        query.nestedLeftJoinFetch("directDebit", "contractPaymentTerm", "contractPaymentTerm");
        query.nestedLeftJoinFetch("contractPaymentTerm", "contract", "contract");
        query.nestedLeftJoinFetch("contract", "client");
        query.setAllowNestedJoinsWithPageable(true);

        //Process Filters
        query.filterBy(CurrentRequest.getSearchFilter());
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", true, false);
        }

        taskExecutor.execute(new Runnable() {
            String emails = CurrentRequest.getUser().getEmail();

            Integer pageIndex = 0;

            Page<DirectDebitFile> page;


            String[] headers = {
                    "Client", "Contract ID", "Reference Number",
                    "Bank Name", "Bank Reference Number", "Start Date", "Amount",
                    "Expiry Date", "Status", "Uploaded", "Signing Date", "Result Date", "Notes",
                    "Additional Discount", "Additional Discount Notes", "Is Active", "Type"};

            String[] namesOrdered = {
                    "client", "contractID", "referenceNumber",
                    "bankName", "ddaRefNo", "startDate", "amount",
                    "expiryDate", "status", "uploaded", "signingDate", "resultDate", "notes",
                    "additionalDiscount", "additionalDiscountNotes", "isActive", "type"};

            @Override
            public void run() {
                do {
                    Pageable pageable = PageRequest.of(pageIndex++, 5000);
                    page = query.execute(pageable);
                    List<DirectDebitFile> directDebitFiles = page.getContent();

                    try {
                        generateCSVFileAndSendViaMail(emails, headers, namesOrdered, DDFCsvProjection.class, "Direct Debit File Report", directDebitFiles);
                    } catch (IOException ex) {
                        logger.log(Level.SEVERE, "advanceSearchMail2");
                        logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(ex));
                    }

                } while (page.hasNext());

            }
        });

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    //Jirra ACC-1604
    @PreAuthorize("hasPermission('directdebitfile','exportmultipleddfs')")
    @RequestMapping(value = "/exportmultipleddfs",
            method = RequestMethod.POST)
    @Transactional
    public ResponseEntity exportMultipleDDFiles(
            @RequestBody List<Long> ids,
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "contractId", required = false) Long contractId,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId) throws IOException {

        if (clientName == null)
            clientName = "";
        if (bankName != null && bankName.isEmpty())
            bankName = null;
        if (ids != null && !ids.isEmpty()) {
            //ACC-9316
            //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

            List<DirectDebitFile> ddfs = directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContractAndIdsIn(
                    clientName, contractId, bankName, paymentTypeId,
                    new LocalDate().toDate(), ids);
            if (ddfs == null || ddfs.isEmpty() || ddfs.size() != ids.size())
                throw new RuntimeException("DDFs List changed!! please refresh page and try again.");
        }
        User user = CurrentRequest.getUser();
//        if (user == null || user.getPositions() == null || user.getPositions().isEmpty() || !user.getPositions().stream().anyMatch(position -> position.getCode().equalsIgnoreCase(DD_FORM_DOWNLOAD_POSITION))) {
//            return new ResponseEntity("you don't have the required permission", HttpStatus.UNAUTHORIZED);
//        }

        if (ids == null || ids.isEmpty()) {
            return new ResponseEntity("you must select at least one direct debit file", HttpStatus.BAD_REQUEST);
        }

        List<DirectDebitFile> directDebitFiles = directDebitFileRep.findAll(ids);

        return new ResponseEntity(prepareDDFListForExport(directDebitFiles), HttpStatus.OK);
    }

    //Jirra ACC-1604
    @PreAuthorize("hasPermission('directdebitfile','exportallddfs')")
    @RequestMapping(value = "/exportallddfs",
            method = RequestMethod.POST)
    @Transactional
    public ResponseEntity exportAllDDFiles(@RequestParam(value = "clientName", required = false) String clientName,
                                           @RequestParam(value = "contractId", required = false) Long contractId,
                                           @RequestParam(value = "bankName", required = false) String bankName,
                                           @RequestParam(value = "paymentType", required = false) Long paymentTypeId) throws IOException {
        User user = CurrentRequest.getUser();
//        if (user == null || user.getPositions() == null || user.getPositions().isEmpty() || !user.getPositions().stream().anyMatch(position -> position.getCode().equalsIgnoreCase(DD_FORM_DOWNLOAD_POSITION))) {
//            return new ResponseEntity("you don't have the required permission", HttpStatus.UNAUTHORIZED);
//        }

        if (clientName == null)
            clientName = "";
        if (bankName != null && bankName.isEmpty())
            bankName = null;

        List<DirectDebitFile> directDebitFiles;
        //ACC-9316
        //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

        directDebitFiles = directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContract(
                clientName, contractId, bankName, paymentTypeId,
                new LocalDate().toDate(), null).getContent();

        return new ResponseEntity(prepareDDFListForExport(directDebitFiles), HttpStatus.OK);
    }

    private Collection<DDFAsCsvHelper.DDFList> prepareDDFListForExport(List<DirectDebitFile> directDebitFiles) {
        DDFExportingConfig ddfAutomaticExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
        DDFExportingConfig ddfManualExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");

        DDFAsCsvHelper ddfAutomaticBeans = new DDFAsCsvHelper(ddfAutomaticExportingConfig);
        DDFAsCsvHelper ddfManualBeans = new DDFAsCsvHelper(ddfManualExportingConfig);

        for (DirectDebitFile ddf : directDebitFiles) {
            if (ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC))
                ddfAutomaticBeans.put(ddf.getId());
            else
                ddfManualBeans.put(ddf.getId());
        }

        Collection<DDFAsCsvHelper.DDFList> ddfLists = new ArrayList();
        if (ddfAutomaticBeans.values().size() > 0)
            ddfLists.addAll(ddfAutomaticBeans.values());
        if (ddfManualBeans.values().size() > 0)
            ddfLists.addAll(ddfManualBeans.values());

        return ddfLists;
    }


    //Jirra ACC-1604
    @PreAuthorize("hasPermission('directdebitfile','downloadbatch')")
    @RequestMapping(value = "/downloadbatch",
            method = RequestMethod.GET)
    public void downloadBatchFile(@RequestParam("ids") List<Long> ids, @RequestParam("downloadBatch") Boolean downloadBatch,
                                  @RequestParam("fileName") String fileName,
                                  HttpServletResponse response) throws IOException {
        List<DirectDebitFile> directDebitFiles = directDebitFileRep.findByIdInAndStatus(ids, DirectDebitFileStatus.NOT_SENT);

        DDFExportingConfig ddfExportingConfig;
        if (directDebitFiles.get(0).getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) {
            ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
        } else {
            ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");
        }

        // download batch file
        if (downloadBatch) {
            if (!Pattern.compile(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_REGEX_TO_CHECK_FILE_NAME_IN_DDF_DOWNLOAD_BATCH_API)).matcher(fileName)
                    .matches()) {
                throw new BusinessException("file name is not valid");
            }

            DDFAsCsvHelper ddfBeans = new DDFAsCsvHelper(ddfExportingConfig);
            DDFAsCsvHelper.DDFList ddfList = ddfBeans.new DDFList();
            ddfList.setFileName(fileName);
            ddfList.setIds(directDebitFiles.stream().map(ddf -> ddf.getId()).collect(Collectors.toList()));
            File csvFile = ddfBeans.generateCsvFile(ddfList);

            createDownloadResponse(response,
                    fileName + ".dds",
                    new FileInputStream(csvFile));

            return;
        }

        File zipFile = generateBatchZipFile(directDebitFiles);

        createDownloadResponse(response,
                "dds.rar",
                new FileInputStream(zipFile));
    }

    //Jirra ACC-2633
    boolean generationForRPATriggered = false;

    //Jirra ACC-2571
    @PreAuthorize("hasPermission('directdebitfile','generatebatch/rpa')")
    @RequestMapping(value = "/generatebatch/rpa",
            method = RequestMethod.POST)
    @Transactional
    public ResponseEntity generateBatchFileByRPA(
            @RequestBody List<Long> ids,
            @RequestParam(value = "clientName", required = false) String clientName,
            @RequestParam(value = "contractId", required = false) Long contractId,
            @RequestParam(value = "bankName", required = false) String bankName,
            @RequestParam(value = "paymentType", required = false) Long paymentTypeId) throws IOException {

        if (clientName == null)
            clientName = "";
        if (bankName != null && bankName.isEmpty())
            bankName = null;
        List<DirectDebitFile> allDirectDebitFiles = new ArrayList<>();
        if (ids != null && !ids.isEmpty()) {
            //ACC-9316
            //String startDate = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_ACC_6003_REFERENCE_DATE);

            allDirectDebitFiles = directDebitFileRep.getPendingDDFsGroupByApplicationIdFilteredByContractAndIdsIn(
                    clientName, contractId, bankName, paymentTypeId,
                    new LocalDate().toDate(), ids);
            if (allDirectDebitFiles == null || allDirectDebitFiles.isEmpty() || allDirectDebitFiles.size() != ids.size())
                throw new RuntimeException("DDFs List changed!! please refresh page and try again.");
        }
        if (generationForRPATriggered) {
            throw new RuntimeException("this request has been triggered before and still processing, please try again later.");
        }

        try {
            generationForRPATriggered = true;

            if (allDirectDebitFiles == null || allDirectDebitFiles.isEmpty()) {
                throw new RuntimeException("either no file was selected, or all the selected files have been processed before.");
            }

            Collection<DDFAsCsvHelper.DDFList> ddfLists = prepareDDFListForExport(allDirectDebitFiles);

            for (DDFAsCsvHelper.DDFList ddfList : ddfLists) {

                // download batch file
                List<DirectDebitFile> directDebitFiles = directDebitFileRep.findAll(ddfList.getIds());
                if (directDebitFiles == null || directDebitFiles.isEmpty())
                    continue;

                DDFExportingConfig ddfExportingConfig;
                if (directDebitFiles.get(0).getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) {
                    ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
                } else {
                    ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");
                }
                DDFAsCsvHelper ddfBeans = new DDFAsCsvHelper(ddfExportingConfig);
                File csvFile = ddfBeans.generateCsvFile(ddfList);
                //
                File zipFile = generateBatchZipFile(directDebitFiles);

                DDFBatchForRPA ddfBatchForRPA = new DDFBatchForRPA();
                Attachment attachment = Storage.storeTemporary("dds.rar", new FileInputStream(zipFile), DDF_BATCH_FILE_RAR, true);
                Attachment attachment2 = Storage.storeTemporary(ddfList.getFileName() + ".dds", new FileInputStream(csvFile), DDF_BATCH_FILE_CSV, true);
                ddfBatchForRPA.addAttachment(attachment);
                ddfBatchForRPA.addAttachment(attachment2);
                ddfBatchForRPA.setIds(ddfList.getIds().toString().substring(1, ddfList.getIds().toString().length() - 1));
                ddfBatchForRPARepository.save(ddfBatchForRPA);

                for (DirectDebitFile ddf : directDebitFiles) {
                    ddf.setDownloadedByRPA(true);
                    ddf.setDdfBatchForRpaId(ddfBatchForRPA.getId());        // ACC-9315
                    directDebitFileRep.save(ddf);
                }
            }
            generationForRPATriggered = false;

            return new ResponseEntity("Done", HttpStatus.OK);
        } catch (Exception e) {
            generationForRPATriggered = false;
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
            throw e;
        }
    }

    public File generateBatchZipFile(List<DirectDebitFile> directDebitFiles) throws IOException {
        //download rar file containing ddf activation files
        File zipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + ".rar")
                .toFile();

        String zipFileName = zipFile.getPath();
        FileOutputStream fos = new FileOutputStream(zipFileName);
        ZipOutputStream zos = new ZipOutputStream(fos);

        for (DirectDebitFile directDebitFile : directDebitFiles) {
            if (!directDebitFile.getStatus().equals(DirectDebitFileStatus.NOT_SENT)) continue;

            zos.putNextEntry(new ZipEntry(directDebitFile.getApplicationId() + ".pdf"));
            zos.write(PdfHelper.getPdfPages(directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION), "firstImage"));
            zos.closeEntry();
        }

        zos.close();

        return zipFile;
    }

    //Jirra ACC-1604
    @PreAuthorize("hasPermission('directdebitfile','sendtobank')")
    @RequestMapping(value = "/sendtobank", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity sendToBankAPI(@RequestBody List<Long> ids) {
        sendToBank(ids, false);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    public List<Long> sendToBank(List<Long> ids, boolean downloadedByRP) {

        if (ids != null && !ids.isEmpty()) {
            List<DirectDebitFile> directDebitFiles = directDebitFileRep.getPendingDDFsAndIdsIn(ids, new LocalDate().toDate(), downloadedByRP);
            if (directDebitFiles == null || directDebitFiles.isEmpty() || directDebitFiles.size() != ids.size())
                throw new RuntimeException("Child DDs list has changed... please refresh page and try again");
        }

        List<DirectDebitFile> successfullySentDDFs = new ArrayList();
        List<DirectDebitFile> directDebitFiles = directDebitFileRep.findAll(ids);
        for (DirectDebitFile ddf : directDebitFiles) {
            Contract contract = ddf.getDirectDebit().getContractPaymentTerm().getContract();
            logger.info("DDF Status: " + ddf.getStatus());

            logger.info("is Contract Cancelled within First X Days: " + contract.isCancelledWithinFirstXDays());
            if(contract.isCancelledWithinFirstXDays()) {
                throw new RuntimeException("DD '" + ddf.getApplicationId() + "' is for contract CANCELED within 3 days, it should be excluded");
            }

            if (ddf.getStatus().equals(DirectDebitFileStatus.NOT_SENT)) {
                ddf.setStatus(DirectDebitFileStatus.SENT);
                ddf.setDdStatus(DirectDebitStatus.PENDING);
                directDebitSignatureService.updateSignatureStatus(ddf, DirectDebitSignatureStatus.UNDER_PROCESS);

                ddf.setDownloadedByRPA(false); // ACC-2704
                successfullySentDDFs.add(ddf);
            }
        }

        return directDebitFileRep.save(successfullySentDDFs).stream()
                .map(ddf -> ddf.getId()).collect(Collectors.toList());
    }

    //Jirra ACC-2621
    @Transactional
    public void unSendToBank(@RequestBody List<Long> ids) {

        List<DirectDebitFile> directDebitFiles = directDebitFileRep.findAll(ids);
        for (DirectDebitFile ddf : directDebitFiles) {
            ddf.setDownloadedByRPA(false);
            directDebitFileRep.save(ddf);
        }
        directDebitFileRep.save(directDebitFiles);
    }

    //Jirra ACC-2571
    @PreAuthorize("hasPermission('directdebitfile','sendtobankbyrpa')")
    @RequestMapping(value = "/sendtobankbyrpa/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity sendToBankByRPA(@PathVariable("id") DDFBatchForRPA ddfBatchForRPA) {

        sendDDFsToBankByRPA(ddfBatchForRPA, DDFBatchStatus.SENT_TO_BANK);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //Jirra ACC-3264
    @PreAuthorize("hasPermission('directdebitfile','sendtobankbyrpa')")
    @RequestMapping(value = "/sendtobankbyrpa/{id}/{rpaStatus}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity sendDDFsToBankByRPA_API(@PathVariable("id") DDFBatchForRPA ddfBatchForRPA,
                                                  @PathVariable("rpaStatus") DDFBatchStatus batchStatus) {
        sendDDFsToBankByRPA(ddfBatchForRPA, batchStatus);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    private void sendDDFsToBankByRPA(DDFBatchForRPA ddfBatchForRPA, DDFBatchStatus batchStatus) {
        ddfBatchForRPA.setStatus(batchStatus);
        ddfBatchForRPARepository.save(ddfBatchForRPA);
        List<Long> ids = Arrays.stream(ddfBatchForRPA.getIds().split(",")).map(x -> Long.parseLong(x.trim())).collect(Collectors.toList());
        this.sendToBank(ids, true);
    }

    //Jirra ACC-2571
    @PreAuthorize("hasPermission('directdebitfile','unsendtobankbyrpa')")
    @RequestMapping(value = "/unsendtobankbyrpa/{id}",
            method = RequestMethod.POST)
    @Transactional
    public ResponseEntity unSendToBankByRPA(@PathVariable("id") DDFBatchForRPA ddfBatchForRPA) {

        ddfBatchForRPA.setStatus(DDFBatchStatus.NOT_SENT);
        ddfBatchForRPA.setSendByRPA(Boolean.FALSE);
        ddfBatchForRPARepository.save(ddfBatchForRPA);
        List<Long> ids = Arrays.stream(ddfBatchForRPA.getIds().split(",")).map(x -> Long.parseLong(x.trim())).collect(Collectors.toList());
        this.unSendToBank(ids);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //Jirra ACC-3198
    @PreAuthorize("hasPermission('directdebitfile','process-rpa')")
    @RequestMapping(value = "/process-rpa/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity processRPA(@PathVariable("id") DDFBatchForRPA ddfBatchForRPA) {

        ddfBatchForRPA.setStatus(DDFBatchStatus.UNDER_PROCESS);
        ddfBatchForRPARepository.save(ddfBatchForRPA);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //Jirra ACC-1588
    private SelectQuery<DirectDebitFile> getSearchQuery(List<FilterItem> filters, Boolean ddContractPayments, Sort sort) {
        if (ddContractPayments) {
            List<BigInteger> idsBigInteger = directDebitFileRep.findDDFWithNoSignature();
            if (idsBigInteger != null && !idsBigInteger.isEmpty()) {
                List<Long> ids =
                        idsBigInteger.stream()
                                .map(x -> x.longValue())
                                .collect(Collectors.toList());
                FilterItem f = new FilterItem("id", "NOT IN", ids);
                filters.add(f);
            }
            logger.log(Level.SEVERE, "break code redundancy warning");
        }

        SelectQuery<DirectDebitFile> query = new SelectQuery(DirectDebitFile.class);
        //Process Filters
        SelectFilter selectFilter = new SelectFilter();

        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(DirectDebitFile.class));
        }

        query.filterBy(selectFilter);
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", true, false);
        }

        return query;
    }

    private void sendFileByMail(
            String emails, File file) {

        logger.log(Level.SEVERE, "sendFileByMail emails: " + emails);

        if (emails != null && !emails.isEmpty()) {
            try {
                Setup.getApplicationContext()
                        .getBean(MessagingService.class)
                        .sendEmailToOfficeStaffWithAttachments("direct_debit_file_report_email",
                                new HashMap<>(), emails, Collections.singletonList(Storage.storeTemporary(file.getName(),
                                        new FileInputStream(file),null,false)),
                                "Direct Debit File Report");
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
    }

    //Jirra ACC-1588
    @PreAuthorize("hasPermission('directdebitfile','send')")
    @RequestMapping(value = "/send/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> send(@PathVariable("id") DirectDebitFile ddf) {

        if (!ddf.getStatus().equals(DirectDebitFileStatus.NOT_SENT))
            throw new RuntimeException("File Status Must be Not Sent");

        sendToBank(Arrays.asList(ddf.getId()), false);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @Override
    public BaseRepository<DirectDebitFile> getRepository() {
        return directDebitFileRep;
    }

    //Jirra SAL-2034
    @PreAuthorize("hasPermission('directdebitfile','checkApprovedDD')")
    @RequestMapping(value = "/checkApprovedDD/{clientId}", method = RequestMethod.GET)
    public ResponseEntity checkApprovedDD(@PathVariable("clientId") Client client) {
        Map response = new HashMap();
        List attachments = new ArrayList();
        response.put("hasApproved", false);
        response.put("attachments", null);
        DirectDebitFile directDebitFile = directDebitFileRep.findFirstByStatusAndDirectDebit_ContractPaymentTerm_Contract_ClientOrderByCreationDateDesc(DirectDebitFileStatus.APPROVED, client);

        if (directDebitFile == null)
            return new ResponseEntity(response, HttpStatus.OK);

        for (String attachmentTag :
                Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                        ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                        ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN)) {
            Attachment attachment = directDebitFile.getAttachment(attachmentTag);
            if (attachment != null) {
                Map item = new HashMap();
                item.put("name", getNameFromTag(attachmentTag));
                item.put("id", attachment.getId());
                attachments.add(item);
            }
        }

        DirectDebitSignature directDebitSignature = directDebitFile.getDirectDebitSignature();
        if (directDebitSignature != null) {
            Map item = new HashMap();
            item.put("name", getNameFromTag(DirectDebitFile.FILE_TAG_DD_SIGNATURE));
            item.put("id", directDebitSignature.getSignatureAttachment().getId());
            attachments.add(item);
        }

        if (!attachments.isEmpty()) {
            response.put("hasApproved", true);
            response.put("attachments", attachments);
        }
        return new ResponseEntity(response, HttpStatus.OK);
    }

    private String getNameFromTag(String tag) {

        if (tag.equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME))
            return "accountName";

        if (tag.equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID))
            return "eid";

        if (tag.equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN))
            return "iban";

        if (tag.equals(DirectDebitFile.FILE_TAG_DD_SIGNATURE))
            return "signature";

        return "";
    }


    //Jirra ACC-2333
    @PreAuthorize("hasPermission('directdebitfile','/fill-bank-name/data-correction')")
    @RequestMapping(value = "/fill-bank-name/data-correction", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity fillBankNameDataCorrection(@RequestBody List<Long> ddfIDs,
                                                     @RequestParam("iban") String iban,
                                                     @RequestParam("bankName") String bankName, @RequestParam("bank") PicklistItem bank,
                                                     @RequestParam("eid") String eid, @RequestParam("accountName") String accountName,
                                                     @RequestParam(value = "clientName", required = false) String clientName,
                                                     @RequestParam(value = "clientTitleId", required = false) PicklistItem clientTitle,
                                                     @RequestParam(value = "nationalityId", required = false) PicklistItem nationality,
                                                     @RequestParam(value = "updateMasterDD", required = false) Boolean updateMasterDD) {

        if (iban == null || iban.isEmpty() || bank == null || bankName == null || bankName.isEmpty() ||
                eid == null || eid.isEmpty() || accountName == null || accountName.isEmpty())
            throw new RuntimeException("Bad Request, please make sure that u r sending all required data, (iban, bank, bankName, eid, accountName)");

        DirectDebitController directDebitController = Setup.getApplicationContext().getBean(DirectDebitController.class);
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        ContractPaymentTermRepository contractPaymentTermRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        ClientRepository clientRepository = Setup.getRepository(ClientRepository.class);
        ClientDocumentRepository clientDocumentRepository = Setup.getRepository(ClientDocumentRepository.class);

        List<String> confirmedTags = Arrays.asList(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID, ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);

        List<DirectDebitFile> ddfs = getRepository().findAll(ddfIDs);
        for (DirectDebitFile ddf : ddfs) {
            if (!Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.PENDING_DATA_ENTRY, DirectDebitStatus.IN_COMPLETE).contains(ddf.getDdStatus()))
                throw new RuntimeException("Invalid DD_Status for DD with ID: " + ddf.getId());

            Attachment ddActivationFile = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
            if (ddActivationFile != null) {
                attachementRepository.delete(ddActivationFile);
            }

            ddf.setAccountName(accountName);
            ddf.setBankName(bankName);
            ddf.setIbanNumber(iban);
            ddf.setEid(eid);
            ddf.setConfirmedBankInfo(true);
            ddf.setDdDataEntryNotificationSent(false);
            ddf.setDdStatus(DirectDebitStatus.PENDING);
            if (!directDebitService.createDirectDebitActivationAttachmentIfNotExist(ddf)) getRepository().save(ddf);

            DirectDebit dd = ddf.getDirectDebit();
            ContractPaymentTerm cpt = dd.getContractPaymentTerm();
            if (updateMasterDD != null && updateMasterDD) {
                dd.setIbanNumber(iban);
                dd.setBankName(bankName);
                dd.setBank(bank);
                dd.setEid(eid);
                dd.setAccountName(accountName);
                dd.setConfirmedBankInfo(true);
                if (dd.getStatus().equals(DirectDebitStatus.PENDING_DATA_ENTRY)) {
                    dd.setStatus(DirectDebitStatus.PENDING);
                    dd.setMStatus(DirectDebitStatus.PENDING);
                }
                // update attachments
                for (String tag : confirmedTags) {
                    Attachment ddAtt = dd.getAttachment(tag);
                    Attachment directDebitAtt = ddf.getAttachment(tag);
                    if (directDebitAtt != null) {
                        logger.log(Level.SEVERE, "Regenerate DD Attachment, with Tag: " + tag);
                        if (ddAtt != null) {
                            attachementRepository.delete(ddAtt);
                        }
                        dd.addAttachment(directDebitAtt);
                    }
                }
                directDebitRepository.save(dd);
            }

            // update CPT bank info by dd
            cpt.setAccountName(accountName);
            cpt.setIbanNumber(iban);
            cpt.setEid(eid);
            cpt.setBankName(bankName);
            cpt.setBank(bank);
            confirmedTags.forEach(tag -> {
                Attachment cptAtt = cpt.getAttachment(tag);
                Attachment directDebitFileAtt = ddf.getAttachment(tag);
                logger.log(Level.SEVERE, "Regenerate CPT Attachment, with Tag: " + tag);
                if (directDebitFileAtt != null) {
                    if (cptAtt != null)
                        attachementRepository.delete(cptAtt);
                    cpt.addAttachment(directDebitFileAtt);
                }
            });
            contractPaymentTermRepository.save(cpt);

            //Jirra ACC-1579
            Client client = clientRepository.findOne(cpt.getContract().getClient().getId());

            client.setEid(eid != null && !eid.isEmpty() ? eid : null);
            //SAL-2034 update client name
            //Jirra ACC-1964
            if (clientName != null && !clientName.isEmpty())
                client.setName(clientName);
            if (clientTitle != null)
                client.setTitle(clientTitle);
            if (nationality != null)
                client.setNationality(nationality);

            clientRepository.save(client);

            // jira acc-1579
            Attachment att = ddf.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
            if (att != null) {
                ClientDocument cDoc = new ClientDocument();
                cDoc.setAttachments(Arrays.asList(att));
                cDoc.setName("EMIRATES_ID_FRONT_SIDE");
                cDoc.setClient(client);
                logger.log(Level.SEVERE, "break Code Redundancy alert");
                cDoc.setUploadDate(new Date(System.currentTimeMillis()));
                cDoc.setType(Setup.getItem("ClientDocumentType", "EMIRATES_ID_FRONT_SIDE"));
                clientDocumentRepository.save(cDoc);
            }
            logger.log(Level.SEVERE, "DDF Activation File generated successfully");
        }
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //Jirra ACC-3095
    @PreAuthorize("hasPermission('directdebitfile','/regenerate-dd-form')")
    @RequestMapping(value = "/regenerate-dd-form", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity regenerateDDForm(@RequestBody List<Long> ddfIDs) {

        DirectDebitController directDebitController = Setup.getApplicationContext().getBean(DirectDebitController.class);
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);

        List<DirectDebitFile> ddfs = getRepository().findAll(ddfIDs);
        for (DirectDebitFile ddf : ddfs) {
            if (!Arrays.asList(DirectDebitStatus.PENDING, DirectDebitStatus.CONFIRMED).contains(ddf.getDdStatus()))
                throw new RuntimeException("Invalid DD_Status for DDF with ID: " + ddf.getId());

            Attachment ddActivationFile = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
            if (ddActivationFile != null) {
                attachementRepository.delete(ddActivationFile);
            }

            ddf.setConfirmedBankInfo(true);
            ddf.setDdDataEntryNotificationSent(false);

            directDebitService.createDirectDebitActivationAttachmentIfNotExist(ddf);
        }

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','/getDDFsFormsForClients')")
    @PostMapping(value = "/getDDFsFormsForClients")
    public void getDDFsFormsForClients(
            @RequestBody List<Long> ddfIDs,
            HttpServletResponse response) throws Exception {

        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            PdfDocument pdfDoc = new PdfDocument(new PdfWriter(byteArrayOutputStream));
            PdfMerger merger = new PdfMerger(pdfDoc);
            int pageIndex = 0;
            Page<DirectDebitFile> page;

            do {
                page = directDebitFileRep.findByIdIn(ddfIDs, PageRequest.of(pageIndex++, 100));
                List<DirectDebitFile> ddfList = page.getContent();

                for (DirectDebitFile directDebitFile : ddfList) {
                    InputStream fileStream = null;
                    
                    try {
                        fileStream = directDebitService.generateDDForm(directDebitFile, null, false);
                        if (fileStream == null) continue;

                        PdfDocument srcDoc = new PdfDocument(new PdfReader(fileStream));
                        merger.merge(srcDoc, 1, 1);
                        srcDoc.close();
                    } finally {
                        StreamsUtil.closeStream(fileStream);
                    }
                }
            } while (page.hasNext());

            pdfDoc.close();
            byte[] contents = byteArrayOutputStream.toByteArray();

            createDownloadResponse(response, "DDs Form.pdf", new ByteArrayInputStream(contents));
        } catch (Exception e) {
            response.setStatus(500);
            response.getWriter().write(ExceptionUtils.getStackTrace(e));
            response.getWriter().flush();
            response.getWriter().close();

            return;
        }
    }

    // Jirra ACC-2726
    @PreAuthorize("hasPermission('directdebitfile','/dd-data-entry')")
    @RequestMapping(value = "dd-data-entry/{id}", method = RequestMethod.GET)
    public ResponseEntity getForDDDataEntry(
            @PathVariable("id") DirectDebitFile directDebitFile) throws IOException {

        //ACC-4657
        Map<String, Object> resultMap = new HashMap<>();
        ContractPaymentTerm contractPaymentTerm = directDebitFile.getDirectDebit().getContractPaymentTerm();
        boolean enableAccountName = directDebitFileRep
                .existsByDirectDebit_ContractPaymentTerm_Contract_ClientAndRejectCategory(contractPaymentTerm.getContract().getClient());

        List<Attachment> pendingOcrAttachments = directDebitFile.getAttachments()
                .stream()
                .filter(a ->
                        a.getTag().equals(ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR) &&
                        !a.getExtension().equalsIgnoreCase("pdf"))
                .collect(Collectors.toList());

        if (!pendingOcrAttachments.isEmpty()) {
            Map<String, Object> map = new HashMap<>();
            map.put("pendingOcrAttachments", pendingOcrAttachments);
            map.put("enableAccountName", enableAccountName);

            map.putAll(Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class)
                    .extractBankInfoFromPhotos(map));

            if (map.size() > 2) {
                if (map.get("eid") != null) directDebitFile.setEid((String) map.get("eid"));
                if (map.get("iban") != null) directDebitFile.setIbanNumber((String) map.get("iban"));
                if (map.get("accountName") != null)
                    directDebitFile.setAccountName((String) map.get("accountName"));

                directDebitFileRep.save(directDebitFile);
            }
        }

        resultMap.put("eid", directDebitFile.getEid());
        resultMap.put("ibanNumber", directDebitFile.getIbanNumber());
        resultMap.put("accountName", directDebitFile.getAccountName());
        resultMap.put("enableAccountName", enableAccountName);
        resultMap.put("id", directDebitFile.getId());
        resultMap.put("attachments", directDebitFile.getSecuredAttachments());
        resultMap.put("directDebit", projectionFactory.createProjection(
                DdDataEntryProjection.class,
                directDebitFile.getDirectDebit()));
        resultMap.put("bankName", directDebitFile.getBankName());

        resultMap.putAll(Setup.getApplicationContext()
                .getBean(DirectDebitService.class)
                .canUpdateClientName(directDebitFile.getDirectDebit()));

        return ResponseEntity.ok(resultMap);
    }

    @PreAuthorize("hasPermission('directdebitfile','/dd-data-entry-validateClient')")
    @RequestMapping(value = "dd-data-entry-validateClient/{id}", method = RequestMethod.GET)
    public ResponseEntity ddDataEntryValidateClient_API(
            @PathVariable("id") DirectDebitFile directDebitFile,
            @RequestParam(value = "eid") String eid,
            @RequestParam(value = "iban") String iban) {

        return ResponseEntity.ok(ddDataEntryValidateClient(directDebitFile, eid, iban));
    }

    public Map<String, String> ddDataEntryValidateClient(
            DirectDebitFile directDebitFile,
            String eid,
            String iban) {

        Client currentClient = directDebitFile.getDirectDebit().getContractPaymentTerm().getContract().getClient();

        List<Contract> contracts = currentClient.getActiveContracts();
        Contract currentContract = contracts != null && !contracts.isEmpty() ?
                currentClient.getActiveContracts().get(0) : null;

        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("status", "active");
        resultMap.put("msg", "");

        if (currentContract == null) return resultMap;

        String applyValidation =
                currentContract.getContractProspectType().getCode().equals("maids.cc_prospect") ?
                        Setup.getParameter(Setup.getModule("sales"), "cc_contract_auto_check_blocked_client") :
                        Setup.getParameter(Setup.getModule("sales"), "mv_contract_auto_check_blocked_client");

        if (applyValidation.equals("true") && eid != null && !eid.isEmpty() &&
                iban != null && !iban.isEmpty()) {

            List<Client> matchingClientList = clientRepository.findClientByEidAndIban(
                    eid, iban, currentClient);

            for (Client client : matchingClientList) {
                //if the client is blocked create compliant to-do
                if (client.isBlocked()) {
                    Contract lastContract = client.getActiveOrLastContract();
                    if (lastContract != null)
                        DDUtils.createComplaintNoMaidFromComplaints(client.getId(), lastContract);

                    resultMap.put("status", "blocked");
                    resultMap.put("msg", "Client Blocked");
                    return resultMap;
                }

                //if the client is fired cancel the contract and send sms and email and throw exception
                if (client.getFired()) {
                    // 1- cancel the contract
                    DDUtils.fireClientAndTerminateContractFromClientMgmt(currentContract.getId());
                    // 2- send sms and email
                    DDUtils.sendFiredClientSmsAndEmailFromSales(currentContract.getId());

                    // 3- throw exception
                    resultMap.put("status", "fired");
                    resultMap.put("msg", "The client is a fired client, the system will cancel the contract and the DDs associated with it.");
                    return resultMap;
                }
            }
        }

        return resultMap;
    }

    // Jirra ACC-2744
    @PreAuthorize("hasPermission('directdebitfile','download-dd-form')")
    @RequestMapping(value = {"download-dd-form/{id}"}, method = {RequestMethod.GET})
    public void downloadDDForm(@PathVariable("id") DirectDebitFile directDebitFile, HttpServletResponse response) throws Exception {
        User user = CurrentRequest.getUser();
        if (user == null || user.getPositions() == null || user.getPositions().isEmpty() || !user.getPositions().stream().anyMatch(position -> position.getCode().equalsIgnoreCase(DD_FORM_DOWNLOAD_POSITION))) {
            response.setStatus(401);
            response.getWriter().write("you don't have the required permission");
            response.getWriter().flush();
            response.getWriter().close();
            return;
        } else {
            createDownloadResponse(response, "DD Form.pdf", Storage.getStream(directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION)));
        }
    }

    // Jirra ACC-2741
    @NoPermission
    @RequestMapping(value = "dd-form-no-signature/{id}", method = RequestMethod.GET)
    public void getDDFormWithoutSignature(@PathVariable("id") DirectDebitFile directDebitFile, HttpServletResponse response) throws Exception {
        Attachment activationAttachment = directDebitFile.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);
        if (activationAttachment == null) {
            response.setStatus(415);
            response.getWriter().write("DD Form hasn't been generated yet");
            response.getWriter().flush();
            response.getWriter().close();
            return;
        }

        InputStream fileStream = null;
        try {
            fileStream = getDDFormWithoutSignatureFile(directDebitFile, activationAttachment.getCreationDate());
            createDownloadResponse(response, "DD Form-No Signature.pdf", fileStream);
        } finally {
            StreamsUtil.closeStream(fileStream);
        }
    }

    private InputStream getDDFormWithoutSignatureFile(DirectDebitFile ddf, Date generationDate) {

        InputStream fileStream = null;
        try {
            fileStream = directDebitService.generateDDForm(ddf, generationDate, false);
        } catch (Exception e) {
            e.printStackTrace();
            StreamsUtil.closeStream(fileStream);
        }

        return fileStream;
    }

    @UsedBy(others = UsedBy.Others.New_GPT)
    @JwtSecured
    @EnableSwaggerMethod
    @GetMapping(value = "/getDDFormLinkByApplicationId")
    public ResponseEntity<?> getDDFormLinkByApplicationId(String applicationId) {

        DirectDebitFile ddf = directDebitFileRep.findFirstByApplicationId(applicationId);
        Map<String, String> r = new HashMap<>();
        if (ddf == null) {
            r.put("error", "DD Application Reference Number Not Found");
            r.put("downloadLink", "");
            return ResponseEntity.ok(r);
        }

        Attachment directDebitAttachmentFile = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION_WITHOUT_SIGNATURE);
        if (directDebitAttachmentFile == null) {
            Attachment activationAttachment = ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION);

            if (activationAttachment == null) {
                r.put("error", "DD Form hasn't been generated yet");
                r.put("downloadLink", "");
                return ResponseEntity.ok(r);
            }

            InputStream fileStream = getDDFormWithoutSignatureFile(ddf, activationAttachment.getCreationDate());

            directDebitAttachmentFile = Storage.storeTemporary("Direct Debit Activation.pdf", fileStream,
                    DirectDebitFile.FILE_TAG_DD_ACTIVATION_WITHOUT_SIGNATURE, true);
            ddf.addAttachment(directDebitAttachmentFile);
            directDebitFileRep.save(ddf);
        }

        String backendBaseUrl = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BACKEND_BASE_URL);
        String downloadLink = backendBaseUrl + "/public/download/" + directDebitAttachmentFile.getUuid();

        r.put("error", "");
        r.put("downloadLink", downloadLink);
        return ResponseEntity.ok(r);
    }

    // Jirra ACC-2986
    @NoPermission
    @RequestMapping(value = "/getBankNames", method = RequestMethod.GET)
    public ResponseEntity getBankNamesAPI() {
        return ResponseEntity.ok(getBankNames());
    }

    public List getBankNames() {
        return Setup.getRepository(PicklistRepository.class).findByCode("BankName").getItemsWithTag("IBAN_BANK_NAME")
                .stream().map(item -> item.getTagValue("IBAN_BANK_NAME").getValue())
                .collect(Collectors.toList());
    }

    @PreAuthorize("hasPermission('directdebitfile','/dataMigrationACC4493SecondStep')")
    @RequestMapping(value = "dataMigrationACC4493SecondStep", method = RequestMethod.GET)
    public ResponseEntity dataMigrationACC4493SecondStep(@RequestParam(name = "limit") Integer limit) {

        directDebitSignatureService.dataMigrationACC4493SecondStep(limit);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','/dataMigrationACC4493SecondStepRefresh')")
    @RequestMapping(value = "dataMigrationACC4493SecondStepRefresh", method = RequestMethod.GET)
    public ResponseEntity dataMigrationACC4493SecondStepRefresh(@RequestParam(name = "limit") Integer limit) {

        directDebitSignatureService.dataMigrationACC4493SecondStepRefresh(limit);
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','/dataMigrationACC4493FirstStep')")
    @RequestMapping(value = "dataMigrationACC4493FirstStep", method = RequestMethod.GET)
    public ResponseEntity dataMigrationACC4493FirstStep(
            @RequestParam(name = "limit") Integer limit,
            @RequestParam(name = "startId", required = false, defaultValue = "0") Long startId) {

        List<Long> clientIds = ddSignatureTempRepository.getNotCorrectedCptFirstStep(startId, 30);
        logger.log(Level.INFO, "dataMigrationACC4493FirstStep clients size {0}", clientIds.size());

        int i = 0;
        while (!clientIds.isEmpty() && i < limit) {
            i++;
            Long lastClientId = Iterables.getLast(clientIds);

            try {
                logger.log(Level.INFO, "dataMigrationACC4493FS create background task lastClientId id {0}", lastClientId);
                backgroundTaskService.create(new BackgroundTask.builder(
                        "dataMigrationACC4493FirstStep_" + lastClientId,
                        "accounting",
                        "directDebitFileController",
                        "dataMigrationACC4493ProcessFS")
                        .withRelatedEntity( "ContractPaymentTerm", null)
                        .withParameters( new Class<?>[] {List.class},
                                new Object[] { clientIds.stream().map(x -> x.toString()).collect(Collectors.toList()) } )
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
            clientIds = ddSignatureTempRepository.getNotCorrectedCptFirstStep(lastClientId, 30);
            logger.log(Level.INFO, "dataMigrationACC4493FirstStep clientIds size {0}", clientIds.size());
        }
        logger.log(Level.INFO, "dataMigrationACC4493FirstStepFs end create background tasks");
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    public void dataMigrationACC4493ProcessFS(List<String> clientIds) {
        logger.log(Level.INFO, "clientIds ids {0}", String.join(",", clientIds));

        clientIds.forEach(x -> {
            try {
                dataMigrationACC4493SingleProcessFS(Long.parseLong(x));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

    }

    @Transactional
    public void dataMigrationACC4493SingleProcessFS(Long clientId) {
        logger.log(Level.INFO, "clientId: {0}", clientId);

        if (ddSignatureTempRepository.existsByClient_Id(clientId))
            return;

        List<Map> ddfList = ddSignatureTempRepository.getNotCorrectedCPTSignAttachments(clientId);
        logger.log(Level.INFO, "ddfList size: {0}", ddfList.size());
        Map<Long, Long> activeCptList = new HashMap<>();
        Table<String, String, Long> base64Signs = HashBasedTable.create();
        ddfList.forEach(map -> {
            try {
                Long contractId = (Long) map.get("contractId");
                if (!activeCptList.containsKey(contractId)) {
                    ContractPaymentTerm activeCpt = Setup.getRepository(ContractPaymentTermRepository.class)
                            .findFirstByContract_IdOrderByIdDesc(contractId);
                    activeCptList.put(contractId, activeCpt.getId());
                }

                Long directDebitFileId = (Long) map.get("directDebitFileId");
                Attachment oldAttachment = (Attachment) map.get("oldAttachment");
                String eid = (String) map.get("eid");
                logger.log(Level.INFO, "directDebitFileId: {0}; oldAttachment id: {1}; eid: {2}",
                        new Object[]{directDebitFileId, oldAttachment.getId(), eid});

                InputStream stream = Storage.getStream(oldAttachment);
                byte[] bytes;

                    bytes = IOUtils.toByteArray(stream);
                    String encoded = Base64.getEncoder().encodeToString(bytes);

                DDSignatureTemp ddSignatureTemp = new DDSignatureTemp();
                ddSignatureTemp.setDirectDebitFileId(directDebitFileId);
                ddSignatureTemp.setEid(eid);
                ddSignatureTemp.setOldAttachmentId(oldAttachment.getId());
                ddSignatureTemp.setCptId(activeCptList.get(contractId));
                if (!base64Signs.contains(encoded, eid + "")) {
                    Attachment newSignatureAttachment = Storage.storeTemporary("Direct Debit Signature.png",
                            new ByteArrayInputStream(bytes), DirectDebitFile.FILE_TAG_DD_SIGNATURE, true);
                    base64Signs.put(encoded, eid + "", newSignatureAttachment.getId());
                    logger.log(Level.INFO, "newSignatureAttachment id: {0}", newSignatureAttachment.getId());
                }

                ddSignatureTemp.setNewAttachmentId(base64Signs.get(encoded, eid + ""));
                ddSignatureTempRepository.save(ddSignatureTemp);

                StreamsUtil.closeStream(stream);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });
    }

    @PreAuthorize("hasPermission('directdebitfile','/dataMigrationACC4493FirstStepRefresh')")
    @RequestMapping(value = "dataMigrationACC4493FirstStepRefresh", method = RequestMethod.GET)
    public ResponseEntity dataMigrationACC4493FirstStepRefresh(
        @RequestParam(name = "limit") Integer limit,
        @RequestParam(name = "startId", required = false, defaultValue = "0") Long startId) {

        List<Long> ddfIds = ddSignatureTempRepository.getNewSignaturesNotHandled(startId,30);
        logger.log(Level.INFO, "dataMigrationACC4493FirstStepRefresh ddf size {0}", ddfIds.size());

        int i = 0;
        while (!ddfIds.isEmpty() && i < limit) {
            i++;
            Long lastDdfId = Iterables.getLast(ddfIds);

            try {
                logger.log(Level.INFO, "dataMigrationACC4493FirstStepRefresh create background task lastDdfId id {0}", lastDdfId);
                backgroundTaskService.create(new BackgroundTask.builder(
                    "dataMigrationACC4493FirstStepRefreshWithEmail" + lastDdfId,
                    "accounting",
                    "directDebitFileController",
                    "dataMigrationACC4493FirstStepRefreshWithEmail")
                    .withRelatedEntity( "ContractPaymentTerm", null)
                    .withParameters( new Class<?>[] {List.class},
                        new Object[] { ddfIds.stream().map(x -> x.toString()).collect(Collectors.toList()) } )
                    .withQueue(BackgroundTaskQueues.SequentialQueue)
                    .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
            ddfIds = ddSignatureTempRepository.getNewSignaturesNotHandled(lastDdfId, 30);
            logger.log(Level.INFO, "dataMigrationACC4493FirstStepRefresh ddf size {0}", ddfIds.size());
        }
        logger.log(Level.INFO, "dataMigrationACC4493FirstStepRefresh end create background tasks");
        return new ResponseEntity("Done", HttpStatus.OK);
    }

    public void dataMigrationACC4493FirstStepRefreshWithEmail(List<String> ddfIds) {
        logger.log(Level.INFO, "ddf ids {0}", String.join(",", ddfIds));

        dataMigrationACC4493FirstStepRefresh(ddfIds);
    }

    @Transactional
    public void dataMigrationACC4493FirstStepRefresh(List<String> ddfIds) {
        logger.log(Level.INFO, "ddf size {0}", ddfIds.size());

        Map<Long, Long> activeCptList = new HashMap<>();
        Table<String, String, Long> base64Signs = HashBasedTable.create();
        ddfIds.forEach(ddfIdStr -> {
            try {
                long ddfId = Long.parseLong(ddfIdStr);
                logger.log(Level.INFO, "ddf size {0}", ddfIds.size());

                if (ddSignatureTempRepository.existsByDdf_Id(ddfId))
                    return;

                Map ddf= ddSignatureTempRepository.getNewDdfSignAttachments(ddfId);

                Long contractId = (Long) ddf.get("contractId");
                if (!activeCptList.containsKey(contractId)) {
                    ContractPaymentTerm activeCpt = Setup.getRepository(ContractPaymentTermRepository.class)
                        .findFirstByContract_IdOrderByIdDesc(contractId);
                    activeCptList.put(contractId, activeCpt.getId());
                }

                Long directDebitFileId = (Long) ddf.get("directDebitFileId");
                Attachment oldAttachment = (Attachment) ddf.get("oldAttachment");
                String eid = (String) ddf.get("eid");
                logger.log(Level.INFO, "directDebitFileId: {0}; oldAttachment id: {1}; eid: {2}",
                    new Object[]{directDebitFileId, oldAttachment.getId(), eid});

                InputStream stream = Storage.getStream(oldAttachment);
                byte[] bytes;

                bytes = IOUtils.toByteArray(stream);

                String encoded = Base64.getEncoder().encodeToString(bytes);

                DDSignatureTemp ddSignatureTemp = new DDSignatureTemp();
                ddSignatureTemp.setDirectDebitFileId(directDebitFileId);
                ddSignatureTemp.setEid(eid);
                ddSignatureTemp.setOldAttachmentId(oldAttachment.getId());
                ddSignatureTemp.setCptId(activeCptList.get(contractId));
                if (!base64Signs.contains(encoded, eid + "")) {
                    Attachment newSignatureAttachment = Storage.storeTemporary("Direct Debit Signature.png",
                        new ByteArrayInputStream(bytes), DirectDebitFile.FILE_TAG_DD_SIGNATURE, true, true);
                    base64Signs.put(encoded, eid + "", newSignatureAttachment.getId());
                    logger.log(Level.INFO, "newSignatureAttachment id: {0}", newSignatureAttachment.getId());
                }

                ddSignatureTemp.setNewAttachmentId(base64Signs.get(encoded, eid + ""));
                ddSignatureTempRepository.save(ddSignatureTemp);

                StreamsUtil.closeStream(stream);

            } catch (Exception ex) {
                ex.printStackTrace();
            }
        });
    }

    @PreAuthorize("hasPermission('directdebitfile','/dataMigrationACC5196CanBeUsedManually')")
    @RequestMapping(value = "dataMigrationACC5196CanBeUsedManually", method = RequestMethod.GET)
    public ResponseEntity<?> dataMigrationACC5196CanBeUsedManually() {
        directDebitSignatureService.dataMigrationACC5196CanBeUsedManually();
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('directdebitfile','advancesearch')")
    @GetMapping(value = "/advancesearch2New/page")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.client.name",
            label = "Client Name",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.client.mobileNumber",
            label = "Client Mobile",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.contract.id",
            label = "Contract ID",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "directDebit.contractPaymentTerm.bankName",
            label = "Bank Name",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "applicationId",
            label = "DD ID",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "rejectionReason",
            label = "Rejection Reason",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "ddStatus",
            label = "Status",
            entity = DirectDebitFile.class,
            valuesApi = "/accounting/directDebit/getalldirectdebitstatus",
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "startDate",
            label = "Start Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "creationDate",
            label = "Signing Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "expiryDate",
            label = "Expiry Date",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "notes",
            label = "Notes",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    //Jirra ACC-762
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    @Searchable(fieldName = "directDebit.additionalDiscount",
            label = "DD Discount Amount",
            entity = DirectDebitFile.class,
            apiKey = "directdebitfiles_management_new")
    public ResponseEntity<?> advanceSearch2Acc7306(
            Pageable pageable) {
        return ResponseEntity.ok(Setup.getApplicationContext()
                .getBean(QueryService.class)
                .directDebitFileSearch(pageable));
    }
}
