package com.magnamedia.controller;


import com.magnamedia.entity.AdhocVariableBucket;
import com.magnamedia.entity.AdhocVariableNode;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.repository.AdhocVariableBucketRepository;
import com.magnamedia.repository.AdhocVariableNodeRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.BasePLVariableRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@RequestMapping("/adhocvariablebuckets")
@RestController
public class AdhocVariableBucketController extends BasePLVariableBucketController<AdhocVariableBucket> {
    @Autowired
    private AdhocVariableBucketRepository adhocVariableBucketRepository;

    @Autowired
    private AdhocVariableNodeRepository adhocVariableNodeRepository;

    @Override
    public BasePLVariableRepository getPlVariableRepository() {
        return adhocVariableNodeRepository;
    }

    @Override
    public BasePLVariableBucketRepository<AdhocVariableBucket> getRepository() {
        return adhocVariableBucketRepository;
    }

    @Override
    public ResponseEntity<?> createEntity(@RequestBody AdhocVariableBucket entity) {
        // Validate required fields
        if (entity.getWieght() == null) {
            return new ResponseEntity<>("Bucket weight should not be empty.", HttpStatus.BAD_REQUEST);
        }

        if (entity.getWieght() < -1 || entity.getWieght() > +1) {
            return new ResponseEntity<>("Bucket weight should be between -1 and +1.", HttpStatus.BAD_REQUEST);
        }

        // Check if PLVariable is provided in the request
        BasePLVariableNode providedPLVariable = entity.getpLVariable();
        if (providedPLVariable == null || providedPLVariable.getId() == null) {
            return new ResponseEntity<>("Variable should not be empty.", HttpStatus.BAD_REQUEST);
        }

        // Fetch the full PLVariable entity from database
        AdhocVariableNode pLVariable = adhocVariableNodeRepository.findOne(providedPLVariable.getId());
        if (pLVariable == null) {
            return new ResponseEntity<>("PLVariable with ID " + providedPLVariable.getId() + " not found.", HttpStatus.BAD_REQUEST);
        }

        // Set the relationship properly
        entity.setpLVariable(pLVariable);

        // Call parent's createEntity method
        return super.createEntity(entity);
    }
}