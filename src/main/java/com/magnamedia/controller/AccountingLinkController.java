package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.AccountingLink;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.extra.Utils;
import com.magnamedia.repository.AccountingLinkRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.ContractRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/accountingLink")
public class AccountingLinkController extends BaseRepositoryController<AccountingLink> {

    @Autowired
    private AccountingLinkRepository accountingLinkRepository;

    @Override
    public BaseRepositoryParent<AccountingLink> getRepository() {
        return accountingLinkRepository;
    }

//    @PreAuthorize("hasPermission('accountingLink','getLinkStatus')")
//    @GetMapping("/getLinkStatus/{uuid}")
//    public ResponseEntity<?> getLinkStatus(@PathVariable("uuid") String linkUuid) {
//
//        return ResponseEntity.ok(AccountingLinkService.getLinkStatus(linkUuid));
//    }

    @NoPermission
    @GetMapping("/getLastSignDdLink")
    public Map<String, String> getLastSignDdLink(String contractUuid){
        Map<String, String> r = new HashMap<>();
        Contract c = Setup.getRepository(ContractRepository.class).findByUuid(contractUuid);
        if (c == null) return r;

        try {
            AccountingLink a = accountingLinkRepository.findTopByContractIdAndTypeOrderByCreationDateDesc(c.getId(), AccountingLink.AccountingLinkType.SIGN_DD_WEB_PAGE);
            if (a == null) return r;

            String url = a.getOriginalLink();
            int contractUUidIndex = url.indexOf(contractUuid);
            if (contractUUidIndex != -1) {
                url = url.substring(contractUUidIndex);
            }

            // Split the URL into parts based on '&'
            String[] parts = url.split("&");

            // Iterate over the parts and populate the map
            for (String part : parts) {
                String[] keyValue = part.split("=");
                if (keyValue.length == 2) {
                    r.put(keyValue[0], keyValue[1]);
                }
            }
        } catch (Exception e) {
           e.printStackTrace();
           return new HashMap<>();
        }
        return r;
    }

    @NoPermission
    @GetMapping("/getLatestGeneratedSigningLink/{todoUuid}")
    public ResponseEntity<?> getLatestGeneratedSigningLink(@PathVariable String todoUuid){
        try {
            ContractPaymentConfirmationToDo todo = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class).findByUuid(todoUuid);
            AccountingLink a = accountingLinkRepository.findTopByContractIdAndTypeOrderByCreationDateDesc(todo.getContractPaymentTerm().getContract().getId(),
                    AccountingLink.AccountingLinkType.SIGN_DD_WEB_PAGE);
            if (a != null) ResponseEntity.ok(a.getShortenedLink());

            Map<String, Object> signDDMap = new HashMap<>();
            signDDMap.put("cpt", todo.getContractPaymentTerm().isActive() ?
                    todo.getContractPaymentTerm() : todo.getContractPaymentTerm().getContract().getActiveContractPaymentTerm());
            signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
                put("sentFrom", "RECURRING_SCREEN");
            }});

            String signLink = Setup.getApplicationContext()
                    .getBean(Utils.class)
                    .getSingDDLink(signDDMap);

            return ResponseEntity.ok(signLink);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("This action is currently unavailable");
        }
    }
}