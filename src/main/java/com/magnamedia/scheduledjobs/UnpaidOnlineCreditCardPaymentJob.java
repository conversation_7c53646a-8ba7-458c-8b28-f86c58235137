package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.FlowProcessorService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import com.magnamedia.service.UnpaidOnlineCreditCardPaymentService;
import com.magnamedia.service.UnpaidOnlineCreditCardPaymentService;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UnpaidOnlineCreditCardPaymentJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(UnpaidOnlineCreditCardPaymentJob.class.getName());

    private FlowProcessorService flowProcessorService;
    private UnpaidOnlineCreditCardPaymentService onlineCardPaymentService;
    private FlowEventConfig flowEventConfig;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");

        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        onlineCardPaymentService = Setup.getApplicationContext().getBean(UnpaidOnlineCreditCardPaymentService.class);
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.ONLINE_CREDIT_CARD_PAYMENT_REMINDERS);
        if (flowEventConfig == null) return;
        logger.log(Level.INFO, "flowEventConfig id: {0}", flowEventConfig.getId());

        sendUnpaidOnlineCreditCardPaymentMessages();
        logger.log(Level.INFO, "Job finished");

    }

    public void sendUnpaidOnlineCreditCardPaymentMessages() {
        logger.log(Level.INFO, "sendUnpaidOnlineCreditCardPaymentMessages runs");

        List <FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);


        for (FlowProcessorEntity flowProcessorEntity : flowProcessorEntities) {
            try {
                logger.info("flow id: " + flowProcessorEntity.getId() +
                        "; reminders: " + flowProcessorEntity.getReminders());

                if (new LocalDate(flowProcessorEntity.getCreationDate()).isBefore(new LocalDate().minusDays(7))) {
                    flowProcessorEntity.setStopped(true);
                    flowProcessorEntityRepository.save(flowProcessorEntity);
                    logger.info("This is an outdated flow and will be Stopped -> exiting.");
                    continue;
                }

                if (onlineCardPaymentService.shouldDelayTerminationMessageBeforePaidEndDate(flowProcessorEntity)) {
                    logger.info("Flow Stopped Or Delay sending the termination message of the Online Reminder flow required -> exiting");
                    continue;
                }

                flowProcessorService.processFlowSubEventConfig(flowProcessorEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}