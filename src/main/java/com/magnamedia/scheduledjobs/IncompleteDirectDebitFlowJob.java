package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.helper.PaginationHelper;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.*;

import java.text.ParseException;
import java.util.Map;
import java.util.logging.Logger;

// ACC-6804
public class IncompleteDirectDebitFlowJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(IncompleteDirectDebitFlowJob.class.getName());

    private final FlowProcessorService flowProcessorService;
    private final IncompleteDirectDebitService incompleteDirectDebitService;
    private final FlowProcessorEntityRepository flowProcessorEntityRepository;

    public IncompleteDirectDebitFlowJob() {
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        incompleteDirectDebitService = Setup.getApplicationContext().getBean(IncompleteDirectDebitService.class);
    }

    @Override
    public void run(Map<String, ?> map) {

        processFlowAndSendMessages();
    }

    public void processFlowAndSendMessages() {
        logger.info("Start");
        PaginationHelper.processPaginated(
                IncompleteDirectDebitFlowJob.class.getSimpleName(),
                (lastId, pageRequest) -> flowProcessorEntityRepository
                        .findRunningIncompleteFlowMissingBankInfo(
                                lastId, FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_BANK_INFO, pageRequest),
                this::processFlowAndSendMessage);
        logger.info("End");
    }

    public void processFlowAndSendMessage(FlowProcessorEntity entity) throws ParseException {
        logger.info("cpt id:" + entity.getContractPaymentTerm().getId() + "; flow id: " + entity.getId());

        incompleteDirectDebitService.updateToPaidFlow(entity);
        if (incompleteDirectDebitService.shouldDelayMessage(entity)) {
            logger.info("Flow Stopped Or Delay sending the termination message -> exiting");
            return;
        }

        flowProcessorService.processFlowSubEventConfig(entity);
    }
}
