package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.helper.PaginationHelper;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.service.QueryService;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.FlowEventConfigRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.ClientPayingViaCreditCardService;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import com.magnamedia.service.FlowProcessorService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

//ACC-4715
public class ClientPayingViaCreditCardJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(ClientPayingViaCreditCardJob.class.getName());
    private FlowEventConfig flowEventConfig;
    private FlowProcessorService flowProcessorService;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    private ContractPaymentTermRepository contractPaymentTermRepository;
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;
    private PaymentService paymentService;

    Integer sendDdSigningOfferDay = 1;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Start job");
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        clientPayingViaCreditCardService = Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class);
        contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        contractPaymentTermRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        maidVisaFailedMedicalCheckService = Setup.getApplicationContext().getBean(MaidVisaFailedMedicalCheckService.class);
        paymentService = Setup.getApplicationContext().getBean(PaymentService.class);

        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card);

        if (flowEventConfig == null) return;
        logger.log(Level.INFO, "flowEventConfig id: {0}", flowEventConfig.getId());

        if (flowEventConfig.hasTag("day_of_send_dd_signing_offer")) {
            sendDdSigningOfferDay = Integer.parseInt(
                    flowEventConfig.getTagValue("day_of_send_dd_signing_offer").getValue());
        }

        PaginationHelper.processPaginated(
                "processScheduledTokenDeletions",
                (lastId, pageRequest) -> contractPaymentTermRepository.findScheduledTokenDeletionsForToday(
                        lastId, new LocalDate().dayOfMonth().withMinimumValue().toDate(), pageRequest),
                this::processScheduledTokenDeletions);

        clientPayingViaCreditCardService.removeRecurringFlagAndCreateToDos();

        clientPayingViaCreditCardService.removePaymentsWhenErpParameterFalse();

        clientPayingViaCreditCardService.removePaymentsUponProviderChange();

        clientPayingViaCreditCardService.removeTokenFromInactiveCpt();

        createReminderFlow();

        clientPayingViaCreditCardService.startAutomaticCollectionRecurringCreditCardPayment();

        clientPayingViaCreditCardService.handleFailedErpCaptureRecurringPayment();

        sendMessages();

        logger.log(Level.SEVERE, "End job");
    }

    private void processScheduledTokenDeletions(ContractPaymentTerm cpt) {
        contractPaymentTermServiceNew.deleteCreditCardToken(cpt, false);
    }

    public void sendMessages() {
        logger.info("start");

        List<FlowProcessorEntity> flowProcessorEntities = flowProcessorEntityRepository
                .findByFlowEventConfigAndStoppedFalseAndCompletedFalse(flowEventConfig);
        logger.log(Level.SEVERE, "flowProcessorEntityRepository size: {0}", flowProcessorEntities.size());

        for (FlowProcessorEntity entity : flowProcessorEntities) {

            try {
                logger.info("flow id: " + entity.getId() +
                        "; trial: " + entity.getTrials());

                if (!entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_SIGNING_OFFER) &&
                        !entity.getCurrentSubEvent().getName().equals(FlowSubEventConfig.FlowSubEventName.DD_Rejection) &&
                        clientPayingViaCreditCardService.validateSkippingFlow(entity)) continue;

                switch (entity.getCurrentSubEvent().getName()) {
                    case MONTHLY_REMINDER:
                    case EXPIRED_CARD:
                        // ACC-6564
                        if (new DateTime().isAfter(new DateTime(entity.getContract().getPaidEndDate())) && !entity.isIgnorePaidEndDate()) {
                            entity.setTrials(entity.getCurrentSubEvent().getMaxTrials() - 1);
                            entity.setReminders(entity.getCurrentSubEvent().getMaxReminders());
                        }
                        break;
                    case DD_SIGNING_OFFER: // ACC-7965
                        if (!entity.getContract().isPayingViaCreditCard()) {
                            logger.info("The flag turned to false -> stop the flow");
                            entity.setStopped(true);
                            flowProcessorEntityRepository.save(entity);
                            continue;
                        }
                }

                // ACC-7549
                if (FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER.equals(entity.getCurrentSubEvent().getName()) &&
                        entity.getContract().getScheduledDateOfTermination() != null &&
                        ContractStatus.ACTIVE.equals(entity.getContract().getStatus())) {
                    logger.info("keep it silent while the contract is scheduled for termination");
                    continue;
                }

                flowProcessorService.processFlowSubEventConfig(entity);

            } catch (Exception e) {
                logger.log(Level.SEVERE, "error " + e.getMessage());
                e.printStackTrace();
            }
        }

        logger.info("finish");
    }

    private void createReminderFlow() {
        logger.log(Level.INFO, "start");
        int startMonthlyReminderDay = 8;

        if (flowEventConfig.hasTag("monthly_reminder_paying_cc_start_before_x_days")) {
            startMonthlyReminderDay = Integer.parseInt(
                    flowEventConfig.getTagValue("monthly_reminder_paying_cc_start_before_x_days").getValue());
        }

        Date d = new LocalDate().plusDays(startMonthlyReminderDay).toDate();

        for (ContractPaymentTerm c :
                Setup.getRepository(ContractPaymentTermRepository.class)
                        .findByCptAndFlowProcessEntityAndPayingViaCreditCard(d)) {
            try {
                logger.log(Level.INFO, "cpt id : {0}", c.getId());

                clientPayingViaCreditCardService.startReminderFlow(c,
                        FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                        FlowSubEventConfig.FlowSubEventName.MONTHLY_REMINDER,
                        startMonthlyReminderDay);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        logger.info("finish");
    }
}