package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.helper.PaginationHelper;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.service.*;

import java.text.ParseException;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 04, 2020
 *         Jirra ACC-2024
 */

public class IncompleteDDInfoSJ implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(IncompleteDDInfoSJ.class.getName());

    private final FlowProcessorEntityRepository flowProcessorEntityRepository;
    private final IncompleteDirectDebitService incompleteDirectDebitService;
    private final FlowProcessorService flowProcessorService;

    public IncompleteDDInfoSJ() {
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        incompleteDirectDebitService = Setup.getApplicationContext().getBean(IncompleteDirectDebitService.class);
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        logger.info("Start Job");
        processFlowAndSendMessages();
        logger.info("End Job");
    }

    // ACC-9677
    public void processFlowAndSendMessages() {
        logger.info("Start");
        PaginationHelper.processPaginated(
                IncompleteDDInfoSJ.class.getSimpleName(),
                (lastId, pageRequest) -> flowProcessorEntityRepository
                        .findByEventNameAndStoppedFalseAndCompletedFalseAndStatus(
                                lastId, FlowEventConfig.FlowEventName.INCOMPLETE_FLOW_MISSING_DD_INFO,
                                Collections.singletonList(FlowProcessorEntity.FlowProcessorStatus.ACTIVE),
                                pageRequest),
                this::processFlowAndSendMessage);
        logger.info("End");
    }

    public void processFlowAndSendMessage(FlowProcessorEntity entity) throws ParseException {
        incompleteDirectDebitService.updateToPaidFlow(entity);

        if (incompleteDirectDebitService.shouldDelayMessage(entity)) {
            logger.info("Flow Stopped Or Delay sending the termination message -> exiting");
            return;
        }

        flowProcessorService.processFlowSubEventConfig(entity);
    }
}
