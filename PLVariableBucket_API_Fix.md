# PLVariableBucket API Fix

## Problem Description

The PLVariableBucket creation API was throwing the error:
```
"message": "Variable should not be empty."
```

When sending requests like:
```json
{
    "wieght": 1,
    "pLVariable": {
        "id": 6,
        "type": "PLVariableNode"
    },
    "revenue": {
        "id": 4
    }
}
```

### Root Cause

The issue occurred because:

1. **JSON Deserialization Problem**: When <PERSON> deserializes the JSON payload, it doesn't properly set the PLVariable relationship in the entity
2. **Validation Timing**: The `@BeforeInsert` validation in `BasePLVariableBucket.validate()` runs before the relationship is properly established
3. **Null Reference**: `this.getpLVariable()` returns `null` during validation, triggering the error on line 64 of `BasePLVariableBucket.java`

### Code Flow:
1. POST request to `/accounting/plvariablebuckets/create`
2. `BaseRepositoryController.createEntity()` called (from magnamedia-core)
3. Entity saved directly, triggering `@BeforeInsert` validation
4. `BasePLVariableBucket.validate()` checks `this.getpLVariable() == null`
5. Throws `RuntimeException("Variable should not be empty.")`

## Solution

Added custom `createEntity` methods to both:
- `PLVariableBucketController`
- `AdhocVariableBucketController`

These methods:
1. Validate required fields (weight, range)
2. Check that PLVariable is provided in the request
3. Fetch the full PLVariable entity from database if only ID is provided
4. Set the relationship before calling `super.createEntity()`

## API Usage

### Correct Request Format

When creating a PLVariableBucket, include the PLVariable reference in your JSON:

```json
{
  "wieght": 0.5,
  "pLVariable": {
    "id": 123
  },
  "expense": {
    "id": 456
  }
}
```

OR

```json
{
  "wieght": -0.3,
  "pLVariable": {
    "id": 123
  },
  "revenue": {
    "id": 789
  }
}
```

### Required Fields:
- `wieght`: Double between -1 and +1
- `pLVariable`: Object with valid `id` of existing PLVariableNode
- Either `expense` OR `revenue` (not both)

### Validation Rules:
- Weight must be between -1 and +1 (inclusive)
- PLVariable must exist in database
- PLVariable cannot be null

## Files Modified

1. `src/main/java/com/magnamedia/controller/PLVariableBucketController.java`
   - Added `createEntity` method override
   - Added validation and relationship setup

2. `src/main/java/com/magnamedia/controller/AdhocVariableBucketController.java`
   - Added `createEntity` method override
   - Added validation and relationship setup

## Testing

To test the fix:

1. **Valid Request:**
```bash
POST /accounting/plvariablebuckets/create
Content-Type: application/json

{
  "wieght": 0.5,
  "pLVariable": {"id": 1},
  "expense": {"id": 1}
}
```

2. **Invalid Weight:**
```bash
POST /accounting/plvariablebuckets/create
Content-Type: application/json

{
  "wieght": 2.0,
  "pLVariable": {"id": 1},
  "expense": {"id": 1}
}
```
Expected: `"Bucket weight should be between -1 and +1."`

3. **Missing PLVariable:**
```bash
POST /accounting/plvariablebuckets/create
Content-Type: application/json

{
  "wieght": 0.5,
  "expense": {"id": 1}
}
```
Expected: `"Variable should not be empty."`

4. **Non-existent PLVariable:**
```bash
POST /accounting/plvariablebuckets/create
Content-Type: application/json

{
  "wieght": 0.5,
  "pLVariable": {"id": 99999},
  "expense": {"id": 1}
}
```
Expected: `"PLVariable with ID 99999 not found."`

## Notes

- The fix applies to both regular PLVariableBucket and AdhocVariableBucket endpoints
- The `type` field in the pLVariable object is optional and will be ignored
- Only the `id` field is required in the pLVariable object
- The API will now provide more specific error messages for different validation failures
